{"version": 3, "sources": ["../src/cli.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport { homedir } from \"node:os\"\nimport { join, resolve } from \"node:path\"\nimport { cancel, intro, isCancel, multiselect, outro, spinner } from \"@clack/prompts\"\nimport { sum } from \"es-toolkit\"\nimport meow from \"meow\"\nimport { cleanupRegistry } from \"./lib/cleanup-registry.js\"\nimport { DB } from \"./lib/database.js\"\nimport { explorer } from \"./lib/explorer.js\"\nimport type { FileGroup, Language } from \"./lib/types.js\"\n\nconst entrypoint = meow(\n  `\n\tUsage\n\t  $ context42 [options]\n\n\tOptions\n\t  -i, --input       Input directory to analyze (default: current directory)\n\t  -o, --output      Output directory for style guides (default: ./context42)\n\t  -m, --model       Gemini model to use (default: gemini-2.5-pro)\n\t  -c, --concurrency Number of concurrent operations (default: 4)\n\t  -r, --run         Resume from a previous run ID\n\t  -d, --debug       Debug mode - shows run ID on error and preserves it\n\t  -h, --help        Show help\n\n\tExamples\n\t  $ context42\n\t  $ context42 -i src/\n\t  $ context42 -o .cursor/rules/\n\t  $ context42 -m gemini-2.0-flash-exp\n\t  $ context42 -c 10\n\t  $ context42 --run abc123-def456\n\t  $ context42 --debug\n\t  $ GEMINI_API_KEY=\"your-key\" context42\n\n\tNote: Requires GEMINI_API_KEY environment variable to be set.\n`,\n  {\n    importMeta: import.meta,\n    flags: {\n      input: {\n        type: \"string\",\n        shortFlag: \"i\",\n        default: \".\",\n      },\n      output: {\n        type: \"string\",\n        shortFlag: \"o\",\n        default: \"./context42/\",\n      },\n      model: {\n        type: \"string\",\n        shortFlag: \"m\",\n        default: \"gemini-2.5-flash\",\n      },\n      concurrency: {\n        type: \"number\",\n        shortFlag: \"c\",\n        default: 4,\n      },\n      run: {\n        type: \"string\",\n        shortFlag: \"r\",\n      },\n      debug: {\n        type: \"boolean\",\n        shortFlag: \"d\",\n        default: false,\n      },\n    },\n  },\n)\n\nexport interface CLIResult {\n  fileGroups: Map<Language, FileGroup[]>\n  inputDir: string\n  outputDir: string\n  model: string\n  concurrency: number\n  total: number\n  database: DB\n  debug: boolean\n}\n\nexport const cli = async (): Promise<CLIResult | null> => {\n  if (!process.env.GEMINI_API_KEY) {\n    cancel(\"Error: GEMINI_API_KEY environment variable is not set.\")\n    process.exit(1)\n  }\n\n  intro(\"\\x1b[1mThe best code style guide is the one your team already follows.\\x1b[0m This tool discovers it.\")\n  const s = spinner({ indicator: \"timer\" })\n  s.start(\"Exploring subdirectories...\")\n  const allFileGroups = await explorer({ directory: resolve(entrypoint.flags.input) })\n  const allFileCount = sum(\n    Array.from(allFileGroups.values()).map(groups => sum(groups.map(group => group.files.length))),\n  )\n  s.stop(`Found ${allFileCount} files`)\n\n  const languages = await multiselect({\n    message: \"What shall I ponder?\",\n    options: Array.from(allFileGroups.keys())\n      .map(ext => ({ label: `**/*.${ext}`, value: ext as string }))\n      .toSorted((a, b) => a.value.localeCompare(b.value)),\n  })\n\n  if (isCancel(languages)) {\n    cancel(\"No languages selected\")\n    return null\n  }\n\n  let selectedFileCount = 0\n  // Filter to only selected languages\n  const selectedFileGroups = new Map<Language, FileGroup[]>()\n  for (const lang of languages) {\n    const groups = allFileGroups.get(lang as Language)\n    if (groups) {\n      selectedFileGroups.set(lang as Language, groups)\n      selectedFileCount += sum(groups.map(group => group.files.length))\n    }\n  }\n\n  // Initialize database\n  const database = new DB(join(homedir(), \".context42\", \"data.db\"), entrypoint.flags.run)\n  outro(`Beginning run ${database.runId}`)\n\n  // Set up signal handlers for proper cleanup\n  const cleanup = async () => {\n    await cleanupRegistry.cleanup()\n    database.close()\n    process.exit(0)\n  }\n\n  process.on(\"SIGINT\", cleanup)\n  process.on(\"SIGTERM\", cleanup)\n\n  return {\n    fileGroups: selectedFileGroups,\n    inputDir: resolve(entrypoint.flags.input),\n    outputDir: resolve(entrypoint.flags.output),\n    model: entrypoint.flags.model,\n    concurrency: Math.min(entrypoint.flags.concurrency, selectedFileCount),\n    total: selectedFileCount,\n    database,\n    debug: entrypoint.flags.debug,\n  }\n}\n"], "mappings": ";AACA,SAAS,eAAe;AACxB,SAAS,MAAM,eAAe;AAC9B,SAAS,QAAQ,OAAO,UAAU,aAAa,OAAO,eAAe;AACrE,SAAS,WAAW;AACpB,OAAO,UAAU;AACjB,SAAS,uBAAuB;AAChC,SAAS,UAAU;AACnB,SAAS,gBAAgB;AAGzB,MAAM,aAAa;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBA;AAAA,IACE,YAAY;AAAA,IACZ,OAAO;AAAA,MACL,OAAO;AAAA,QACL,MAAM;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,MACX;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,MAAM;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,MACX;AAAA,MACA,aAAa;AAAA,QACX,MAAM;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,MAAM;AAAA,QACN,WAAW;AAAA,MACb;AAAA,MACA,OAAO;AAAA,QACL,MAAM;AAAA,QACN,WAAW;AAAA,QACX,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACF;AAaO,MAAM,MAAM,YAAuC;AACxD,MAAI,CAAC,QAAQ,IAAI,gBAAgB;AAC/B,WAAO,wDAAwD;AAC/D,YAAQ,KAAK,CAAC;AAAA,EAChB;AAEA,QAAM,uGAAuG;AAC7G,QAAM,IAAI,QAAQ,EAAE,WAAW,QAAQ,CAAC;AACxC,IAAE,MAAM,6BAA6B;AACrC,QAAM,gBAAgB,MAAM,SAAS,EAAE,WAAW,QAAQ,WAAW,MAAM,KAAK,EAAE,CAAC;AACnF,QAAM,eAAe;AAAA,IACnB,MAAM,KAAK,cAAc,OAAO,CAAC,EAAE,IAAI,YAAU,IAAI,OAAO,IAAI,WAAS,MAAM,MAAM,MAAM,CAAC,CAAC;AAAA,EAC/F;AACA,IAAE,KAAK,SAAS,YAAY,QAAQ;AAEpC,QAAM,YAAY,MAAM,YAAY;AAAA,IAClC,SAAS;AAAA,IACT,SAAS,MAAM,KAAK,cAAc,KAAK,CAAC,EACrC,IAAI,UAAQ,EAAE,OAAO,QAAQ,GAAG,IAAI,OAAO,IAAc,EAAE,EAC3D,SAAS,CAAC,GAAG,MAAM,EAAE,MAAM,cAAc,EAAE,KAAK,CAAC;AAAA,EACtD,CAAC;AAED,MAAI,SAAS,SAAS,GAAG;AACvB,WAAO,uBAAuB;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,oBAAoB;AAExB,QAAM,qBAAqB,oBAAI,IAA2B;AAC1D,aAAW,QAAQ,WAAW;AAC5B,UAAM,SAAS,cAAc,IAAI,IAAgB;AACjD,QAAI,QAAQ;AACV,yBAAmB,IAAI,MAAkB,MAAM;AAC/C,2BAAqB,IAAI,OAAO,IAAI,WAAS,MAAM,MAAM,MAAM,CAAC;AAAA,IAClE;AAAA,EACF;AAGA,QAAM,WAAW,IAAI,GAAG,KAAK,QAAQ,GAAG,cAAc,SAAS,GAAG,WAAW,MAAM,GAAG;AACtF,QAAM,iBAAiB,SAAS,KAAK,EAAE;AAGvC,QAAM,UAAU,YAAY;AAC1B,UAAM,gBAAgB,QAAQ;AAC9B,aAAS,MAAM;AACf,YAAQ,KAAK,CAAC;AAAA,EAChB;AAEA,UAAQ,GAAG,UAAU,OAAO;AAC5B,UAAQ,GAAG,WAAW,OAAO;AAE7B,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,UAAU,QAAQ,WAAW,MAAM,KAAK;AAAA,IACxC,WAAW,QAAQ,WAAW,MAAM,MAAM;AAAA,IAC1C,OAAO,WAAW,MAAM;AAAA,IACxB,aAAa,KAAK,IAAI,WAAW,MAAM,aAAa,iBAAiB;AAAA,IACrE,OAAO;AAAA,IACP;AAAA,IACA,OAAO,WAAW,MAAM;AAAA,EAC1B;AACF;", "names": []}