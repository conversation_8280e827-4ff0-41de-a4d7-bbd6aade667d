{"cells": [{"cell_type": "markdown", "id": "79c9ee88-945b-44fa-9245-9d2c615a7c1a", "metadata": {}, "source": ["# Handle NaNs\n", "\n", "By default, WB-X does not skip NaNs in the aggregation. This is to avoid unexpected and possible erroneous NaNs staying undetected. However, sometimes NaNs do appear in the targets (e.g. for radar data).\n", "\n", "The explicit way to handle this is to add a NaN mask. This is an option in all data loaders and will add a coordinate called mask that is True for non-NaN values. Let's take a look at an example."]}, {"cell_type": "code", "execution_count": 1, "id": "474c6adf-a694-40ea-a103-1ac3c614d0b3", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from weatherbenchX import test_utils\n", "from weatherbenchX.data_loaders import xarray_loaders"]}, {"cell_type": "code", "execution_count": 2, "id": "ce36be93-3b85-47e9-bc86-e6d716b9bf97", "metadata": {}, "outputs": [], "source": ["predictions = test_utils.mock_prediction_data()"]}, {"cell_type": "code", "execution_count": 3, "id": "9d49bd05-753e-4b84-b09c-e04954d117bc", "metadata": {}, "outputs": [], "source": ["predictions = predictions.where(predictions.latitude >0, np.nan)"]}, {"cell_type": "code", "execution_count": 4, "id": "4aceaad7-96b3-44d7-bfe9-3c87084d1243", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 44MB\n", "Dimensions:               (prediction_timedelta: 11, time: 366, latitude: 19,\n", "                           longitude: 36, level: 3)\n", "Coordinates:\n", "  * prediction_timedelta  (prediction_timedelta) timedelta64[ns] 88B 0 days ....\n", "  * time                  (time) datetime64[ns] 3kB 2020-01-01 ... 2020-12-31\n", "  * latitude              (latitude) float64 152B -90.0 -80.0 ... 80.0 90.0\n", "  * longitude             (longitude) float64 288B 0.0 10.0 20.0 ... 340.0 350.0\n", "  * level                 (level) int64 24B 500 700 850\n", "Data variables:\n", "    geopotential          (prediction_timedelta, time, latitude, longitude, level) float32 33MB ...\n", "    2m_temperature        (prediction_timedelta, latitude, longitude, time) float32 11MB ...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-8275abf3-c16e-4e83-9768-f9729791c536' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-8275abf3-c16e-4e83-9768-f9729791c536' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>prediction_timedelta</span>: 11</li><li><span class='xr-has-index'>time</span>: 366</li><li><span class='xr-has-index'>latitude</span>: 19</li><li><span class='xr-has-index'>longitude</span>: 36</li><li><span class='xr-has-index'>level</span>: 3</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-d34a1b30-7e24-4809-a975-20855fa319ea' class='xr-section-summary-in' type='checkbox'  checked><label for='section-d34a1b30-7e24-4809-a975-20855fa319ea' class='xr-section-summary' >Coordinates: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>prediction_timedelta</span></div><div class='xr-var-dims'>(prediction_timedelta)</div><div class='xr-var-dtype'>timedelta64[ns]</div><div class='xr-var-preview xr-preview'>0 days 1 days ... 9 days 10 days</div><input id='attrs-683c5da8-6dfd-44f8-9ada-12726968d117' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-683c5da8-6dfd-44f8-9ada-12726968d117' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-72fb2a16-6676-4a7a-adad-b86aa83bf3c0' class='xr-var-data-in' type='checkbox'><label for='data-72fb2a16-6676-4a7a-adad-b86aa83bf3c0' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([              0,  86400000000000, 172800000000000, 259200000000000,\n", "       345600000000000, 432000000000000, 518400000000000, 604800000000000,\n", "       691200000000000, 777600000000000, 864000000000000],\n", "      dtype=&#x27;timedelta64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>time</span></div><div class='xr-var-dims'>(time)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2020-01-01 ... 2020-12-31</div><input id='attrs-7aef4a9c-d3d5-4f5c-8beb-220d2a719da5' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-7aef4a9c-d3d5-4f5c-8beb-220d2a719da5' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-40cc7c5d-64eb-4e24-8d5f-3c256029c61a' class='xr-var-data-in' type='checkbox'><label for='data-40cc7c5d-64eb-4e24-8d5f-3c256029c61a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2020-01-01T00:00:00.000000000&#x27;, &#x27;2020-01-02T00:00:00.000000000&#x27;,\n", "       &#x27;2020-01-03T00:00:00.000000000&#x27;, ..., &#x27;2020-12-29T00:00:00.000000000&#x27;,\n", "       &#x27;2020-12-30T00:00:00.000000000&#x27;, &#x27;2020-12-31T00:00:00.000000000&#x27;],\n", "      dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>latitude</span></div><div class='xr-var-dims'>(latitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>-90.0 -80.0 -70.0 ... 80.0 90.0</div><input id='attrs-d4792bee-4621-4173-8435-89452ca57bb9' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d4792bee-4621-4173-8435-89452ca57bb9' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-bb1049fc-93ae-4ed2-ac7f-1ec49a921483' class='xr-var-data-in' type='checkbox'><label for='data-bb1049fc-93ae-4ed2-ac7f-1ec49a921483' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([-90., -80., -70., -60., -50., -40., -30., -20., -10.,   0.,  10.,  20.,\n", "        30.,  40.,  50.,  60.,  70.,  80.,  90.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>longitude</span></div><div class='xr-var-dims'>(longitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.0 10.0 20.0 ... 330.0 340.0 350.0</div><input id='attrs-0676aa6f-df6a-4ad1-b002-45523b66b45c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0676aa6f-df6a-4ad1-b002-45523b66b45c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-8298ba24-b8fc-496b-9086-d380fc3a22e6' class='xr-var-data-in' type='checkbox'><label for='data-8298ba24-b8fc-496b-9086-d380fc3a22e6' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  0.,  10.,  20.,  30.,  40.,  50.,  60.,  70.,  80.,  90., 100., 110.,\n", "       120., 130., 140., 150., 160., 170., 180., 190., 200., 210., 220., 230.,\n", "       240., 250., 260., 270., 280., 290., 300., 310., 320., 330., 340., 350.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>level</span></div><div class='xr-var-dims'>(level)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>500 700 850</div><input id='attrs-f31b5890-1de4-479e-a142-c0c2abfd0ca7' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-f31b5890-1de4-479e-a142-c0c2abfd0ca7' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-30decafa-fe8a-4ed0-9239-f3e07041a16d' class='xr-var-data-in' type='checkbox'><label for='data-30decafa-fe8a-4ed0-9239-f3e07041a16d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([500, 700, 850])</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-13439364-903c-49ad-9ee0-b939881eb417' class='xr-section-summary-in' type='checkbox'  checked><label for='section-13439364-903c-49ad-9ee0-b939881eb417' class='xr-section-summary' >Data variables: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>geopotential</span></div><div class='xr-var-dims'>(prediction_timedelta, time, latitude, longitude, level)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>nan nan nan nan ... 0.0 0.0 0.0 0.0</div><input id='attrs-47451697-bdab-44ab-aa5b-4db986cecaba' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-47451697-bdab-44ab-aa5b-4db986cecaba' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-828ad8d8-d4b6-4bb3-a31f-bc9fe53c9c8b' class='xr-var-data-in' type='checkbox'><label for='data-828ad8d8-d4b6-4bb3-a31f-bc9fe53c9c8b' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[[[[nan, nan, nan],\n", "          [nan, nan, nan],\n", "          [nan, nan, nan],\n", "          ...,\n", "          [nan, nan, nan],\n", "          [nan, nan, nan],\n", "          [nan, nan, nan]],\n", "\n", "         [[nan, nan, nan],\n", "          [nan, nan, nan],\n", "          [nan, nan, nan],\n", "          ...,\n", "          [nan, nan, nan],\n", "          [nan, nan, nan],\n", "          [nan, nan, nan]],\n", "\n", "         [[nan, nan, nan],\n", "          [nan, nan, nan],\n", "          [nan, nan, nan],\n", "          ...,\n", "...\n", "          ...,\n", "          [ 0.,  0.,  0.],\n", "          [ 0.,  0.,  0.],\n", "          [ 0.,  0.,  0.]],\n", "\n", "         [[ 0.,  0.,  0.],\n", "          [ 0.,  0.,  0.],\n", "          [ 0.,  0.,  0.],\n", "          ...,\n", "          [ 0.,  0.,  0.],\n", "          [ 0.,  0.,  0.],\n", "          [ 0.,  0.,  0.]],\n", "\n", "         [[ 0.,  0.,  0.],\n", "          [ 0.,  0.,  0.],\n", "          [ 0.,  0.,  0.],\n", "          ...,\n", "          [ 0.,  0.,  0.],\n", "          [ 0.,  0.,  0.],\n", "          [ 0.,  0.,  0.]]]]], dtype=float32)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>2m_temperature</span></div><div class='xr-var-dims'>(prediction_timedelta, latitude, longitude, time)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>nan nan nan nan ... 0.0 0.0 0.0 0.0</div><input id='attrs-0fbfe8d8-dd66-4dba-a01f-9af7497acd00' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0fbfe8d8-dd66-4dba-a01f-9af7497acd00' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-8df102f5-db61-4e9c-9744-e25696ab0d5d' class='xr-var-data-in' type='checkbox'><label for='data-8df102f5-db61-4e9c-9744-e25696ab0d5d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[[[nan, nan, nan, ..., nan, nan, nan],\n", "         [nan, nan, nan, ..., nan, nan, nan],\n", "         [nan, nan, nan, ..., nan, nan, nan],\n", "         ...,\n", "         [nan, nan, nan, ..., nan, nan, nan],\n", "         [nan, nan, nan, ..., nan, nan, nan],\n", "         [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "        [[nan, nan, nan, ..., nan, nan, nan],\n", "         [nan, nan, nan, ..., nan, nan, nan],\n", "         [nan, nan, nan, ..., nan, nan, nan],\n", "         ...,\n", "         [nan, nan, nan, ..., nan, nan, nan],\n", "         [nan, nan, nan, ..., nan, nan, nan],\n", "         [nan, nan, nan, ..., nan, nan, nan]],\n", "\n", "        [[nan, nan, nan, ..., nan, nan, nan],\n", "         [nan, nan, nan, ..., nan, nan, nan],\n", "         [nan, nan, nan, ..., nan, nan, nan],\n", "         ...,\n", "...\n", "         ...,\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.],\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.],\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.]],\n", "\n", "        [[ 0.,  0.,  0., ...,  0.,  0.,  0.],\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.],\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.],\n", "         ...,\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.],\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.],\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.]],\n", "\n", "        [[ 0.,  0.,  0., ...,  0.,  0.,  0.],\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.],\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.],\n", "         ...,\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.],\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.],\n", "         [ 0.,  0.,  0., ...,  0.,  0.,  0.]]]], dtype=float32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-4c847785-a20f-4b63-b17e-aac67698f71d' class='xr-section-summary-in' type='checkbox'  ><label for='section-4c847785-a20f-4b63-b17e-aac67698f71d' class='xr-section-summary' >Indexes: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>prediction_timedelta</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-620fef21-5e25-4ef3-9f7b-09cbf337bd0e' class='xr-index-data-in' type='checkbox'/><label for='index-620fef21-5e25-4ef3-9f7b-09cbf337bd0e' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(TimedeltaIndex([ &#x27;0 days&#x27;,  &#x27;1 days&#x27;,  &#x27;2 days&#x27;,  &#x27;3 days&#x27;,  &#x27;4 days&#x27;,\n", "                 &#x27;5 days&#x27;,  &#x27;6 days&#x27;,  &#x27;7 days&#x27;,  &#x27;8 days&#x27;,  &#x27;9 days&#x27;,\n", "                &#x27;10 days&#x27;],\n", "               dtype=&#x27;timedelta64[ns]&#x27;, name=&#x27;prediction_timedelta&#x27;, freq=&#x27;D&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-61ea18a6-38a3-4f01-b2d2-c3668c91878f' class='xr-index-data-in' type='checkbox'/><label for='index-61ea18a6-38a3-4f01-b2d2-c3668c91878f' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2020-01-01&#x27;, &#x27;2020-01-02&#x27;, &#x27;2020-01-03&#x27;, &#x27;2020-01-04&#x27;,\n", "               &#x27;2020-01-05&#x27;, &#x27;2020-01-06&#x27;, &#x27;2020-01-07&#x27;, &#x27;2020-01-08&#x27;,\n", "               &#x27;2020-01-09&#x27;, &#x27;2020-01-10&#x27;,\n", "               ...\n", "               &#x27;2020-12-22&#x27;, &#x27;2020-12-23&#x27;, &#x27;2020-12-24&#x27;, &#x27;2020-12-25&#x27;,\n", "               &#x27;2020-12-26&#x27;, &#x27;2020-12-27&#x27;, &#x27;2020-12-28&#x27;, &#x27;2020-12-29&#x27;,\n", "               &#x27;2020-12-30&#x27;, &#x27;2020-12-31&#x27;],\n", "              dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;time&#x27;, length=366, freq=&#x27;D&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>latitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-5f4aef8f-6298-45fa-b08f-8c7aa7576bcf' class='xr-index-data-in' type='checkbox'/><label for='index-5f4aef8f-6298-45fa-b08f-8c7aa7576bcf' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([-90.0, -80.0, -70.0, -60.0, -50.0, -40.0, -30.0, -20.0, -10.0,   0.0,\n", "        10.0,  20.0,  30.0,  40.0,  50.0,  60.0,  70.0,  80.0,  90.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;latitude&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>longitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-bd579ab6-41d4-4927-81df-b52581a2ada5' class='xr-index-data-in' type='checkbox'/><label for='index-bd579ab6-41d4-4927-81df-b52581a2ada5' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([  0.0,  10.0,  20.0,  30.0,  40.0,  50.0,  60.0,  70.0,  80.0,  90.0,\n", "       100.0, 110.0, 120.0, 130.0, 140.0, 150.0, 160.0, 170.0, 180.0, 190.0,\n", "       200.0, 210.0, 220.0, 230.0, 240.0, 250.0, 260.0, 270.0, 280.0, 290.0,\n", "       300.0, 310.0, 320.0, 330.0, 340.0, 350.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;longitude&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>level</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-1b2197a3-1321-4254-9d86-457dbdc60404' class='xr-index-data-in' type='checkbox'/><label for='index-1b2197a3-1321-4254-9d86-457dbdc60404' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([500, 700, 850], dtype=&#x27;int64&#x27;, name=&#x27;level&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-286a972c-52a1-4259-8474-9c01cec5b947' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-286a972c-52a1-4259-8474-9c01cec5b947' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 44MB\n", "Dimensions:               (prediction_timedelta: 11, time: 366, latitude: 19,\n", "                           longitude: 36, level: 3)\n", "Coordinates:\n", "  * prediction_timedelta  (prediction_timedelta) timedelta64[ns] 88B 0 days ....\n", "  * time                  (time) datetime64[ns] 3kB 2020-01-01 ... 2020-12-31\n", "  * latitude              (latitude) float64 152B -90.0 -80.0 ... 80.0 90.0\n", "  * longitude             (longitude) float64 288B 0.0 10.0 20.0 ... 340.0 350.0\n", "  * level                 (level) int64 24B 500 700 850\n", "Data variables:\n", "    geopotential          (prediction_timedelta, time, latitude, longitude, level) float32 33MB ...\n", "    2m_temperature        (prediction_timedelta, latitude, longitude, time) float32 11MB ..."]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["predictions"]}, {"cell_type": "code", "execution_count": 5, "id": "c7fe8c9b-cbae-4854-9e47-061f28eba4bb", "metadata": {}, "outputs": [], "source": ["data_loader = xarray_loaders.PredictionsFromXarray(\n", "    ds=predictions,\n", "    add_nan_mask=True\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "72b4d20d-9b02-4b9e-8f0a-b3e006662dc1", "metadata": {}, "outputs": [], "source": ["chunk = data_loader.load_chunk(\n", "    np.array(['2020-01-01T00'], dtype='datetime64'),\n", "    np.array([24], dtype='timedelta64[h]')\n", ")"]}, {"cell_type": "code", "execution_count": 7, "id": "255d00aa-245b-4a41-b508-c3c4fd698bca", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["chunk['2m_temperature'].plot();"]}, {"cell_type": "code", "execution_count": 8, "id": "5d472ae5-28d5-4cc2-952b-01c8fe31619d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["chunk['2m_temperature'].mask.plot();"]}, {"cell_type": "markdown", "id": "497ebf9b-be1c-41af-a97a-6f163ef699b9", "metadata": {}, "source": ["In the Aggregator, if `masked=True` (default is False), the masked out values will then be ignored.\n", "\n", "Note that only target OR prediction can have a mask. Generally, best practice is to try to avoid having NaNs in the predictions.\n", "\n", "If this isn't possible, there is a brute force method `skipna` in the aggregator that will ignore all NaNs in the aggregation. However, caution is required with this option since this could also skip unexpected NaNs."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}