# WeatherBench-X: Concepts and Design Philosophy Tutorial

*Understanding the thought process behind Google's weather forecast evaluation framework*

## 🎯 Core Philosophy

WeatherBench-X embodies three fundamental principles:

1. **Modularity**: Every component is interchangeable and composable
2. **XArray-First**: All data operations use xarray's labeled dimensions
3. **Scalability**: Designed for massive datasets using Apache Beam

## 🏗️ Architecture Overview

```
Data Sources → Data Loaders → Interpolation → Metrics → Aggregation → Results
     ↓              ↓             ↓           ↓          ↓
   Zarr/NetCDF   XArray      Spatial/Temp   Statistics  Binning    Zarr/NetCDF
   Parquet       DataArrays   Alignment     Computation  Weighting
```

## 📊 Key Concepts and Data Processing Patterns

### 1. **XArray as the Foundation**

**Why XArray?**
- **Labeled dimensions**: No more index confusion (`lat`, `lon`, `time` vs `[0, 1, 2]`)
- **Broadcasting**: Automatic alignment of different coordinate systems
- **Lazy evaluation**: Process massive datasets without loading into memory
- **Integration**: Works seamlessly with pandas, numpy, and distributed systems

```python
# WeatherBench-X Pattern: Everything is an xarray DataArray
import xarray as xr
import numpy as np

# Create weather data with labeled dimensions
temperature = xr.DataArray(
    np.random.normal(20, 5, (365, 180, 360)),
    dims=['time', 'lat', 'lon'],
    coords={
        'time': pd.date_range('2024-01-01', periods=365),
        'lat': np.linspace(-90, 90, 180),
        'lon': np.linspace(-180, 180, 360)
    }
)

# Operations are intuitive and self-documenting
global_mean = temperature.mean(dim=['lat', 'lon'])  # Time series
spatial_mean = temperature.mean(dim='time')         # Climatology
regional = temperature.sel(lat=slice(30, 60))       # Geographic subset
```

### 2. **Modular Data Loading Strategy**

**Problem**: Weather data comes in many formats (Zarr, NetCDF, Parquet, CSV)
**Solution**: Abstract data loader interface

```python
# WeatherBench-X Pattern: Unified data loading interface
from weatherbenchX.data_loaders import xarray_loaders, sparse_parquet

# Gridded forecast data (Zarr format)
prediction_loader = xarray_loaders.PredictionsFromXarray(
    path='gs://weatherbench2/datasets/hres/forecasts.zarr',
    variables=['2m_temperature', '10m_wind_speed']
)

# Sparse observation data (Parquet format)
target_loader = sparse_parquet.METARFromParquet(
    path='gs://weatherbench2/datasets/observations/metar/',
    variables=['2m_temperature', '10m_wind_speed'],
    time_dim='timeNominal'
)

# Both return xarray DataArrays - unified interface!
predictions = prediction_loader.load_chunk(init_times, lead_times)
targets = target_loader.load_chunk(init_times, lead_times)
```

### 3. **Intelligent Interpolation System**

**Problem**: Forecasts are on regular grids, observations are at irregular points
**Solution**: Flexible interpolation framework

```python
# WeatherBench-X Pattern: Spatial interpolation for data alignment
from weatherbenchX import interpolations

# Interpolate gridded forecasts to observation points
interpolation = interpolations.InterpolateToReferenceCoords(
    method='linear',  # or 'nearest', 'cubic'
    extrapolate_out_of_bounds=True
)

# Apply interpolation during data loading
prediction_loader = xarray_loaders.PredictionsFromXarray(
    path='forecasts.zarr',
    variables=['2m_temperature'],
    interpolation=interpolation  # Automatic spatial alignment
)
```

### 4. **Time Chunking for Scalability**

**Problem**: Weather datasets are massive (TB-PB scale)
**Solution**: Process data in manageable time chunks

```python
# WeatherBench-X Pattern: Chunked processing
from weatherbenchX import time_chunks

# Define time chunks for processing
init_times = np.arange('2020-01-01', '2021-01-01', dtype='datetime64[D]')
lead_times = np.arange(0, 240, 6, dtype='timedelta64[h]')  # 10-day forecasts

times = time_chunks.TimeChunks(
    init_times=init_times,
    lead_times=lead_times,
    init_time_chunk_size=30,  # Process 30 days at a time
    lead_time_chunk_size=20   # Process 20 lead times at a time
)

# Each chunk is processed independently - perfect for distributed computing
for init_chunk, lead_chunk in times:
    print(f"Processing {len(init_chunk)} init times, {len(lead_chunk)} lead times")
```

### 5. **Composable Metrics System**

**Problem**: Different evaluation metrics for different use cases
**Solution**: Modular metric computation

```python
# WeatherBench-X Pattern: Composable metrics
from weatherbenchX.metrics import deterministic, probabilistic

# Define multiple metrics
metrics = {
    'rmse': deterministic.RMSE(),
    'mae': deterministic.MAE(),
    'bias': deterministic.Bias(),
    'acc': deterministic.ACC(),  # Anomaly Correlation Coefficient
}

# Metrics are computed efficiently in batch
from weatherbenchX.metrics import base as metrics_base
statistics = metrics_base.compute_unique_statistics_for_all_metrics(
    metrics, predictions, targets
)
```

### 6. **Flexible Binning and Aggregation**

**Problem**: Need to aggregate results by region, season, lead time, etc.
**Solution**: Composable binning system

```python
# WeatherBench-X Pattern: Multi-dimensional binning
from weatherbenchX import binning, aggregation, weighting

# Define spatial regions
regions = {
    'global': ((-90, 90), (0, 360)),
    'tropics': ((-30, 30), (0, 360)),
    'northern_hemisphere': ((0, 90), (0, 360))
}

# Define binning strategies
bin_by = [
    binning.Regions(regions),                    # Spatial binning
    binning.ByTimeUnit('init_time', 'season'),   # Seasonal binning
    binning.ByExactCoord('lead_time')            # Lead time binning
]

# Define weighting (important for global statistics)
weigh_by = [weighting.GridAreaWeighting()]  # Weight by grid cell area

# Create aggregator
aggregator = aggregation.Aggregator(
    reduce_dims=['init_time', 'latitude', 'longitude'],
    bin_by=bin_by,
    weigh_by=weigh_by
)
```

### 7. **Apache Beam for Distributed Processing**

**Problem**: Processing years of global weather data takes too long on one machine
**Solution**: Apache Beam for distributed, fault-tolerant processing

```python
# WeatherBench-X Pattern: Distributed processing pipeline
import apache_beam as beam
from weatherbenchX import beam_pipeline

# Define the processing pipeline
with beam.Pipeline(runner='DataflowRunner') as pipeline:
    beam_pipeline.define_pipeline(
        root=pipeline,
        times=times,
        predictions_loader=prediction_loader,
        targets_loader=target_loader,
        metrics=metrics,
        aggregator=aggregator,
        out_path='gs://my-bucket/results.zarr'
    )

# This runs on Google Cloud Dataflow with automatic scaling!
```

## 🔄 Complete Workflow Example

```python
# WeatherBench-X Complete Evaluation Workflow
import numpy as np
import apache_beam as beam
from weatherbenchX import (
    time_chunks, aggregation, binning, weighting, beam_pipeline
)
from weatherbenchX.data_loaders import xarray_loaders
from weatherbenchX.metrics import deterministic

# 1. Define time periods
init_times = np.arange('2020-01-01', '2021-01-01', dtype='datetime64[D]')
lead_times = np.arange(0, 240, 6, dtype='timedelta64[h]')
times = time_chunks.TimeChunks(init_times, lead_times, 
                               init_time_chunk_size=30)

# 2. Set up data loaders
prediction_loader = xarray_loaders.PredictionsFromXarray(
    path='gs://weatherbench2/datasets/hres/forecasts.zarr',
    variables=['2m_temperature', 'mean_sea_level_pressure']
)

target_loader = xarray_loaders.TargetsFromXarray(
    path='gs://weatherbench2/datasets/era5/targets.zarr',
    variables=['2m_temperature', 'mean_sea_level_pressure']
)

# 3. Define evaluation metrics
metrics = {
    'rmse': deterministic.RMSE(),
    'mae': deterministic.MAE(),
    'acc': deterministic.ACC()
}

# 4. Set up aggregation
regions = {'global': ((-90, 90), (0, 360))}
aggregator = aggregation.Aggregator(
    reduce_dims=['init_time', 'latitude', 'longitude'],
    bin_by=[binning.Regions(regions)],
    weigh_by=[weighting.GridAreaWeighting()]
)

# 5. Run distributed evaluation
with beam.Pipeline(runner='DirectRunner') as pipeline:
    beam_pipeline.define_pipeline(
        root=pipeline,
        times=times,
        predictions_loader=prediction_loader,
        targets_loader=target_loader,
        metrics=metrics,
        aggregator=aggregator,
        out_path='./evaluation_results.nc'
    )
```

## 🧠 Design Insights

### Why This Architecture Works

1. **Separation of Concerns**: Data loading, interpolation, metrics, and aggregation are independent
2. **Lazy Evaluation**: XArray + Dask integration means you only compute what you need
3. **Fault Tolerance**: Apache Beam handles retries and failures automatically
4. **Reproducibility**: All parameters are explicit and serializable
5. **Extensibility**: Easy to add new metrics, data sources, or processing steps

### Key Patterns for Your Interview

1. **Always use labeled dimensions** instead of positional indexing
2. **Compose complex operations** from simple, reusable components  
3. **Design for distributed processing** from the start
4. **Handle missing data gracefully** with masks and skipna options
5. **Weight spatial aggregations** properly (grid area weighting)
6. **Chunk data intelligently** for memory efficiency

This architecture represents Google's approach to production-scale scientific computing - exactly the kind of thinking that will impress in your Gridmatic interview! 🚀

## 📚 Deep Dive: Pandas vs XArray vs Dask Integration

### Pandas Usage in WeatherBench-X

WeatherBench-X uses pandas primarily for:
- **Sparse/tabular data processing** (weather station observations)
- **Time series manipulation** (date/time operations)
- **Data cleaning and preprocessing**

```python
# Example from sparse_parquet.py
def metar_preprocessing_fn(df: pd.DataFrame) -> pd.DataFrame:
    """Clean METAR weather station data"""
    # Remove duplicates by station and time
    df = df.drop_duplicates(subset=['station_id', 'time'])

    # Convert temperature units
    df['2m_temperature'] = df['2m_temperature'] + 273.15  # C to K

    # Filter valid observations
    df = df.query('quality_flag == "good"')

    return df

# Convert to xarray for unified processing
ds = df.set_index(['time', 'station_id']).to_xarray()
```

### XArray: The Core Data Model

**Why XArray over NumPy/Pandas?**

1. **Multi-dimensional labeled arrays** (perfect for weather data: time × lat × lon × level)
2. **Automatic broadcasting** and alignment
3. **Integration with Dask** for out-of-core computation
4. **NetCDF/Zarr integration** for climate data formats

```python
# WeatherBench-X XArray patterns
import xarray as xr

# 1. Coordinate-based selection (no index confusion!)
temperature = ds['temperature']
europe = temperature.sel(lat=slice(35, 70), lon=slice(-10, 40))
winter = temperature.sel(time=temperature.time.dt.season == 'DJF')

# 2. Dimension-aware operations
global_mean = temperature.mean(dim=['lat', 'lon'])  # Time series
climatology = temperature.groupby('time.month').mean()  # Monthly climatology

# 3. Broadcasting magic
anomaly = temperature - climatology  # Automatically broadcasts over time

# 4. Lazy evaluation with Dask
large_dataset = xr.open_zarr('huge_dataset.zarr', chunks={'time': 100})
result = large_dataset.mean(dim='time')  # Creates computation graph
computed_result = result.compute()  # Actually executes
```

### Apache Beam: Distributed Processing Philosophy

**Why Apache Beam over Dask?**

WeatherBench-X chose Apache Beam because:
1. **Fault tolerance**: Automatic retries and checkpointing
2. **Scalability**: Runs on Google Cloud Dataflow with auto-scaling
3. **Batch processing**: Optimized for large, finite datasets
4. **Integration**: Works well with Google Cloud Storage and BigQuery

```python
# WeatherBench-X Beam patterns
import apache_beam as beam

class LoadPredictionsAndTargets(beam.DoFn):
    """Beam transform for loading weather data chunks"""

    def process(self, time_chunk):
        init_times, lead_times = time_chunk

        # Load data chunk
        predictions = self.prediction_loader.load_chunk(init_times, lead_times)
        targets = self.target_loader.load_chunk(init_times, lead_times)

        yield (time_chunk, (predictions, targets))

# Pipeline definition
pipeline = (
    root
    | 'CreateTimeChunks' >> beam.Create(time_chunks)
    | 'LoadData' >> beam.ParDo(LoadPredictionsAndTargets())
    | 'ComputeMetrics' >> beam.ParDo(ComputeStatistics())
    | 'AggregateResults' >> beam.CombineGlobally(AggregateStatistics())
    | 'WriteResults' >> beam.io.WriteToText('results.txt')
)
```

## 🎯 Interview-Ready Concepts

### 1. **Chunking Strategies**
```python
# Memory-efficient processing
chunks = {'time': 100, 'lat': 50, 'lon': 50}  # ~20MB chunks
ds = xr.open_zarr('data.zarr', chunks=chunks)
```

### 2. **Coordinate Alignment**
```python
# Automatic alignment - no manual indexing!
forecast_grid = forecast.interp(lat=obs.lat, lon=obs.lon)
```

### 3. **Lazy Evaluation**
```python
# Build computation graph without executing
result = (ds
          .groupby('time.season')
          .mean()
          .rolling(time=30)
          .mean())
# Execute only when needed
final = result.compute()
```

### 4. **Distributed Aggregation**
```python
# Weighted global means with proper area weighting
weights = np.cos(np.deg2rad(ds.lat))
global_mean = (ds * weights).sum() / weights.sum()
```

This is the level of sophistication Gridmatic expects - production-ready scientific computing! 🚀
