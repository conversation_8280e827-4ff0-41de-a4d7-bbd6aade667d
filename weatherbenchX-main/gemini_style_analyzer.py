#!/usr/bin/env python3
"""
Custom style analyzer using Gemini API directly
Alternative to context42 for analyzing WeatherBench-X codebase
"""

import os
import glob
import json
import requests
from pathlib import Path


def find_python_files(directory, max_files=10):
    """Find Python files to analyze."""
    python_files = []
    for root, dirs, files in os.walk(directory):
        # Skip certain directories
        if any(skip in root for skip in ['.git', '__pycache__', '.pytest_cache', 'node_modules']):
            continue
        
        for file in files:
            if file.endswith('.py') and len(python_files) < max_files:
                python_files.append(os.path.join(root, file))
    
    return python_files


def read_file_content(file_path, max_lines=100):
    """Read file content with line limit."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            if len(lines) > max_lines:
                lines = lines[:max_lines] + [f"\n# ... (truncated, {len(lines) - max_lines} more lines)\n"]
            return ''.join(lines)
    except Exception as e:
        return f"# Error reading file: {e}"


def analyze_with_gemini(code_samples, api_key):
    """Analyze code samples with Gemini API."""
    
    prompt = f"""
Analyze the following Python code samples from the WeatherBench-X project and create a comprehensive style guide.

Focus on:
1. Import organization patterns
2. Naming conventions (classes, functions, variables)
3. Docstring styles and patterns
4. Type hint usage
5. Code structure and organization
6. Error handling patterns
7. Data processing patterns (pandas, xarray, numpy)

Code samples:
{code_samples}

Please provide a detailed style guide in markdown format that captures the specific patterns used in this codebase.
Include specific examples from the code and explain the reasoning behind each pattern.
"""

    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key={api_key}"
    
    headers = {
        'Content-Type': 'application/json',
    }
    
    data = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }],
        "generationConfig": {
            "temperature": 0.1,
            "topK": 1,
            "topP": 1,
            "maxOutputTokens": 8192,
        }
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()
        if 'candidates' in result and len(result['candidates']) > 0:
            return result['candidates'][0]['content']['parts'][0]['text']
        else:
            return "Error: No response from Gemini API"
            
    except Exception as e:
        return f"Error calling Gemini API: {e}"


def main():
    """Main function to analyze WeatherBench-X style."""
    
    # Configuration
    api_key = "AIzaSyArbpzocT51D5_DO6jgHFh6ggaNv0Q1gs8"
    weatherbench_dir = "weatherbenchX"
    output_file = "gemini_style_guide.md"
    
    if not os.path.exists(weatherbench_dir):
        print(f"Directory {weatherbench_dir} not found")
        return
    
    print("🔍 Finding Python files...")
    python_files = find_python_files(weatherbench_dir, max_files=8)
    print(f"Found {len(python_files)} files to analyze")
    
    print("📖 Reading code samples...")
    code_samples = ""
    for i, file_path in enumerate(python_files):
        rel_path = os.path.relpath(file_path, weatherbench_dir)
        content = read_file_content(file_path, max_lines=80)
        
        code_samples += f"\n\n## File {i+1}: {rel_path}\n\n```python\n{content}\n```\n"
        print(f"  ✓ {rel_path}")
    
    print("🤖 Analyzing with Gemini API...")
    style_guide = analyze_with_gemini(code_samples, api_key)
    
    print("💾 Saving style guide...")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# WeatherBench-X Style Guide (Generated by Gemini)\n\n")
        f.write("*This style guide was generated by analyzing the WeatherBench-X codebase using Google's Gemini AI*\n\n")
        f.write(style_guide)
    
    print(f"✅ Style guide saved to {output_file}")
    
    # Also create a summary
    summary_file = "style_summary.md"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("# WeatherBench-X Style Analysis Summary\n\n")
        f.write(f"**Files Analyzed:** {len(python_files)}\n\n")
        f.write("**Files:**\n")
        for file_path in python_files:
            rel_path = os.path.relpath(file_path, weatherbench_dir)
            f.write(f"- {rel_path}\n")
        f.write(f"\n**Full Analysis:** See {output_file}\n")
    
    print(f"📋 Summary saved to {summary_file}")


if __name__ == "__main__":
    main()
