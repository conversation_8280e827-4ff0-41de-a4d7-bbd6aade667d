{"cells": [{"cell_type": "markdown", "id": "22e74b83-f594-444d-915c-27d3c60fb227", "metadata": {}, "source": ["# Evaluate against sparse observations\n", "\n", "This guide will walk through the basics of how to evaluate a gridded forecast against a sparse ground truth dataset, in this case METAR weather station data.\n", "\n", "Sparse data is tabular in nature. The METAR data on the WeatherBench cloud bucket uses Parquet, a tabular cloud-optimized data format."]}, {"cell_type": "code", "execution_count": 15, "id": "0a11a527-143a-4b90-984d-e1cbd716066e", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from weatherbenchX import interpolations\n", "from weatherbenchX import binning\n", "from weatherbenchX import aggregation\n", "from weatherbenchX.metrics import base as metrics_base\n", "from weatherbenchX.metrics import deterministic\n", "from weatherbenchX.data_loaders import sparse_parquet\n", "from weatherbenchX.data_loaders import xarray_loaders"]}, {"cell_type": "code", "execution_count": 2, "id": "638804b8-2e7b-4c4f-8151-0b5de15a80f4", "metadata": {}, "outputs": [], "source": ["variables = ['2m_temperature', '10m_wind_speed']\n", "init_times = np.array(['2020-01-01T00', '2020-01-01T12'], dtype='datetime64[ns]')\n", "lead_times = np.array([6, 12], dtype='timedelta64[h]').astype('timedelta64[ns]')"]}, {"cell_type": "code", "execution_count": 8, "id": "636f30f6-ade9-4023-a086-4c5017a930e3", "metadata": {}, "outputs": [], "source": ["target_data_loader = sparse_parquet.METARFromParquet(\n", "    path='gs://weatherbench2/datasets/metar/metar-timeNominal-by-month/',\n", "    variables=variables,\n", "    partitioned_by='month',\n", "    time_dim='timeNominal',\n", "    add_nan_mask=True\n", ")"]}, {"cell_type": "code", "execution_count": 9, "id": "3e233fd9-ce13-4de7-a780-8aba70ed9c22", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'2m_temperature': <xarray.DataArray '2m_temperature' (index: 33026)> Size: 132kB\n", " array([273.15, 282.15, 291.15, ..., 274.15, 268.15, 272.85], dtype=float32)\n", " Coordinates:\n", "     latitude     (index) float32 132kB -77.87 -53.8 -33.38 ... 46.55 49.82 49.83\n", "     longitude    (index) float32 132kB 167.0 292.2 289.2 ... 299.0 285.0 295.7\n", "     elevation    (index) float32 132kB 8.0 22.0 476.0 141.0 ... 13.0 381.0 53.0\n", "     stationName  (index) object 264kB 'NZCM' 'SAWE' 'SCEL' ... 'CWUK' 'CWBY'\n", "     valid_time   (index) datetime64[ns] 264kB 2020-01-01T06:00:00 ... 2020-01-02\n", "     init_time    (index) datetime64[ns] 264kB 2020-01-01 ... 2020-01-01T12:00:00\n", "     lead_time    (index) timedelta64[ns] 264kB 06:00:00 06:00:00 ... 12:00:00\n", "   * index        (index) int64 264kB 0 1 2 3 4 ... 33021 33022 33023 33024 33025\n", "     mask         (index) bool 33kB True True True True ... True True True True,\n", " '10m_wind_speed': <xarray.DataArray '10m_wind_speed' (index: 33026)> Size: 132kB\n", " array([4.1, 5.1, 2.1, ..., 9.3, 2.1, 2.1], dtype=float32)\n", " Coordinates:\n", "     latitude     (index) float32 132kB -77.87 -53.8 -33.38 ... 46.55 49.82 49.83\n", "     longitude    (index) float32 132kB 167.0 292.2 289.2 ... 299.0 285.0 295.7\n", "     elevation    (index) float32 132kB 8.0 22.0 476.0 141.0 ... 13.0 381.0 53.0\n", "     stationName  (index) object 264kB 'NZCM' 'SAWE' 'SCEL' ... 'CWUK' 'CWBY'\n", "     valid_time   (index) datetime64[ns] 264kB 2020-01-01T06:00:00 ... 2020-01-02\n", "     init_time    (index) datetime64[ns] 264kB 2020-01-01 ... 2020-01-01T12:00:00\n", "     lead_time    (index) timedelta64[ns] 264kB 06:00:00 06:00:00 ... 12:00:00\n", "   * index        (index) int64 264kB 0 1 2 3 4 ... 33021 33022 33023 33024 33025\n", "     mask         (index) bool 33kB True True True True ... True True True True}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["target_chunk = target_data_loader.load_chunk(init_times, lead_times)\n", "target_chunk"]}, {"cell_type": "markdown", "id": "ba9d45f4-522b-4940-9fe3-e630d774617e", "metadata": {}, "source": ["The data now only has an index dimension with init and lead time being non-dimension coordinates. This is because for each init and lead time, there will be a different number of observations.\n", "\n", "When loaded raw, the data has missing values. One way to deal with this is using the NaN mask as done in this example. Another way to deal with this for sparse data is to set the `dropna` argument in the data loader. With the `split_variables` argument set to True, this will then return a dictionary of DataArrays for each variable with NaNs dropped. With `split_variables=False`, this will drop all values where any variable is NaN.\n", "\n", "There also are several measurement for the same station for the same nominal time (which always is a full hour; the raw observations will have different observation times). We cloud also drop those using `remove_duplicates`.\n", "\n", "Next, we need to interpolate the gridded forecast to the station locations. For this, we can use the `InterpolateToReferenceCoords` interpolation, which is passed to the gridded prediction loader."]}, {"cell_type": "code", "execution_count": 10, "id": "b7c2723d-4116-43ed-b6e8-e1c7180eb998", "metadata": {}, "outputs": [], "source": ["interpolation = interpolations.InterpolateToReferenceCoords(\n", "    method='nearest',\n", "    wrap_longitude=True\n", ")"]}, {"cell_type": "code", "execution_count": 11, "id": "0f506ccc-cc52-4e2e-a55e-48248bb959ff", "metadata": {}, "outputs": [], "source": ["prediction_path = 'gs://weatherbench2/datasets/hres/2016-2022-0012-64x32_equiangular_conservative.zarr'\n", "prediction_data_loader = xarray_loaders.PredictionsFromXarray(\n", "    path=prediction_path,\n", "    variables=variables,\n", "    interpolation=interpolation,\n", ")"]}, {"cell_type": "markdown", "id": "0bd5f49a-a814-4ad4-8a40-731fe00af751", "metadata": {}, "source": ["In addition to the init and lead times, we now also pass the target chunk to the prediction loader. The interpolator will then use the coordinates on the target chunks to interpolate the gridded predictions."]}, {"cell_type": "code", "execution_count": 12, "id": "091c4f2c-622c-4e90-a9e2-a0efb027fc24", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'2m_temperature': <xarray.DataArray '2m_temperature' (index: 33026)> Size: 132kB\n", " array([271.56082, 282.60428, 289.23752, ..., 273.2698 , 269.18704,\n", "        273.2698 ], dtype=float32)\n", " Coordinates:\n", "     init_time    (index) datetime64[ns] 264kB 2020-01-01 ... 2020-01-01T12:00:00\n", "     lead_time    (index) timedelta64[ns] 264kB 06:00:00 06:00:00 ... 12:00:00\n", "     longitude    (index) float32 132kB 167.0 292.2 289.2 ... 299.0 285.0 295.7\n", "     latitude     (index) float32 132kB -77.87 -53.8 -33.38 ... 46.55 49.82 49.83\n", "     elevation    (index) float32 132kB 8.0 22.0 476.0 141.0 ... 13.0 381.0 53.0\n", "     stationName  (index) object 264kB 'NZCM' 'SAWE' 'SCEL' ... 'CWUK' 'CWBY'\n", "     valid_time   (index) datetime64[ns] 264kB 2020-01-01T06:00:00 ... 2020-01-02\n", "   * index        (index) int64 264kB 0 1 2 3 4 ... 33021 33022 33023 33024 33025\n", "     mask         (index) bool 33kB True True True True ... True True True True\n", " Attributes:\n", "     long_name:      2 metre temperature\n", "     short_name:     t2m\n", "     standard_name:  unknown\n", "     units:          K,\n", " '10m_wind_speed': <xarray.DataArray '10m_wind_speed' (index: 33026)> Size: 132kB\n", " array([2.2358263, 5.0393696, 5.332086 , ..., 7.8019896, 3.5218635,\n", "        7.8019896], dtype=float32)\n", " Coordinates:\n", "     init_time    (index) datetime64[ns] 264kB 2020-01-01 ... 2020-01-01T12:00:00\n", "     lead_time    (index) timedelta64[ns] 264kB 06:00:00 06:00:00 ... 12:00:00\n", "     longitude    (index) float32 132kB 167.0 292.2 289.2 ... 299.0 285.0 295.7\n", "     latitude     (index) float32 132kB -77.87 -53.8 -33.38 ... 46.55 49.82 49.83\n", "     elevation    (index) float32 132kB 8.0 22.0 476.0 141.0 ... 13.0 381.0 53.0\n", "     stationName  (index) object 264kB 'NZCM' 'SAWE' 'SCEL' ... 'CWUK' 'CWBY'\n", "     valid_time   (index) datetime64[ns] 264kB 2020-01-01T06:00:00 ... 2020-01-02\n", "   * index        (index) int64 264kB 0 1 2 3 4 ... 33021 33022 33023 33024 33025\n", "     mask         (index) bool 33kB True True True True ... True True True True}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["prediction_chunk = prediction_data_loader.load_chunk(init_times, lead_times, reference=target_chunk)\n", "prediction_chunk"]}, {"cell_type": "markdown", "id": "5bc67532-37ca-446e-a1a4-c5e72f172245", "metadata": {}, "source": ["Now the target and prediction data is aligned and we can proceed as usual, apart from the aggregation. Because lead_time is now not a dimension any more, the aggregator would simply average out all lead_times. Typically, however, we want results as a function of lead time. To achieve this, we need to add a `binning` instance that adds a lead_time bin."]}, {"cell_type": "code", "execution_count": 21, "id": "93005e20-c1cd-4710-905a-d07390a63f61", "metadata": {}, "outputs": [], "source": ["metrics = {\n", "  'rmse': deterministic.RMSE(),\n", "  'mae': deterministic.MAE(),\n", "}\n", "statistics = metrics_base.compute_unique_statistics_for_all_metrics(\n", "  metrics, prediction_chunk, target_chunk\n", ")\n", "bin_by = [binning.ByExactCoord('lead_time')]\n", "aggregator = aggregation.Aggregator(\n", "  reduce_dims=['index'],\n", "  bin_by=bin_by,\n", "  masked=True\n", ")\n", "aggregation_state = aggregator.aggregate_statistics(statistics)"]}, {"cell_type": "code", "execution_count": 22, "id": "d18c23ea-0bf8-482d-ae9d-4d9b6b42a036", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 80B\n", "Dimensions:              (lead_time: 2)\n", "Coordinates:\n", "  * lead_time            (lead_time) timedelta64[ns] 16B 06:00:00 12:00:00\n", "Data variables:\n", "    rmse.2m_temperature  (lead_time) float64 16B 3.618 3.645\n", "    rmse.10m_wind_speed  (lead_time) float64 16B 2.641 2.664\n", "    mae.2m_temperature   (lead_time) float64 16B 2.723 2.732\n", "    mae.10m_wind_speed   (lead_time) float64 16B 2.001 2.051</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-51d5f343-2d2c-49b3-a004-2bb2073ffb20' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-51d5f343-2d2c-49b3-a004-2bb2073ffb20' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>lead_time</span>: 2</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-f3dee468-258c-4b63-9d41-268ef4b55840' class='xr-section-summary-in' type='checkbox'  checked><label for='section-f3dee468-258c-4b63-9d41-268ef4b55840' class='xr-section-summary' >Coordinates: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>lead_time</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>timedelta64[ns]</div><div class='xr-var-preview xr-preview'>06:00:00 12:00:00</div><input id='attrs-b1367765-732f-40c7-827b-42c3d7932bcb' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-b1367765-732f-40c7-827b-42c3d7932bcb' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c399dae7-9497-4371-9036-f1fec939b47e' class='xr-var-data-in' type='checkbox'><label for='data-c399dae7-9497-4371-9036-f1fec939b47e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([21600000000000, 43200000000000], dtype=&#x27;timedelta64[ns]&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-07083e09-9434-4e59-b860-cd981174c261' class='xr-section-summary-in' type='checkbox'  checked><label for='section-07083e09-9434-4e59-b860-cd981174c261' class='xr-section-summary' >Data variables: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>rmse.2m_temperature</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3.618 3.645</div><input id='attrs-0663355f-f578-482e-b843-7ccc32f0b395' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0663355f-f578-482e-b843-7ccc32f0b395' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-483eb2c7-5e7c-4c33-8291-0c9978669990' class='xr-var-data-in' type='checkbox'><label for='data-483eb2c7-5e7c-4c33-8291-0c9978669990' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([3.61824122, 3.64498151])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>rmse.10m_wind_speed</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>2.641 2.664</div><input id='attrs-6c0e9c9e-3b31-498b-8b54-8fe4f32c3eef' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-6c0e9c9e-3b31-498b-8b54-8fe4f32c3eef' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b90813d7-5c4a-4c41-bdea-388eb322ac43' class='xr-var-data-in' type='checkbox'><label for='data-b90813d7-5c4a-4c41-bdea-388eb322ac43' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([2.64133017, 2.66442657])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>mae.2m_temperature</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>2.723 2.732</div><input id='attrs-ba30d7d2-c2f2-4af4-88a9-0ca53a142d22' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ba30d7d2-c2f2-4af4-88a9-0ca53a142d22' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-6efef07b-7a28-46cf-9df2-2adb5e738be1' class='xr-var-data-in' type='checkbox'><label for='data-6efef07b-7a28-46cf-9df2-2adb5e738be1' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([2.72318399, 2.73244744])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>mae.10m_wind_speed</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>2.001 2.051</div><input id='attrs-57221a03-5ebc-48d7-a728-2451b20babcf' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-57221a03-5ebc-48d7-a728-2451b20babcf' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-968cce0b-e4cf-43b8-91d7-d1491e017f6a' class='xr-var-data-in' type='checkbox'><label for='data-968cce0b-e4cf-43b8-91d7-d1491e017f6a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([2.00089175, 2.05069853])</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-5416d74c-f067-4388-b5ba-f3ae494e75e5' class='xr-section-summary-in' type='checkbox'  ><label for='section-5416d74c-f067-4388-b5ba-f3ae494e75e5' class='xr-section-summary' >Indexes: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>lead_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-44068a94-f5aa-4ee5-8451-afab80ba5d05' class='xr-index-data-in' type='checkbox'/><label for='index-44068a94-f5aa-4ee5-8451-afab80ba5d05' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(TimedeltaIndex([&#x27;0 days 06:00:00&#x27;, &#x27;0 days 12:00:00&#x27;], dtype=&#x27;timedelta64[ns]&#x27;, name=&#x27;lead_time&#x27;, freq=None))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-f66e1288-c355-40c2-81b9-7d9b4a3a95a1' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-f66e1288-c355-40c2-81b9-7d9b4a3a95a1' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 80B\n", "Dimensions:              (lead_time: 2)\n", "Coordinates:\n", "  * lead_time            (lead_time) timedelta64[ns] 16B 06:00:00 12:00:00\n", "Data variables:\n", "    rmse.2m_temperature  (lead_time) float64 16B 3.618 3.645\n", "    rmse.10m_wind_speed  (lead_time) float64 16B 2.641 2.664\n", "    mae.2m_temperature   (lead_time) float64 16B 2.723 2.732\n", "    mae.10m_wind_speed   (lead_time) float64 16B 2.001 2.051"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["aggregation_state.metric_values(metrics)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}