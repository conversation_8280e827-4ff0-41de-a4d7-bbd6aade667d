{"version": 3, "sources": ["../../src/lib/generator.ts"], "sourcesContent": ["import path from \"node:path\"\nimport { llml } from \"@zenbase/llml\"\nimport { backOff } from \"exponential-backoff\"\nimport { $ } from \"zx\"\n\nconst executor = ((): string => {\n  const runner = process.argv[0]\n  if (!runner) {\n    throw new Error(\"Unexpected error: Please file an issue.\")\n  }\n\n  const binDir = path.dirname(runner)\n\n  if (runner.includes(\"pnpm\")) {\n    return path.join(binDir, \"pnpx\")\n  }\n  return path.join(binDir, \"npx\")\n})()\n\nexport type GenerateStyleGuideOptions = {\n  model: string\n  files: string[]\n  language: string\n  childStyleGuides?: Record<string, string> // relative-dir -> style guide content\n  onProgress?: (message: string) => void\n  signal?: AbortSignal\n}\n\nexport const generateStyleGuide = async ({\n  model,\n  files,\n  language,\n  childStyleGuides,\n  onProgress,\n  signal,\n}: GenerateStyleGuideOptions): Promise<void> => {\n  if (files.length === 0) {\n    throw new Error(`No ${language} files found to analyze`)\n  }\n\n  // The language IS the extension now\n  const fileExtension = language\n\n  // Get the common base directory from all files\n  const getCommonDirectory = (filePaths: string[]): string => {\n    if (filePaths.length === 0) return process.cwd()\n    if (filePaths.length === 1) return path.dirname(filePaths[0]!)\n\n    const dirs = filePaths.map(f => path.dirname(f))\n    let commonDir = dirs[0]!\n\n    for (const dir of dirs) {\n      while (!dir.startsWith(commonDir)) {\n        commonDir = path.dirname(commonDir)\n      }\n    }\n\n    return commonDir\n  }\n\n  const baseDir = getCommonDirectory(files)\n  // Detect synthesis mode\n  const isSynthesisMode = childStyleGuides && Object.keys(childStyleGuides).length > 0\n\n  const prompt = llml({\n    role: \"Code Anthropologist & Style Detective\",\n    task: isSynthesisMode\n      ? \"Synthesize a masterful style guide by studying how architectural patterns flow across subdirectories, revealing the developer's complete coding philosophy and decision-making framework.\"\n      : \"Decode the developer's unique coding DNA by forensically analyzing their patterns, preferences, unconscious habits, and the subtle decisions that make their code distinctively theirs.\",\n    mission:\n      \"Create a style guide so precise and insightful that an AI agent can seamlessly mimic the developer's coding style, making contributions indistinguishable from the original author. This guide should capture not just WHAT they code, but HOW they think.\",\n    fileCreationInstruction: {\n      importantNote: `**IMPORTANT**: Create a file named 'style.${fileExtension}.md' in the current working directory with the following structure:`,\n      steps: [\n        {\n          step: \"Start with YAML frontmatter (between triple dashes), example:\",\n          example: {\n            yamlFrontmatter: [\n              `\n              ---\n              description: ${fileExtension} Style Guide\n              globs: **/*.${fileExtension}\n              alwaysApply: false\n              ---\n              `,\n            ],\n          },\n        },\n        {\n          step: \"Follow the frontmatter with the complete style guide analysis below.\",\n        },\n      ],\n    },\n    progressInstructions: {\n      description:\n        \"Output progress updates during analysis by prefixing lines with [PROGRESS]. Before beginning with the actual analysis, output a [PROGRESS] with something whimsical and playful.\",\n      goal: \"Make the user feel happy engaged, and occassionally laugh.\",\n      examples: [\n        \"[PROGRESS] Sneaking past legacy code traps armed only with a rubber duck...\",\n        \"[PROGRESS] Translating cryptic TODOs into ancient developer runes...\",\n        \"[PROGRESS] Playing hide-and-seek with global variables (they always win)\",\n        \"[PROGRESS] Summoning the spirit of clean code with a ritual of semicolons and whitespace\",\n      ],\n      requires: [\"fun\", \"whimsy\", \"playful\", \"engaging\"],\n      constraints: [\"the user will only see the first line of [PROGRESS], so no newlines\"],\n    },\n    context: {\n      directory: baseDir,\n      fileCount: files.length,\n      files: [...files.map(f => path.relative(baseDir, f))].join(\"\\n\"),\n      deepAnalysisRequired: true,\n      captureSubtlePatterns: true,\n      revealImplicitKnowledge: true,\n      ...(isSynthesisMode && {\n        synthesisMode: true,\n        childDirectories: Object.keys(childStyleGuides).length,\n        synthesisNote:\n          \"This is a synthesis task - reveal the emergent philosophy and patterns that unite all subdirectories into a coherent whole.\",\n      }),\n    },\n    childStyleGuides: childStyleGuides || undefined,\n    analysisFramework: {\n      \"Layer 1: Surface Patterns\": [\n        \"Variable/function/class naming conventions (including abbreviations, compound words, domain terms)\",\n        \"File naming and organization patterns\",\n        \"Import statement organization and grouping logic\",\n        \"Comment style, placement, and density\",\n        \"Code spacing and visual rhythm preferences\",\n        \"Bracket placement and indentation philosophy\",\n      ],\n      \"Layer 2: Architectural Patterns\": [\n        \"Module boundaries and responsibility distribution\",\n        \"Dependency injection and inversion of control approaches\",\n        \"State management philosophy (immutable vs mutable, local vs global)\",\n        \"Error handling strategies and exception philosophy\",\n        \"API design principles (REST vs GraphQL patterns, naming conventions)\",\n        \"Data flow patterns and transformation approaches\",\n        \"Abstraction layers and interface design\",\n        \"Service/component communication patterns\",\n      ],\n      \"Layer 3: Developer Psychology\": [\n        \"Problem decomposition approach (top-down vs bottom-up)\",\n        \"Abstraction threshold (when they choose to DRY vs WET)\",\n        \"Performance vs readability trade-off decisions\",\n        \"Testing philosophy (unit vs integration preference, coverage goals)\",\n        \"Documentation depth and style (when/what/how they document)\",\n        \"Defensive programming tendencies\",\n        \"Refactoring triggers and patterns\",\n        \"Code review focus areas\",\n      ],\n      \"Layer 4: Domain-Specific Patterns\": [\n        \"Business logic organization and encapsulation\",\n        \"Domain terminology usage and modeling\",\n        \"Industry-specific patterns and conventions\",\n        \"Custom abstractions and their evolution\",\n        \"Performance optimizations specific to the problem domain\",\n        \"Security patterns and threat modeling approach\",\n      ],\n    },\n    outputSections: {\n      \"# 1. CORE PHILOSOPHY\":\n        \"The developer's fundamental beliefs about good code, their values, and what they optimize for\",\n      \"# 2. NAMING PATTERNS\":\n        \"Comprehensive naming conventions with decision trees for different contexts (when to abbreviate, how to handle compound words, domain term usage)\",\n      \"# 3. CODE ORGANIZATION\":\n        \"How code is structured from project level down to function internals, including file boundaries and module responsibilities\",\n      \"# 4. ERROR HANDLING\": \"Complete approach to errors, edge cases, validation, and defensive coding strategies\",\n      \"# 5. STATE MANAGEMENT\": \"How state is organized, accessed, mutated, and synchronized across the application\",\n      \"# 6. API DESIGN\": \"Patterns for internal and external APIs, including naming, versioning, and contract design\",\n      \"# 7. TESTING APPROACH\": \"Testing philosophy, patterns, coverage goals, and what they consider worth testing\",\n      \"# 8. PERFORMANCE PATTERNS\":\n        \"When and how performance is prioritized, common optimizations, and measurement approaches\",\n      \"# 9. ANTI-PATTERNS\": \"What the developer explicitly avoids and why, with examples of rejected approaches\",\n      \"# 10. DECISION TREES\":\n        \"When to use pattern A vs pattern B, with clear criteria for choosing between alternatives\",\n      \"# 11. AI AGENT INSTRUCTIONS\":\n        \"Step-by-step guide for AI agents to write code in this style, including pre-flight checklist and review criteria\",\n    },\n    detectionStrategies: [\n      \"Find patterns that appear 3+ times - these reveal intentional choices\",\n      \"Identify naming patterns beyond simple case conventions (abbreviation rules, compound word handling)\",\n      \"Detect implicit hierarchies and boundaries in code organization\",\n      \"Recognize custom abstractions and understand their purpose and evolution\",\n      \"Spot error handling consistency and philosophy across different contexts\",\n      \"Analyze comment patterns to understand documentation philosophy\",\n      \"Identify performance optimizations vs readability choices and the criteria used\",\n      \"Look for patterns in how external dependencies are wrapped or used\",\n      \"Notice patterns in test structure and what gets tested vs what doesn't\",\n      \"Detect code smells the developer consistently avoids\",\n    ],\n    exampleRequirements: {\n      quantity: \"Every pattern MUST include 2-3 real code examples from the actual codebase\",\n      comparison: \"Show the pattern alongside what it's NOT (anti-examples) to clarify boundaries\",\n      rationale: \"Explain WHY this pattern exists in THIS specific codebase and problem domain\",\n      evolution: \"If possible, show how patterns vary in different contexts or have evolved\",\n      specificity: \"Use exact code snippets, not simplified examples\",\n    },\n    aiAgentGuidance: {\n      preWriteChecklist: [\n        \"Review relevant existing code in the same module/directory\",\n        \"Identify the abstraction level and patterns used nearby\",\n        \"Check naming conventions for similar concepts\",\n        \"Determine error handling approach for this context\",\n        \"Consider performance vs readability trade-offs\",\n      ],\n      writingProcess: [\n        \"Start with the problem decomposition approach typical for this developer\",\n        \"Apply naming patterns consistently with existing code\",\n        \"Structure code following established organizational patterns\",\n        \"Implement error handling using identified strategies\",\n        \"Add comments/documentation matching the developer's style\",\n      ],\n      reviewCriteria: [\n        \"Does it feel like it belongs in this codebase?\",\n        \"Are naming patterns consistent with existing code?\",\n        \"Is the abstraction level appropriate for the context?\",\n        \"Does error handling match established patterns?\",\n        \"Would the original developer make the same choices?\",\n      ],\n    },\n    analysisInstructions: isSynthesisMode\n      ? [\n          \"PRIMARY: Synthesize the complete coding philosophy from child directory patterns\",\n          \"Identify the core principles that unite all subdirectories\",\n          \"Reveal how local patterns contribute to the global architecture\",\n          \"Map the flow of ideas and patterns across directory boundaries\",\n          \"Extract the mental model that guides all architectural decisions\",\n          \"Show how different subsystems embody the same core philosophy differently\",\n          \"Create a unified theory of the developer's coding approach\",\n          \"Identify evolution patterns - how the style has matured across the codebase\",\n        ]\n      : [\n          \"Perform forensic analysis on ALL files to decode the developer's style DNA\",\n          \"Focus on the WHY behind every pattern - what problem does it solve?\",\n          \"Identify the developer's mental model and problem-solving approach\",\n          \"Detect subtle patterns that reveal unconscious preferences\",\n          \"Map the decision criteria used for architectural choices\",\n          \"Uncover the hidden rules that make code 'feel right' to this developer\",\n          \"Identify patterns unique to this codebase vs general best practices\",\n          \"Reveal the developer's priorities through their trade-off decisions\",\n        ],\n    guardrails: isSynthesisMode\n      ? [\n          \"Every synthesized pattern must emerge from actual child style guides\",\n          \"Explain how local patterns serve the global architecture\",\n          \"Focus on philosophy and principles, not implementation details\",\n          \"Show how patterns evolved and adapted across subdirectories\",\n          \"Create actionable guidance for system-wide development\",\n          \"Reveal the 'why' behind architectural decisions that span directories\",\n          \"Build a coherent narrative, not a pattern catalog\",\n        ]\n      : [\n          \"Every pattern must be evidenced by actual code (no speculation)\",\n          \"Explain the problem each pattern solves in this specific context\",\n          \"Distinguish between intentional patterns and coincidences\",\n          \"Focus on patterns that require human judgment to apply\",\n          \"Make the implicit explicit without losing nuance\",\n          \"Capture the 'feel' of the code, not just the mechanics\",\n          \"Enable AI agents to make the same decisions the developer would\",\n        ],\n    tips: isSynthesisMode\n      ? [\n          \"Look for the philosophical thread that connects all directories\",\n          \"Identify how global principles manifest differently in each context\",\n          \"Notice evolutionary patterns - how style changed over time\",\n          \"Find the core mental model that drives all decisions\",\n          \"Reveal the unwritten rules that govern the entire system\",\n        ]\n      : [\n          \"Study code like an anthropologist studies culture\",\n          \"Look for rituals and repeated ceremonies in the code\",\n          \"Identify the developer's 'signature moves' and favorite patterns\",\n          \"Notice what's NOT there - avoided patterns are as revealing as used ones\",\n          \"Find the moments where the developer chose simplicity vs cleverness\",\n          \"Detect the developer's personal style beyond team conventions\",\n        ],\n  })\n\n  try {\n    await backOff(\n      async () => {\n        // Check if already aborted\n        if (signal?.aborted) {\n          throw new Error(\"Operation cancelled\")\n        }\n\n        // Run gemini-cli directly without shell wrapper\n        const p = $({\n          cwd: baseDir,\n          input: prompt,\n          signal,\n        })`${executor} @google/gemini-cli --yolo -m ${model} -a`\n\n        // Stream stdout and parse progress messages\n        try {\n          for await (const chunk of p.stdout) {\n            // Check if aborted during streaming\n            if (signal?.aborted) {\n              p.kill()\n              throw new Error(\"Operation cancelled\")\n            }\n\n            const lines = chunk.toString().split(\"\\n\")\n            for (const line of lines) {\n              if (line.startsWith(\"[PROGRESS]\")) {\n                const message = line.substring(10).trim()\n                onProgress?.(message)\n              }\n            }\n          }\n\n          // Wait for process to complete\n          await p\n        } catch (error) {\n          // Kill the process if still running\n          p.kill()\n          throw error\n        }\n      },\n      {\n        numOfAttempts: 3,\n        timeMultiple: 2,\n        startingDelay: 2000,\n      },\n    )\n  } catch (error) {\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error\"\n    // biome-ignore lint/suspicious/noExplicitAny: Error logging is intentional\n    const stderr = (error as any).stderr || \"\"\n\n    if (signal?.aborted) {\n      throw new Error(\"Operation cancelled\")\n    }\n\n    if (stderr.includes(\"rate limit\")) {\n      throw new Error(\"Gemini API rate limit exceeded. Exponential backoff failed. Please wait and try again.\")\n    }\n\n    throw new Error(`Gemini command failed: ${stderr || errorMessage}`)\n  }\n}\n"], "mappings": "AAAA,OAAO,UAAU;AACjB,SAAS,YAAY;AACrB,SAAS,eAAe;AACxB,SAAS,SAAS;AAElB,MAAM,YAAY,MAAc;AAC9B,QAAM,SAAS,QAAQ,KAAK,CAAC;AAC7B,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC3D;AAEA,QAAM,SAAS,KAAK,QAAQ,MAAM;AAElC,MAAI,OAAO,SAAS,MAAM,GAAG;AAC3B,WAAO,KAAK,KAAK,QAAQ,MAAM;AAAA,EACjC;AACA,SAAO,KAAK,KAAK,QAAQ,KAAK;AAChC,GAAG;AAWI,MAAM,qBAAqB,OAAO;AAAA,EACvC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAgD;AAC9C,MAAI,MAAM,WAAW,GAAG;AACtB,UAAM,IAAI,MAAM,MAAM,QAAQ,yBAAyB;AAAA,EACzD;AAGA,QAAM,gBAAgB;AAGtB,QAAM,qBAAqB,CAAC,cAAgC;AAC1D,QAAI,UAAU,WAAW,EAAG,QAAO,QAAQ,IAAI;AAC/C,QAAI,UAAU,WAAW,EAAG,QAAO,KAAK,QAAQ,UAAU,CAAC,CAAE;AAE7D,UAAM,OAAO,UAAU,IAAI,OAAK,KAAK,QAAQ,CAAC,CAAC;AAC/C,QAAI,YAAY,KAAK,CAAC;AAEtB,eAAW,OAAO,MAAM;AACtB,aAAO,CAAC,IAAI,WAAW,SAAS,GAAG;AACjC,oBAAY,KAAK,QAAQ,SAAS;AAAA,MACpC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,QAAM,UAAU,mBAAmB,KAAK;AAExC,QAAM,kBAAkB,oBAAoB,OAAO,KAAK,gBAAgB,EAAE,SAAS;AAEnF,QAAM,SAAS,KAAK;AAAA,IAClB,MAAM;AAAA,IACN,MAAM,kBACF,8LACA;AAAA,IACJ,SACE;AAAA,IACF,yBAAyB;AAAA,MACvB,eAAe,6CAA6C,aAAa;AAAA,MACzE,OAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,YACP,iBAAiB;AAAA,cACf;AAAA;AAAA,6BAEe,aAAa;AAAA,4BACd,aAAa;AAAA;AAAA;AAAA;AAAA,YAI7B;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,IACA,sBAAsB;AAAA,MACpB,aACE;AAAA,MACF,MAAM;AAAA,MACN,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAU,CAAC,OAAO,UAAU,WAAW,UAAU;AAAA,MACjD,aAAa,CAAC,qEAAqE;AAAA,IACrF;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,WAAW,MAAM;AAAA,MACjB,OAAO,CAAC,GAAG,MAAM,IAAI,OAAK,KAAK,SAAS,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI;AAAA,MAC/D,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,yBAAyB;AAAA,MACzB,GAAI,mBAAmB;AAAA,QACrB,eAAe;AAAA,QACf,kBAAkB,OAAO,KAAK,gBAAgB,EAAE;AAAA,QAChD,eACE;AAAA,MACJ;AAAA,IACF;AAAA,IACA,kBAAkB,oBAAoB;AAAA,IACtC,mBAAmB;AAAA,MACjB,6BAA6B;AAAA,QAC3B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,mCAAmC;AAAA,QACjC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,iCAAiC;AAAA,QAC/B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,qCAAqC;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,wBACE;AAAA,MACF,wBACE;AAAA,MACF,0BACE;AAAA,MACF,uBAAuB;AAAA,MACvB,yBAAyB;AAAA,MACzB,mBAAmB;AAAA,MACnB,yBAAyB;AAAA,MACzB,6BACE;AAAA,MACF,sBAAsB;AAAA,MACtB,wBACE;AAAA,MACF,+BACE;AAAA,IACJ;AAAA,IACA,qBAAqB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,IACf;AAAA,IACA,iBAAiB;AAAA,MACf,mBAAmB;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,sBAAsB,kBAClB;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACJ,YAAY,kBACR;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACJ,MAAM,kBACF;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACN,CAAC;AAED,MAAI;AACF,UAAM;AAAA,MACJ,YAAY;AAEV,YAAI,QAAQ,SAAS;AACnB,gBAAM,IAAI,MAAM,qBAAqB;AAAA,QACvC;AAGA,cAAM,IAAI,EAAE;AAAA,UACV,KAAK;AAAA,UACL,OAAO;AAAA,UACP;AAAA,QACF,CAAC,IAAI,QAAQ,iCAAiC,KAAK;AAGnD,YAAI;AACF,2BAAiB,SAAS,EAAE,QAAQ;AAElC,gBAAI,QAAQ,SAAS;AACnB,gBAAE,KAAK;AACP,oBAAM,IAAI,MAAM,qBAAqB;AAAA,YACvC;AAEA,kBAAM,QAAQ,MAAM,SAAS,EAAE,MAAM,IAAI;AACzC,uBAAW,QAAQ,OAAO;AACxB,kBAAI,KAAK,WAAW,YAAY,GAAG;AACjC,sBAAM,UAAU,KAAK,UAAU,EAAE,EAAE,KAAK;AACxC,6BAAa,OAAO;AAAA,cACtB;AAAA,YACF;AAAA,UACF;AAGA,gBAAM;AAAA,QACR,SAAS,OAAO;AAEd,YAAE,KAAK;AACP,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA;AAAA,QACE,eAAe;AAAA,QACf,cAAc;AAAA,QACd,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF,SAAS,OAAO;AACd,UAAM,eAAe,iBAAiB,QAAQ,MAAM,UAAU;AAE9D,UAAM,SAAU,MAAc,UAAU;AAExC,QAAI,QAAQ,SAAS;AACnB,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACvC;AAEA,QAAI,OAAO,SAAS,YAAY,GAAG;AACjC,YAAM,IAAI,MAAM,wFAAwF;AAAA,IAC1G;AAEA,UAAM,IAAI,MAAM,0BAA0B,UAAU,YAAY,EAAE;AAAA,EACpE;AACF;", "names": []}