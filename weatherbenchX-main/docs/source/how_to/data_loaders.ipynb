{"cells": [{"cell_type": "markdown", "id": "e75f86f1-dcf3-4dc3-a245-bf01e9d9da18", "metadata": {}, "source": ["# Implement a new data loader\n", "\n", "WB-X can, in theory, deal with any kind of data. All that is required is an appropriate data loader that, for a given init/lead time combination returns an xr.Dataset.\n", "\n", "Let's go through the building blocks of a data loader. The `__init__` of the data loader base class requires three arguments:\n", "```md\n", "Args:\n", "  interpolation: (Optional) Interpolation to be applied to the data.\n", "  compute: Load chunk into memory. Default: True.\n", "  add_nan_mask: Adds a boolean coordinate named 'mask' to each variable\n", "    (variables will be split into DataArrays if they are not already), with\n", "    False indicating NaN values. To be used for masked aggregation. Default:\n", "    False.\n", "```\n", "\n", "The next and only other thing that needs to be implemented is `_load_chunk_from_source()` which takes the init/lead times and returns the appropriate data array.\n", "\n", "Note that lead_time can be either an array or a slice. The latter case is used in cases where target or prediction data comes at random times (e.g. in the case of weather station data). In most cases, however, you probably want exact lead times.\n", "\n", "Depending on how the data is stored, the init/lead times can be accessed in a single call, e.g. for Zarr data, or separately. The example below shows a loop over init/lead time in a case where each init/lead needs to be accessed separately.\n", "\n", "```python\n", "class MyNewDataLoader(data_loader_base.DataLoader):\n", "  def __init__(\n", "      self,\n", "      *args,\n", "      interpolation: Optional[interpolations.Interpolation] = None,\n", "      compute: bool = True,\n", "      add_nan_mask: bool = False,\n", "  ):\n", "    super().__init__(\n", "        interpolation=interpolation,\n", "        compute=compute,\n", "        add_nan_mask=add_nan_mask,\n", "    )\n", "\n", "  def _load_chunk_from_source(\n", "      self,\n", "      init_times: np.n<PERSON><PERSON>,\n", "      lead_times: Optional[Union[np.ndarray, slice]] = None,\n", "  ) -> Mapping[<PERSON><PERSON><PERSON>, <PERSON>r.<PERSON>]:\n", "    if not isinstance(lead_times, np.ndarray):\n", "      raise ValueError('Only exact lead times are supported.')\n", "\n", "    datasets = []\n", "    for init_time in init_times:\n", "      for lead_time in lead_times:\n", "        ds = some_data_loading_function(init_time, lead_time)\n", "        datasets.append(ds)\n", "    chunk = xr.merge(datasets)\n", "    return chunk\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "632141cc-d73f-483c-af15-370e29fac7f4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}