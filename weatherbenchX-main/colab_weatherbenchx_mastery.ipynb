{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# WeatherBench-X Mastery Tutorial for Gridmatic Interview\n", "\n", "**🎯 Goal**: Master all WeatherBench-X concepts through hands-on practice\n", "\n", "**📚 What You'll Learn**:\n", "1. XArray operations with weather data\n", "2. Aggregation patterns (AggregationState)\n", "3. Binning strategies (spatial/temporal)\n", "4. Weighting systems (area weighting)\n", "5. Interpolation methods\n", "6. Metrics computation\n", "7. Complete evaluation workflows\n", "\n", "**⏱️ Time**: 2-3 hours of focused practice\n", "\n", "**🚀 Interview Ready**: After this tutorial, you'll understand production-scale weather data processing!"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 🔧 Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install"}, "outputs": [], "source": ["# Install required packages\n", "!pip install xarray numpy pandas mat<PERSON><PERSON><PERSON>b seaborn dask netcdf4 zarr\n", "!pip install cartopy  # For map plotting\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import Dict, List, Optional, Tuple, Union\n", "from abc import ABC, abstractmethod\n", "import dataclasses\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ Setup complete! Ready to master WeatherBench-X concepts.\")"]}, {"cell_type": "markdown", "metadata": {"id": "concept1"}, "source": ["# 📊 Concept 1: XArray Mastery - The Foundation\n", "\n", "**Why <PERSON><PERSON><PERSON><PERSON>?** Weather data is multi-dimensional with labeled coordinates. XArray prevents bugs and makes operations intuitive."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "xarray_basics"}, "outputs": [], "source": ["# Create realistic weather data\n", "def create_weather_dataset():\n", "    \"\"\"Create sample weather dataset with realistic patterns\"\"\"\n", "    \n", "    # Coordinates\n", "    time = pd.date_range('2024-01-01', periods=365, freq='D')\n", "    lat = np.linspace(-90, 90, 37)  # 5-degree resolution\n", "    lon = np.linspace(0, 360, 72)   # 5-degree resolution\n", "    lead_time = np.arange(0, 168, 6, dtype='timedelta64[h]')  # 0-7 days, 6-hourly\n", "    \n", "    # Create temperature with realistic patterns\n", "    lat_grid, lon_grid = np.meshgrid(lat, lon, indexing='ij')\n", "    \n", "    # Base temperature: warmer at equator, colder at poles\n", "    base_temp = 288 - 40 * np.abs(lat_grid) / 90  # <PERSON><PERSON>\n", "    \n", "    # Add seasonal cycle\n", "    day_of_year = np.arange(365)\n", "    seasonal_cycle = 20 * np.sin(2 * np.pi * (day_of_year - 80) / 365)\n", "    \n", "    # Combine patterns\n", "    temperature = np.zeros((365, 37, 72, 28))  # time, lat, lon, lead_time\n", "    \n", "    for t in range(365):\n", "        for lt in range(28):\n", "            # Add forecast error that increases with lead time\n", "            forecast_error = np.random.normal(0, 0.5 + 0.1 * lt, (37, 72))\n", "            temperature[t, :, :, lt] = (base_temp + \n", "                                      seasonal_cycle[t] * np.cos(np.deg2rad(lat_grid)) +\n", "                                      forecast_error)\n", "    \n", "    # Create xarray Dataset\n", "    ds = xr.Dataset({\n", "        'temperature': (['time', 'lat', 'lon', 'lead_time'], temperature),\n", "        'precipitation': (['time', 'lat', 'lon', 'lead_time'], \n", "                        np.maximum(0, np.random.exponential(2, temperature.shape)))\n", "    }, coords={\n", "        'time': time,\n", "        'lat': lat,\n", "        'lon': lon,\n", "        'lead_time': lead_time\n", "    })\n", "    \n", "    return ds\n", "\n", "# Create the dataset\n", "weather_data = create_weather_dataset()\n", "print(\"📊 Weather dataset created:\")\n", "print(weather_data)\n", "print(f\"\\n📏 Data size: {weather_data.nbytes / 1e6:.1f} MB\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "xarray_operations"}, "outputs": [], "source": ["# XArray Operations Mastery\n", "print(\"🎯 XArray Operations Practice\")\n", "print(\"=\" * 40)\n", "\n", "# 1. Coordinate-based selection (NOT positional indexing!)\n", "print(\"\\n1. Coordinate-based Selection:\")\n", "europe = weather_data.sel(lat=slice(35, 70), lon=slice(-10, 40))\n", "print(f\"   Europe subset shape: {europe.temperature.shape}\")\n", "\n", "winter = weather_data.sel(time=weather_data.time.dt.season == 'DJF')\n", "print(f\"   Winter data shape: {winter.temperature.shape}\")\n", "\n", "short_term = weather_data.sel(lead_time=slice('0 hours', '48 hours'))\n", "print(f\"   Short-term forecasts shape: {short_term.temperature.shape}\")\n", "\n", "# 2. Dimension-aware operations\n", "print(\"\\n2. Dimension-aware Operations:\")\n", "global_mean_timeseries = weather_data.temperature.mean(dim=['lat', 'lon'])\n", "print(f\"   Global mean time series shape: {global_mean_timeseries.shape}\")\n", "\n", "climatology = weather_data.temperature.groupby('time.month').mean()\n", "print(f\"   Monthly climatology shape: {climatology.shape}\")\n", "\n", "# 3. Broadcasting magic\n", "print(\"\\n3. Broadcasting and Alignment:\")\n", "anomaly = weather_data.temperature - climatology\n", "print(f\"   Temperature anomaly shape: {anomaly.shape}\")\n", "print(\"   ✅ Automatic broadcasting over time dimension!\")\n", "\n", "# 4. Advanced operations\n", "print(\"\\n4. Advanced Operations:\")\n", "# Rolling mean (30-day moving average)\n", "smoothed = weather_data.temperature.rolling(time=30, center=True).mean()\n", "print(f\"   30-day smoothed data shape: {smoothed.shape}\")\n", "\n", "# Resampling (monthly means)\n", "monthly = weather_data.resample(time='M').mean()\n", "print(f\"   Monthly resampled shape: {monthly.temperature.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "xarray_visualization"}, "outputs": [], "source": ["# Visualize XArray operations\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Global mean temperature time series\n", "global_temp = weather_data.temperature.sel(lead_time='0 hours').mean(dim=['lat', 'lon'])\n", "global_temp.plot(ax=axes[0,0])\n", "axes[0,0].set_title('Global Mean Temperature Time Series')\n", "axes[0,0].set_ylabel('Temperature (K)')\n", "\n", "# Temperature map for a specific day\n", "temp_map = weather_data.temperature.sel(time='2024-07-01', lead_time='0 hours')\n", "temp_map.plot(ax=axes[0,1], cmap='RdYlBu_r')\n", "axes[0,1].set_title('Temperature Map (July 1, 2024)')\n", "\n", "# Seasonal cycle by latitude\n", "seasonal = weather_data.temperature.sel(lead_time='0 hours').groupby('time.season').mean()\n", "seasonal.mean(dim='lon').plot(ax=axes[1,0], y='lat')\n", "axes[1,0].set_title('Seasonal Temperature by Latitude')\n", "axes[1,0].set_xlabel('Temperature (K)')\n", "\n", "# Forecast error growth\n", "forecast_error = weather_data.temperature.std(dim=['time', 'lat', 'lon'])\n", "forecast_error.plot(ax=axes[1,1])\n", "axes[1,1].set_title('Forecast Error Growth with Lead Time')\n", "axes[1,1].set_ylabel('Temperature Std Dev (K)')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ XArray visualization complete!\")\n", "print(\"🎯 Key takeaway: XArray makes complex operations intuitive with labeled dimensions\")"]}, {"cell_type": "markdown", "metadata": {"id": "concept2"}, "source": ["# 🔄 Concept 2: Aggregation System - The Heart of WeatherBench-X\n", "\n", "**Core Concept**: `AggregationState` allows combining partial results from distributed processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "aggregation_state"}, "outputs": [], "source": ["@dataclasses.dataclass\n", "class AggregationState:\n", "    \"\"\"Container for weighted statistics that can be combined.\n", "    \n", "    This is the key abstraction that enables distributed processing!\n", "    \"\"\"\n", "    sum_weighted_statistics: Dict\n", "    sum_weights: Dict\n", "    \n", "    def __add__(self, other: 'AggregationState') -> 'AggregationState':\n", "        \"\"\"Combine two aggregation states - crucial for distributed processing\"\"\"\n", "        def add_nested_dicts(dict1, dict2):\n", "            result = {}\n", "            for key in dict1:\n", "                if isinstance(dict1[key], dict):\n", "                    result[key] = add_nested_dicts(dict1[key], dict2[key])\n", "                else:\n", "                    result[key] = dict1[key] + dict2[key]\n", "            return result\n", "        \n", "        return AggregationState(\n", "            sum_weighted_statistics=add_nested_dicts(self.sum_weighted_statistics, \n", "                                                   other.sum_weighted_statistics),\n", "            sum_weights=add_nested_dicts(self.sum_weights, other.sum_weights)\n", "        )\n", "    \n", "    def mean_statistics(self) -> Dict:\n", "        \"\"\"Compute final weighted means: sum_weighted_stats / sum_weights\"\"\"\n", "        def divide_nested_dicts(stats_dict, weights_dict):\n", "            result = {}\n", "            for key in stats_dict:\n", "                if isinstance(stats_dict[key], dict):\n", "                    result[key] = divide_nested_dicts(stats_dict[key], weights_dict[key])\n", "                else:\n", "                    result[key] = stats_dict[key] / weights_dict[key]\n", "            return result\n", "        \n", "        return divide_nested_dicts(self.sum_weighted_statistics, self.sum_weights)\n", "\n", "# Demonstrate the power of AggregationState\n", "print(\"🔄 AggregationState Demo: Why This Pattern is Crucial\")\n", "print(\"=\" * 55)\n", "\n", "# Simulate processing data in chunks (like distributed processing)\n", "chunk1_data = np.array([1, 2, 3, 4, 5])  # 5 data points\n", "chunk2_data = np.array([6, 7, 8])        # 3 data points\n", "chunk3_data = np.array([9, 10])          # 2 data points\n", "\n", "# WRONG WAY: Average the averages\n", "chunk1_mean = chunk1_data.mean()  # 3.0\n", "chunk2_mean = chunk2_data.mean()  # 7.0  \n", "chunk3_mean = chunk3_data.mean()  # 9.5\n", "wrong_total_mean = (chunk1_mean + chunk2_mean + chunk3_mean) / 3\n", "\n", "# CORRECT WAY: Use AggregationState\n", "state1 = AggregationState(\n", "    sum_weighted_statistics={'temperature': chunk1_data.sum()},\n", "    sum_weights={'temperature': len(chunk1_data)}\n", ")\n", "state2 = AggregationState(\n", "    sum_weighted_statistics={'temperature': chunk2_data.sum()},\n", "    sum_weights={'temperature': len(chunk2_data)}\n", ")\n", "state3 = AggregationState(\n", "    sum_weighted_statistics={'temperature': chunk3_data.sum()},\n", "    sum_weights={'temperature': len(chunk3_data)}\n", ")\n", "\n", "# Combine states (this is what happens in distributed processing)\n", "combined_state = state1 + state2 + state3\n", "correct_total_mean = combined_state.mean_statistics()['temperature']\n", "\n", "# True answer\n", "all_data = np.concatenate([chunk1_data, chunk2_data, chunk3_data])\n", "true_mean = all_data.mean()\n", "\n", "print(f\"❌ Wrong (average of averages): {wrong_total_mean:.2f}\")\n", "print(f\"✅ Correct (AggregationState):  {correct_total_mean:.2f}\")\n", "print(f\"🎯 True answer:                {true_mean:.2f}\")\n", "print(f\"\\n💡 Error from wrong method: {abs(wrong_total_mean - true_mean):.2f}\")\n", "print(f\"✨ Error from correct method: {abs(correct_total_mean - true_mean):.6f}\")\n", "\n", "print(\"\\n🚀 This is why WeatherBench-X can process petabyte datasets correctly!\")"]}, {"cell_type": "markdown", "metadata": {"id": "concept3"}, "source": ["# 🗺️ Concept 3: Binning System - Multi-Dimensional Grouping\n", "\n", "**Core Concept**: Create boolean masks with new dimensions for flexible grouping."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "binning_system"}, "outputs": [], "source": ["class Binning(ABC):\n", "    \"\"\"Base class for all binning strategies\"\"\"\n", "    \n", "    def __init__(self, bin_dim_name: str):\n", "        self.bin_dim_name = bin_dim_name\n", "    \n", "    @abstractmethod\n", "    def create_bin_mask(self, statistic: xr.DataArray) -> xr.<PERSON>Array:\n", "        \"\"\"Create boolean mask with new bin dimension\"\"\"\n", "        pass\n", "\n", "\n", "class Regions(Binning):\n", "    \"\"\"Bin by geographic regions - most common spatial binning\"\"\"\n", "    \n", "    def __init__(self, regions: Dict[str, Tu<PERSON>], bin_dim_name: str = 'region'):\n", "        super().__init__(bin_dim_name)\n", "        self.regions = regions\n", "    \n", "    def create_bin_mask(self, statistic: xr.DataArray) -> xr.<PERSON>Array:\n", "        \"\"\"Create mask for each region\"\"\"\n", "        masks = []\n", "        region_names = []\n", "        \n", "        for region_name, ((lat_min, lat_max), (lon_min, lon_max)) in self.regions.items():\n", "            # Create boolean mask for this region\n", "            lat_mask = (statistic.lat >= lat_min) & (statistic.lat <= lat_max)\n", "            lon_mask = (statistic.lon >= lon_min) & (statistic.lon <= lon_max)\n", "            region_mask = lat_mask & lon_mask\n", "            \n", "            masks.append(region_mask)\n", "            region_names.append(region_name)\n", "        \n", "        # Stack masks along new region dimension\n", "        mask_array = xr.concat(masks, dim=self.bin_dim_name)\n", "        mask_array[self.bin_dim_name] = region_names\n", "        \n", "        return mask_array\n", "\n", "\n", "class ByTimeUnit(Binning):\n", "    \"\"\"Bin by time units (hour, month, season) - temporal aggregation\"\"\"\n", "    \n", "    def __init__(self, time_dim: str, unit: str, bin_dim_name: str = None):\n", "        super().__init__(bin_dim_name or unit)\n", "        self.time_dim = time_dim\n", "        self.unit = unit\n", "    \n", "    def create_bin_mask(self, statistic: xr.DataArray) -> xr.<PERSON>Array:\n", "        \"\"\"Create mask for each time unit\"\"\"\n", "        time_coord = statistic[self.time_dim]\n", "        \n", "        if self.unit == 'season':\n", "            time_values = time_coord.dt.season\n", "            unique_values = ['DJ<PERSON>', 'MAM', 'JJ<PERSON>', 'SON']\n", "        elif self.unit == 'month':\n", "            time_values = time_coord.dt.month\n", "            unique_values = list(range(1, 13))\n", "        elif self.unit == 'hour':\n", "            time_values = time_coord.dt.hour\n", "            unique_values = list(range(0, 24, 6))  # 00, 06, 12, 18 UTC\n", "        else:\n", "            raise ValueError(f\"Unsupported time unit: {self.unit}\")\n", "        \n", "        masks = []\n", "        for value in unique_values:\n", "            mask = time_values == value\n", "            masks.append(mask)\n", "        \n", "        mask_array = xr.concat(masks, dim=self.bin_dim_name)\n", "        mask_array[self.bin_dim_name] = unique_values\n", "        \n", "        return mask_array\n", "\n", "\n", "# Demo: Binning System in Action\n", "print(\"🗺️ Binning System Demo\")\n", "print(\"=\" * 30)\n", "\n", "# Define regions\n", "regions = {\n", "    'global': ((-90, 90), (0, 360)),\n", "    'tropics': ((-30, 30), (0, 360)),\n", "    'northern_hemisphere': ((0, 90), (0, 360)),\n", "    'europe': ((35, 70), (-10, 40)),\n", "    'north_america': ((25, 70), (190, 300))\n", "}\n", "\n", "# Create binning objects\n", "region_binning = Regions(regions)\n", "season_binning = ByTimeUnit('time', 'season')\n", "\n", "# Apply to our weather data\n", "temp_data = weather_data.temperature.sel(lead_time='0 hours')\n", "\n", "# Create regional masks\n", "region_masks = region_binning.create_bin_mask(temp_data)\n", "print(f\"Regional masks shape: {region_masks.shape}\")\n", "print(f\"Regions: {list(region_masks.region.values)}\")\n", "\n", "# Create seasonal masks  \n", "season_masks = season_binning.create_bin_mask(temp_data)\n", "print(f\"\\nSeasonal masks shape: {season_masks.shape}\")\n", "print(f\"Seasons: {list(season_masks.season.values)}\")\n", "\n", "# Demonstrate mask application\n", "print(\"\\n🎯 Applying Masks:\")\n", "for region in regions.keys():\n", "    mask = region_masks.sel(region=region)\n", "    regional_temp = temp_data.where(mask)\n", "    mean_temp = regional_temp.mean(dim=['time', 'lat', 'lon'], skipna=True)\n", "    print(f\"   {region:20}: {mean_temp.values:.1f} K\")\n", "\n", "print(\"\\n✅ Binning system allows flexible multi-dimensional grouping!\")"]}, {"cell_type": "markdown", "metadata": {"id": "concept4"}, "source": ["# ⚖️ Concept 4: Weighting System - Statistical Rigor\n", "\n", "**Core Concept**: Proper weighting prevents statistical bias in global aggregations."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "weighting_system"}, "outputs": [], "source": ["class Weighting(ABC):\n", "    \"\"\"Base class for weighting schemes\"\"\"\n", "    \n", "    @abstractmethod\n", "    def weights(self, statistic: xr.<PERSON>) -> xr.<PERSON>y:\n", "        \"\"\"Return weights that broadcast against statistic\"\"\"\n", "        pass\n", "\n", "\n", "class GridAreaWeighting(Weighting):\n", "    \"\"\"Weight by grid cell area - essential for global statistics\"\"\"\n", "    \n", "    def __init__(self, latitude_name: str = 'lat'):\n", "        self.latitude_name = latitude_name\n", "    \n", "    def weights(self, statistic: xr.<PERSON>) -> xr.<PERSON>y:\n", "        \"\"\"Compute area weights proportional to cos(latitude)\"\"\"\n", "        if self.latitude_name not in statistic.dims:\n", "            return xr.<PERSON>(1.0)  # No weighting needed\n", "        \n", "        lat = statistic[self.latitude_name]\n", "        # Area weight proportional to cos(latitude)\n", "        weights = np.cos(np.deg2rad(lat))\n", "        \n", "        # Normalize to mean of 1 (optional, for interpretability)\n", "        weights = weights / weights.mean()\n", "        \n", "        return weights\n", "\n", "\n", "# Demo: Why Area Weighting Matters\n", "print(\"⚖️ Area Weighting Demo: Why This Matters\")\n", "print(\"=\" * 45)\n", "\n", "# Create simple temperature field\n", "temp_field = weather_data.temperature.sel(time='2024-07-01', lead_time='0 hours')\n", "\n", "# Method 1: Naive global average (WRONG!)\n", "naive_global_mean = temp_field.mean(dim=['lat', 'lon'])\n", "\n", "# Method 2: Area-weighted global average (CORRECT!)\n", "area_weighting = GridAreaWeighting()\n", "area_weights = area_weighting.weights(temp_field)\n", "\n", "# Use xr.dot for weighted average\n", "weighted_global_mean = xr.dot(temp_field, area_weights, dims=['lat', 'lon']) / area_weights.sum()\n", "\n", "print(f\"❌ Naive global mean:        {naive_global_mean.values:.2f} K\")\n", "print(f\"✅ Area-weighted global mean: {weighted_global_mean.values:.2f} K\")\n", "print(f\"📊 Difference:               {abs(naive_global_mean - weighted_global_mean).values:.2f} K\")\n", "\n", "# Visualize the weights\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "\n", "# Temperature field\n", "temp_field.plot(ax=axes[0], cmap='RdYlBu_r')\n", "axes[0].set_title('Temperature Field')\n", "\n", "# Area weights\n", "area_weights.plot(ax=axes[1], cmap='viridis')\n", "axes[1].set_title('Area Weights (cos(latitude))')\n", "\n", "# Weight distribution by latitude\n", "area_weights.plot(ax=axes[2])\n", "axes[2].set_title('Area Weight vs Latitude')\n", "axes[2].set_xlabel('Latitude')\n", "axes[2].set_ylabel('Area Weight')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n💡 Key Insight: Grid cells near poles are much smaller!\")\n", "print(\"   Without area weighting, polar regions get too much influence.\")\n", "print(\"   This is crucial for global climate statistics!\")"]}, {"cell_type": "markdown", "metadata": {"id": "concept5"}, "source": ["# 🎯 Concept 5: Complete Aggregator - Putting It All Together\n", "\n", "**Core Concept**: The Aggregator combines binning, weighting, and reduction into a unified system."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "aggregator_system"}, "outputs": [], "source": ["class Aggregator:\n", "    \"\"\"The heart of WeatherBench-X: drives reduction from per-point to aggregated statistics\"\"\"\n", "    \n", "    def __init__(self, \n", "                 reduce_dims: List[str],\n", "                 bin_by: Optional[List[Binning]] = None,\n", "                 weigh_by: Optional[List[Weighting]] = None,\n", "                 masked: bool = False,\n", "                 skipna: bool = True):\n", "        self.reduce_dims = reduce_dims\n", "        self.bin_by = bin_by or []\n", "        self.weigh_by = weigh_by or []\n", "        self.masked = masked\n", "        self.skipna = skipna\n", "    \n", "    def aggregation_fn(self, stat: xr.DataArray) -> Tuple[xr.DataArray, xr.DataArray]:\n", "        \"\"\"Core aggregation: returns (weighted_sum, weight_sum)\"\"\"\n", "        \n", "        # Start with the statistic\n", "        data = stat\n", "        weights = xr.ones_like(stat)\n", "        \n", "        # Apply weighting schemes\n", "        for weighting in self.weigh_by:\n", "            weight = weighting.weights(stat)\n", "            weights = weights * weight\n", "        \n", "        # Apply binning masks\n", "        for binning in self.bin_by:\n", "            bin_mask = binning.create_bin_mask(stat)\n", "            # Expand dimensions to match\n", "            data = data * bin_mask\n", "            weights = weights * bin_mask\n", "        \n", "        # Perform reduction\n", "        if self.skipna:\n", "            weighted_sum = (data * weights).sum(dim=self.reduce_dims, skipna=True)\n", "            weight_sum = weights.sum(dim=self.reduce_dims, skipna=True)\n", "        else:\n", "            weighted_sum = (data * weights).sum(dim=self.reduce_dims)\n", "            weight_sum = weights.sum(dim=self.reduce_dims)\n", "        \n", "        return weighted_sum, weight_sum\n", "    \n", "    def aggregate_statistics(self, statistics: Dict) -> AggregationState:\n", "        \"\"\"Aggregate a dictionary of statistics\"\"\"\n", "        sum_weighted_stats = {}\n", "        sum_weights = {}\n", "        \n", "        for stat_name, stat_data in statistics.items():\n", "            weighted_sum, weight_sum = self.aggregation_fn(stat_data)\n", "            sum_weighted_stats[stat_name] = weighted_sum\n", "            sum_weights[stat_name] = weight_sum\n", "        \n", "        return AggregationState(sum_weighted_stats, sum_weights)\n", "\n", "\n", "# Demo: Complete Aggregation System\n", "print(\"🎯 Complete Aggregation Demo\")\n", "print(\"=\" * 35)\n", "\n", "# Create sample statistics\n", "temp_data = weather_data.temperature.sel(lead_time='0 hours')\n", "statistics = {\n", "    'temperature': temp_data,\n", "    'temperature_squared': temp_data ** 2\n", "}\n", "\n", "# Set up complex aggregation\n", "regions = {\n", "    'global': ((-90, 90), (0, 360)),\n", "    'tropics': ((-30, 30), (0, 360)),\n", "    'northern_hemisphere': ((0, 90), (0, 360))\n", "}\n", "\n", "aggregator = Aggregator(\n", "    reduce_dims=['time', 'lat', 'lon'],\n", "    bin_by=[Regions(regions), ByTimeUnit('time', 'season')],\n", "    weigh_by=[GridAreaWeighting()],\n", "    skipna=True\n", ")\n", "\n", "# Perform aggregation\n", "agg_state = aggregator.aggregate_statistics(statistics)\n", "final_stats = agg_state.mean_statistics()\n", "\n", "print(\"📊 Aggregation Results:\")\n", "print(f\"   Result dimensions: {final_stats['temperature'].dims}\")\n", "print(f\"   Result shape: {final_stats['temperature'].shape}\")\n", "\n", "# Show some results\n", "temp_results = final_stats['temperature']\n", "print(\"\\n🌍 Regional & Seasonal Temperature Averages:\")\n", "for region in regions.keys():\n", "    for season in ['DJ<PERSON>', 'MA<PERSON>', 'JJA', 'SON']:\n", "        try:\n", "            temp = temp_results.sel(region=region, season=season)\n", "            print(f\"   {region:20} {season}: {temp.values:.1f} K\")\n", "        except KeyError:\n", "            continue\n", "\n", "print(\"\\n✅ Multi-dimensional aggregation complete!\")\n", "print(\"🚀 This is the power of WeatherBench-X's composable design!\")"]}, {"cell_type": "markdown", "metadata": {"id": "concept6"}, "source": ["# 🔄 Concept 6: Interpolation - Aligning Different Data Sources\n", "\n", "**Core Concept**: Align gridded forecasts with sparse observations through interpolation."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "interpolation_system"}, "outputs": [], "source": ["class Interpolation(ABC):\n", "    \"\"\"Base class for interpolation methods\"\"\"\n", "    \n", "    @abstractmethod\n", "    def interpolate(self, data: xr.<PERSON>, reference: xr.<PERSON>y) -> xr.DataArray:\n", "        \"\"\"Interpolate data to reference coordinates\"\"\"\n", "        pass\n", "\n", "\n", "class InterpolateToReferenceCoords(Interpolation):\n", "    \"\"\"Interpolate to coordinates of a reference dataset\"\"\"\n", "    \n", "    def __init__(self, method: str = 'linear', extrapolate: bool = True):\n", "        self.method = method\n", "        self.extrapolate = extrapolate\n", "    \n", "    def interpolate(self, data: xr.<PERSON>, reference: xr.<PERSON>y) -> xr.DataArray:\n", "        \"\"\"Interpolate gridded data to reference point locations\"\"\"\n", "        \n", "        # Extract coordinates from reference\n", "        target_coords = {}\n", "        if 'lat' in reference.coords and 'lat' in data.coords:\n", "            target_coords['lat'] = reference.lat\n", "        if 'lon' in reference.coords and 'lon' in data.coords:\n", "            target_coords['lon'] = reference.lon\n", "        \n", "        if not target_coords:\n", "            return data  # No interpolation needed\n", "        \n", "        # Handle longitude wrapping for global data\n", "        if 'lon' in target_coords:\n", "            # Ensure longitude is in [0, 360) range\n", "            data_lon = data.lon % 360\n", "            data = data.assign_coords(lon=data_lon)\n", "            \n", "            target_lon = target_coords['lon'] % 360\n", "            target_coords['lon'] = target_lon\n", "        \n", "        # Perform interpolation\n", "        kwargs = {'method': self.method}\n", "        if not self.extrapolate:\n", "            kwargs['bounds_error'] = True\n", "        \n", "        try:\n", "            interpolated = data.interp(**target_coords, **kwargs)\n", "            return interpolated\n", "        except Exception as e:\n", "            print(f\"Interpolation failed: {e}\")\n", "            return data\n", "\n", "\n", "# Demo: Interpolation in Action\n", "print(\"🔄 Interpolation Demo: Grid to Point Alignment\")\n", "print(\"=\" * 50)\n", "\n", "# Create gridded forecast data\n", "forecast_data = weather_data.temperature.sel(time='2024-07-01', lead_time='0 hours')\n", "print(f\"📊 Forecast grid shape: {forecast_data.shape}\")\n", "print(f\"   Lat range: {forecast_data.lat.min().values:.1f} to {forecast_data.lat.max().values:.1f}\")\n", "print(f\"   Lon range: {forecast_data.lon.min().values:.1f} to {forecast_data.lon.max().values:.1f}\")\n", "\n", "# Create synthetic weather station observations\n", "station_lats = np.array([40.7, 34.1, 41.9, 25.8, 47.6])  # NYC, LA, Chicago, Miami, Seattle\n", "station_lons = np.array([286.0, 241.8, 272.7, 279.8, 237.7])  # Convert to 0-360\n", "station_names = ['NYC', 'LA', 'Chicago', 'Miami', 'Seattle']\n", "\n", "# Create reference dataset (observations)\n", "observations = xr.<PERSON>(\n", "    np.random.normal(280, 5, len(station_lats)),\n", "    dims=['station'],\n", "    coords={\n", "        'station': station_names,\n", "        'lat': ('station', station_lats),\n", "        'lon': ('station', station_lons)\n", "    }\n", ")\n", "\n", "print(f\"\\n🏠 Weather stations: {len(station_lats)} locations\")\n", "for i, name in enumerate(station_names):\n", "    print(f\"   {name:10}: ({station_lats[i]:5.1f}°N, {station_lons[i]:6.1f}°E)\")\n", "\n", "# Perform interpolation\n", "interpolator = InterpolateToReferenceCoords(method='linear')\n", "interpolated_forecast = interpolator.interpolate(forecast_data, observations)\n", "\n", "print(f\"\\n🎯 Interpolated forecast shape: {interpolated_forecast.shape}\")\n", "print(f\"   Dimensions: {interpolated_forecast.dims}\")\n", "\n", "# Compare interpolated forecasts with observations\n", "print(\"\\n📊 Forecast vs Observations:\")\n", "print(\"   Station      Forecast   Observation   Difference\")\n", "print(\"   \" + \"-\" * 50)\n", "for i, station in enumerate(station_names):\n", "    forecast_val = interpolated_forecast.isel(station=i).values\n", "    obs_val = observations.isel(station=i).values\n", "    diff = forecast_val - obs_val\n", "    print(f\"   {station:10}   {forecast_val:8.1f}K   {obs_val:10.1f}K   {diff:8.1f}K\")\n", "\n", "print(\"\\n✅ Interpolation enables direct forecast-observation comparison!\")\n", "print(\"🎯 This solves the 'apples to oranges' problem in weather evaluation.\")"]}, {"cell_type": "markdown", "metadata": {"id": "concept7"}, "source": ["# 📈 Concept 7: Metrics System - Evaluation Scores\n", "\n", "**Core Concept**: Modular metrics that compute evaluation scores from pre-computed statistics."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "metrics_system"}, "outputs": [], "source": ["class Metric(ABC):\n", "    \"\"\"Base class for evaluation metrics\"\"\"\n", "    \n", "    @abstractmethod\n", "    def wanted_statistics(self) -> List[str]:\n", "        \"\"\"Return list of statistics this metric needs\"\"\"\n", "        pass\n", "    \n", "    @abstractmethod\n", "    def evaluate_from_statistics(self, statistics: Dict) -> xr.DataArray:\n", "        \"\"\"Compute metric from pre-computed statistics\"\"\"\n", "        pass\n", "\n", "\n", "class RMSE(Metric):\n", "    \"\"\"Root Mean Square Error\"\"\"\n", "    \n", "    def wanted_statistics(self) -> List[str]:\n", "        return ['squared_error']\n", "    \n", "    def evaluate_from_statistics(self, statistics: Dict) -> xr.DataArray:\n", "        return np.sqrt(statistics['squared_error'])\n", "\n", "\n", "class MAE(Metric):\n", "    \"\"\"Mean Absolute Error\"\"\"\n", "    \n", "    def wanted_statistics(self) -> List[str]:\n", "        return ['absolute_error']\n", "    \n", "    def evaluate_from_statistics(self, statistics: Dict) -> xr.DataArray:\n", "        return statistics['absolute_error']\n", "\n", "\n", "class Bias(Metric):\n", "    \"\"\"<PERSON> Bias (forecast - observation)\"\"\"\n", "    \n", "    def wanted_statistics(self) -> List[str]:\n", "        return ['error']\n", "    \n", "    def evaluate_from_statistics(self, statistics: Dict) -> xr.DataArray:\n", "        return statistics['error']\n", "\n", "\n", "class ACC(Metric):\n", "    \"\"\"Anomaly Correlation Coefficient\"\"\"\n", "    \n", "    def wanted_statistics(self) -> List[str]:\n", "        return ['forecast_anomaly', 'observation_anomaly', \n", "                'forecast_anomaly_squared', 'observation_anomaly_squared']\n", "    \n", "    def evaluate_from_statistics(self, statistics: Dict) -> xr.DataArray:\n", "        # ACC = cov(f_anom, o_anom) / sqrt(var(f_anom) * var(o_anom))\n", "        covariance = statistics['forecast_anomaly'] * statistics['observation_anomaly']\n", "        f_variance = statistics['forecast_anomaly_squared']\n", "        o_variance = statistics['observation_anomaly_squared']\n", "        \n", "        return covariance / np.sqrt(f_variance * o_variance)\n", "\n", "\n", "def compute_basic_statistics(predictions: xr.<PERSON>y, \n", "                           targets: xr.<PERSON>,\n", "                           climatology: Optional[xr.<PERSON>] = None) -> Dict[str, xr.<PERSON>rray]:\n", "    \"\"\"Compute basic statistics needed by metrics\"\"\"\n", "    \n", "    error = predictions - targets\n", "    \n", "    statistics = {\n", "        'squared_error': error ** 2,\n", "        'absolute_error': np.abs(error),\n", "        'error': error\n", "    }\n", "    \n", "    # Add anomaly statistics if climatology provided\n", "    if climatology is not None:\n", "        forecast_anomaly = predictions - climatology\n", "        observation_anomaly = targets - climatology\n", "        \n", "        statistics.update({\n", "            'forecast_anomaly': forecast_anomaly,\n", "            'observation_anomaly': observation_anomaly,\n", "            'forecast_anomaly_squared': forecast_anomaly ** 2,\n", "            'observation_anomaly_squared': observation_anomaly ** 2\n", "        })\n", "    \n", "    return statistics\n", "\n", "\n", "# Demo: Metrics System\n", "print(\"📈 Metrics System Demo\")\n", "print(\"=\" * 25)\n", "\n", "# Create synthetic forecast and observation data\n", "forecast = weather_data.temperature.sel(lead_time='24 hours')\n", "# Add some forecast error\n", "observation = forecast + xr.<PERSON>(\n", "    np.random.normal(0, 2, forecast.shape),\n", "    dims=forecast.dims,\n", "    coords=forecast.coords\n", ")\n", "\n", "# Compute climatology (needed for ACC)\n", "climatology = observation.groupby('time.dayofyear').mean()\n", "\n", "# Compute basic statistics\n", "statistics = compute_basic_statistics(forecast, observation, climatology)\n", "\n", "print(f\"📊 Computed {len(statistics)} basic statistics:\")\n", "for stat_name in statistics.keys():\n", "    print(f\"   - {stat_name}\")\n", "\n", "# Define metrics\n", "metrics = {\n", "    'rmse': RMS<PERSON>(),\n", "    'mae': MAE(),\n", "    'bias': <PERSON><PERSON>(),\n", "    'acc': ACC()\n", "}\n", "\n", "# Set up aggregation for global metrics\n", "aggregator = Aggregator(\n", "    reduce_dims=['time', 'lat', 'lon'],\n", "    weigh_by=[GridAreaWeighting()],\n", "    skipna=True\n", ")\n", "\n", "# Aggregate statistics\n", "agg_state = aggregator.aggregate_statistics(statistics)\n", "aggregated_stats = agg_state.mean_statistics()\n", "\n", "# Compute final metrics\n", "final_metrics = {}\n", "for metric_name, metric in metrics.items():\n", "    try:\n", "        needed_stats = {stat: aggregated_stats[stat] for stat in metric.wanted_statistics()}\n", "        final_metrics[metric_name] = metric.evaluate_from_statistics(needed_stats)\n", "    except KeyError as e:\n", "        print(f\"   ⚠️  Skipping {metric_name}: missing statistic {e}\")\n", "\n", "print(\"\\n🎯 Final Evaluation Metrics:\")\n", "for metric_name, metric_value in final_metrics.items():\n", "    if hasattr(metric_value, 'values'):\n", "        print(f\"   {metric_name.upper():4}: {metric_value.values:.3f}\")\n", "    else:\n", "        print(f\"   {metric_name.upper():4}: {metric_value:.3f}\")\n", "\n", "print(\"\\n✅ Modular metrics system enables flexible evaluation!\")"]}, {"cell_type": "markdown", "metadata": {"id": "concept8"}, "source": ["# 🚀 Concept 8: Complete WeatherBench-X Workflow\n", "\n", "**Core Concept**: Putting everything together in a production-ready evaluation pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "complete_workflow"}, "outputs": [], "source": ["def weatherbench_evaluation_pipeline(forecast_data: xr.Dataset,\n", "                                    observation_data: xr.Dataset,\n", "                                    regions: Dict[str, <PERSON><PERSON>],\n", "                                    metrics: Dict[str, Metric],\n", "                                    lead_times: List[str] = None) -> Dict[str, xr.Dataset]:\n", "    \"\"\"Complete WeatherBench-X evaluation pipeline\n", "    \n", "    This function demonstrates the full workflow:\n", "    1. Data alignment (interpolation)\n", "    2. Statistics computation\n", "    3. Multi-dimensional aggregation\n", "    4. Metrics evaluation\n", "    \"\"\"\n", "    \n", "    results = {}\n", "    \n", "    # Process each lead time\n", "    lead_times = lead_times or ['0 hours', '24 hours', '72 hours', '168 hours']\n", "    \n", "    for lead_time in lead_times:\n", "        print(f\"\\n🔄 Processing lead time: {lead_time}\")\n", "        \n", "        try:\n", "            # 1. Extract data for this lead time\n", "            forecast = forecast_data.temperature.sel(lead_time=lead_time)\n", "            target = observation_data.temperature.sel(lead_time='0 hours')  # Analysis\n", "            \n", "            # 2. Align data (interpolation if needed)\n", "            if forecast.dims != target.dims:\n", "                interpolator = InterpolateToReferenceCoords()\n", "                forecast = interpolator.interpolate(forecast, target)\n", "            \n", "            # 3. Compute climatology for anomaly-based metrics\n", "            climatology = target.groupby('time.dayofyear').mean()\n", "            \n", "            # 4. <PERSON><PERSON><PERSON> basic statistics\n", "            statistics = compute_basic_statistics(forecast, target, climatology)\n", "            \n", "            # 5. Set up multi-dimensional aggregation\n", "            aggregator = Aggregator(\n", "                reduce_dims=['time', 'lat', 'lon'],\n", "                bin_by=[\n", "                    Regions(regions),\n", "                    ByTimeUnit('time', 'season')\n", "                ],\n", "                weigh_by=[GridAreaWeighting()],\n", "                skipna=True\n", "            )\n", "            \n", "            # 6. Aggregate statistics\n", "            agg_state = aggregator.aggregate_statistics(statistics)\n", "            aggregated_stats = agg_state.mean_statistics()\n", "            \n", "            # 7. <PERSON><PERSON>ute final metrics\n", "            lead_time_metrics = {}\n", "            for metric_name, metric in metrics.items():\n", "                try:\n", "                    needed_stats = {stat: aggregated_stats[stat] \n", "                                  for stat in metric.wanted_statistics()}\n", "                    lead_time_metrics[metric_name] = metric.evaluate_from_statistics(needed_stats)\n", "                except KeyError:\n", "                    continue\n", "            \n", "            # 8. Convert to Dataset\n", "            results[lead_time] = xr.Dataset(lead_time_metrics)\n", "            \n", "            print(f\"   ✅ Computed {len(lead_time_metrics)} metrics\")\n", "            \n", "        except Exception as e:\n", "            print(f\"   ❌ Error processing {lead_time}: {e}\")\n", "            continue\n", "    \n", "    return results\n", "\n", "\n", "# Demo: Complete Workflow\n", "print(\"🚀 Complete WeatherBench-X Workflow Demo\")\n", "print(\"=\" * 45)\n", "\n", "# Prepare data\n", "forecast_dataset = weather_data\n", "observation_dataset = weather_data  # In reality, this would be different\n", "\n", "# Define evaluation setup\n", "regions = {\n", "    'global': ((-90, 90), (0, 360)),\n", "    'tropics': ((-30, 30), (0, 360)),\n", "    'northern_hemisphere': ((0, 90), (0, 360))\n", "}\n", "\n", "metrics = {\n", "    'rmse': RMS<PERSON>(),\n", "    'mae': MAE(),\n", "    'bias': <PERSON><PERSON>()\n", "}\n", "\n", "lead_times = ['0 hours', '24 hours', '72 hours']\n", "\n", "# Run complete evaluation\n", "print(\"🔄 Running complete evaluation pipeline...\")\n", "evaluation_results = weatherbench_evaluation_pipeline(\n", "    forecast_dataset, observation_dataset, regions, metrics, lead_times\n", ")\n", "\n", "# Display results\n", "print(\"\\n📊 Evaluation Results Summary:\")\n", "print(\"=\" * 35)\n", "\n", "for lead_time, results in evaluation_results.items():\n", "    print(f\"\\n⏰ Lead Time: {lead_time}\")\n", "    print(f\"   Dimensions: {list(results.dims.keys())}\")\n", "    print(f\"   Metrics: {list(results.data_vars.keys())}\")\n", "    \n", "    # Show sample results\n", "    if 'rmse' in results:\n", "        rmse_global = results.rmse.sel(region='global', season='JJA')\n", "        print(f\"   Global JJA RMSE: {rmse_global.values:.2f} K\")\n", "\n", "print(\"\\n🎯 Key Achievements:\")\n", "print(\"   ✅ Multi-dimensional aggregation (region × season)\")\n", "print(\"   ✅ Proper area weighting for global statistics\")\n", "print(\"   ✅ Modular metrics computation\")\n", "print(\"   ✅ Scalable, composable architecture\")\n", "print(\"   ✅ Production-ready evaluation pipeline\")\n", "\n", "print(\"\\n🚀 You now understand the complete WeatherBench-X system!\")\n", "print(\"   This is production-scale weather evaluation at Google level.\")"]}, {"cell_type": "markdown", "metadata": {"id": "interview_prep"}, "source": ["# 🎯 Interview Preparation: Key Concepts Mastery Check\n", "\n", "**Test your understanding with these key questions that might come up in your Gridmatic interview.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "interview_questions"}, "outputs": [], "source": ["print(\"🎯 INTERVIEW PREPARATION CHECKLIST\")\n", "print(\"=\" * 40)\n", "\n", "concepts_mastered = {\n", "    \"XArray Operations\": [\n", "        \"✅ Coordinate-based selection vs positional indexing\",\n", "        \"✅ Dimension-aware operations (mean, groupby, resample)\",\n", "        \"✅ Broadcasting and automatic alignment\",\n", "        \"✅ Lazy evaluation with dask integration\"\n", "    ],\n", "    \"Aggregation System\": [\n", "        \"✅ AggregationState pattern for distributed processing\",\n", "        \"✅ Why you can't just average averages\",\n", "        \"✅ Weighted sums and weight tracking\",\n", "        \"✅ Combinable intermediate states\"\n", "    ],\n", "    \"Binning Strategies\": [\n", "        \"✅ Boolean masks with new dimensions\",\n", "        \"✅ Regional binning (lat/lon boxes)\",\n", "        \"✅ Temporal binning (seasons, months, hours)\",\n", "        \"✅ Composable multi-dimensional grouping\"\n", "    ],\n", "    \"Weighting Systems\": [\n", "        \"✅ Grid area weighting with cos(latitude)\",\n", "        \"✅ Why polar regions need less weight\",\n", "        \"✅ Statistical bias in global averages\",\n", "        \"✅ Proper weighting for meaningful statistics\"\n", "    ],\n", "    \"Interpolation Methods\": [\n", "        \"✅ Grid-to-point alignment\",\n", "        \"✅ Solving the 'apples to oranges' problem\",\n", "        \"✅ Coordinate system handling\",\n", "        \"✅ Longitude wrapping and extrapolation\"\n", "    ],\n", "    \"Metrics Computation\": [\n", "        \"✅ Modular metric design\",\n", "        \"✅ Statistics-based evaluation\",\n", "        \"✅ RMSE, MAE, Bias, ACC computation\",\n", "        \"✅ Anomaly-based metrics\"\n", "    ],\n", "    \"Production Concepts\": [\n", "        \"✅ Scalable architecture design\",\n", "        \"✅ Fault-tolerant distributed processing\",\n", "        \"✅ Memory-efficient chunking strategies\",\n", "        \"✅ Composable, testable components\"\n", "    ]\n", "}\n", "\n", "for concept, items in concepts_mastered.items():\n", "    print(f\"\\n📚 {concept}:\")\n", "    for item in items:\n", "        print(f\"   {item}\")\n", "\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"🚀 INTERVIEW SUCCESS PATTERNS\")\n", "print(\"=\" * 50)\n", "\n", "success_patterns = [\n", "    \"🎯 Always use coordinate-based operations, never positional indexing\",\n", "    \"⚖️ Remember area weighting for any global/regional statistics\",\n", "    \"🔄 Design for distributed processing with combinable states\",\n", "    \"📊 Handle missing data gracefully with skipna parameters\",\n", "    \"🧩 Compose complex operations from simple, reusable components\",\n", "    \"📏 Think about scale: TB-PB datasets, memory efficiency\",\n", "    \"🔍 Understand the physics: why certain patterns exist\",\n", "    \"🛠️ Write clean, testable, maintainable code\"\n", "]\n", "\n", "for pattern in success_patterns:\n", "    print(f\"   {pattern}\")\n", "\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"💡 LIKELY INTERVIEW QUESTIONS\")\n", "print(\"=\" * 50)\n", "\n", "interview_questions = [\n", "    \"❓ How would you compute seasonal averages by region?\",\n", "    \"   → Use Regions + ByTimeUnit binning with proper area weighting\",\n", "    \"\",\n", "    \"❓ Why can't you just average regional averages?\",\n", "    \"   → Different regions have different areas/data points\",\n", "    \"\",\n", "    \"❓ How do you handle forecast grids vs weather station data?\",\n", "    \"   → Use InterpolateToReferenceCoords for spatial alignment\",\n", "    \"\",\n", "    \"❓ What's wrong with naive global temperature averages?\",\n", "    \"   → Grid cells near poles are much smaller, need cos(lat) weighting\",\n", "    \"\",\n", "    \"❓ How would you process data too large for memory?\",\n", "    \"   → Chunking + AggregationState pattern for combinable results\",\n", "    \"\",\n", "    \"❓ How do you add a new evaluation metric?\",\n", "    \"   → Inherit from Metric, implement wanted_statistics() and evaluate_from_statistics()\"\n", "]\n", "\n", "for question in interview_questions:\n", "    print(question)\n", "\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"🏆 CONGRATULATIONS!\")\n", "print(\"=\" * 50)\n", "print(\"You now understand production-scale weather data processing\")\n", "print(\"at the level used by Google for WeatherBench-X!\")\n", "print(\"\")\n", "print(\"🎯 You're ready for your Gridmatic interview!\")\n", "print(\"\")\n", "print(\"Key strengths you can demonstrate:\")\n", "print(\"• Deep understanding of multi-dimensional data processing\")\n", "print(\"• Knowledge of distributed computing patterns\")\n", "print(\"• Statistical rigor in scientific computing\")\n", "print(\"• Production-ready architecture thinking\")\n", "print(\"• Experience with real-world weather/climate data challenges\")\n", "print(\"\")\n", "print(\"🚀 Go ace that interview!\")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}