pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Silk Light
  Author: <PERSON> (https://github.com/Misterio77)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme silk-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #E9F1EF  Default Background
base01  #CCD4D3  Lighter Background (Used for status bars, line number and folding marks)
base02  #90B7B6  Selection Background
base03  #5C787B  Comments, Invisibles, Line Highlighting
base04  #4B5B5F  Dark Foreground (Used for status bars)
base05  #385156  Default Foreground, Caret, Delimiters, Operators
base06  #0e3c46  Light Foreground (Not often used)
base07  #D2FAFF  Light Background (Not often used)
base08  #CF432E  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #D27F46  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #CFAD25  Classes, Markup Bold, Search Text Background
base0B  #6CA38C  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #329CA2  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #39AAC9  Functions, Methods, Attribute IDs, Headings
base0E  #6E6582  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #865369  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #385156;
  background: #E9F1EF
}
.hljs::selection,
.hljs ::selection {
  background-color: #90B7B6;
  color: #385156
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #5C787B -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #5C787B
}
/* base04 - #4B5B5F -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #4B5B5F
}
/* base05 - #385156 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #385156
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #CF432E
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #D27F46
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #CFAD25
}
.hljs-strong {
  font-weight: bold;
  color: #CFAD25
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #6CA38C
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #329CA2
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #39AAC9
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #6E6582
}
.hljs-emphasis {
  color: #6E6582;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #865369
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}