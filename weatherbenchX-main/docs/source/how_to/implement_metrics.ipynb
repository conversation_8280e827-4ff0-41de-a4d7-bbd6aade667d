{"cells": [{"metadata": {}, "id": "92680298-ad35-455c-9bd5-7520d3aed4c8", "cell_type": "markdown", "source": ["# Implement a new metric"]}, {"metadata": {}, "id": "3f266261-8708-4f26-90f5-9b4ce611fb67", "cell_type": "code", "source": ["import numpy as np\n", "import xarray as xr\n", "from weatherbenchX.metrics import base\n", "from weatherbenchX.metrics import deterministic"], "outputs": [], "execution_count": 3}, {"metadata": {}, "id": "24fb4aa2-2710-4252-9495-2a9601c2a72d", "cell_type": "markdown", "source": ["Metrics in WeatherBench-X are defined by a set of statistics and instructions how to compute the final metrics value from the averaged statistics.\n", "\n", "Statistics are computed from the predictions and targets for each element. Further, statistics are divided into single variable statistics (computed separately for each variable; most common use case) and multi-variate statistics (where statistics are computed as a function of several variables).\n", "\n", "As a simple example, let's take the RMSE. Here, the statistic in the squared error which is a per-variable computation.\n", "\n", "```python\n", "class SquaredError(base.PerVariableStatistic):\n", "  \"\"\"Squared error between predictions and targets.\"\"\"\n", "\n", "  def compute_per_variable(\n", "      self,\n", "      predictions: <PERSON>r<PERSON>,\n", "      targets: xr.<PERSON>,\n", "  ) -> xr.<PERSON>:\n", "    return (predictions - targets) ** 2\n", "```\n", "\n", "The RMSE metric specifies the SquaredError statistic and takes the square root over it from the aggregated values.\n", "\n", "```python\n", "class RMSE(base.PerVariableMetric):\n", "  \"\"\"Root mean squared error.\"\"\"\n", "\n", "  @property\n", "  def statistics(self) -> Mapping[str, base.Statistic]:\n", "    return {'SquaredError': SquaredError()}\n", "\n", "  def _values_from_mean_statistics_per_variable(\n", "      self,\n", "      statistic_values: Mapping[str, xr.DataArray],\n", "  ) -> xr.<PERSON>:\n", "    \"\"\"Computes metrics from aggregated statistics.\"\"\"\n", "    return np.sqrt(statistic_values['SquaredError'])\n", "```"]}, {"metadata": {}, "id": "21ce9770-ea04-4657-a5b4-b806980982ef", "cell_type": "code", "source": ["predictions = xr.Dataset({'2m_temperature': xr.<PERSON>(np.ones((2, 32, 64)), dims=['init_time', 'latitude', 'longitude'])})\n", "targets = predictions.copy()\n", "predictions"], "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 33kB\n", "Dimensions:         (init_time: 2, latitude: 32, longitude: 64)\n", "Dimensions without coordinates: init_time, latitude, longitude\n", "Data variables:\n", "    2m_temperature  (init_time, latitude, longitude) float64 33kB 1.0 ... 1.0</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-11318182-dd65-4afc-989f-044d5486d668' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-11318182-dd65-4afc-989f-044d5486d668' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span>init_time</span>: 2</li><li><span>latitude</span>: 32</li><li><span>longitude</span>: 64</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-3bdfcd66-0a7d-473b-9320-c0c08c29aa5a' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-3bdfcd66-0a7d-473b-9320-c0c08c29aa5a' class='xr-section-summary'  title='Expand/collapse section'>Coordinates: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'></ul></div></li><li class='xr-section-item'><input id='section-5e51987b-d308-41b5-94ff-6b942ca43ac1' class='xr-section-summary-in' type='checkbox'  checked><label for='section-5e51987b-d308-41b5-94ff-6b942ca43ac1' class='xr-section-summary' >Data variables: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>2m_temperature</span></div><div class='xr-var-dims'>(init_time, latitude, longitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>1.0 1.0 1.0 1.0 ... 1.0 1.0 1.0 1.0</div><input id='attrs-787692e2-e078-49fe-ae71-e4667b0a7527' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-787692e2-e078-49fe-ae71-e4667b0a7527' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-44f41e28-5f17-47d2-b27e-6da97a3f6be1' class='xr-var-data-in' type='checkbox'><label for='data-44f41e28-5f17-47d2-b27e-6da97a3f6be1' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[[1., 1., 1., ..., 1., 1., 1.],\n", "        [1., 1., 1., ..., 1., 1., 1.],\n", "        [1., 1., 1., ..., 1., 1., 1.],\n", "        ...,\n", "        [1., 1., 1., ..., 1., 1., 1.],\n", "        [1., 1., 1., ..., 1., 1., 1.],\n", "        [1., 1., 1., ..., 1., 1., 1.]],\n", "\n", "       [[1., 1., 1., ..., 1., 1., 1.],\n", "        [1., 1., 1., ..., 1., 1., 1.],\n", "        [1., 1., 1., ..., 1., 1., 1.],\n", "        ...,\n", "        [1., 1., 1., ..., 1., 1., 1.],\n", "        [1., 1., 1., ..., 1., 1., 1.],\n", "        [1., 1., 1., ..., 1., 1., 1.]]])</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-2fdfc42c-a651-4036-8eed-54149141b173' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-2fdfc42c-a651-4036-8eed-54149141b173' class='xr-section-summary'  title='Expand/collapse section'>Indexes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'></ul></div></li><li class='xr-section-item'><input id='section-be35fe62-d0f8-478e-a76f-8da695f1a29d' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-be35fe62-d0f8-478e-a76f-8da695f1a29d' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 33kB\n", "Dimensions:         (init_time: 2, latitude: 32, longitude: 64)\n", "Dimensions without coordinates: init_time, latitude, longitude\n", "Data variables:\n", "    2m_temperature  (init_time, latitude, longitude) float64 33kB 1.0 ... 1.0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"metadata": {}, "id": "5b22a25c-50fd-4b25-af3a-8516de571a1a", "cell_type": "code", "source": ["rmse = deterministic.RMSE()"], "outputs": [], "execution_count": 5}, {"metadata": {}, "id": "05244ea0-b074-41a5-a657-8bbd249a1ce6", "cell_type": "code", "source": ["statistic_values = {name: statistic.compute(predictions, targets) for name, statistic in rmse.statistics.items()}\n", "statistic_values"], "outputs": [{"data": {"text/plain": ["{'SquaredError': {'2m_temperature': <xarray.DataArray '2m_temperature' (init_time: 2, latitude: 32, longitude: 64)> Size: 33kB\n", "  array([[[0., 0., 0., ..., 0., 0., 0.],\n", "          [0., 0., 0., ..., 0., 0., 0.],\n", "          [0., 0., 0., ..., 0., 0., 0.],\n", "          ...,\n", "          [0., 0., 0., ..., 0., 0., 0.],\n", "          [0., 0., 0., ..., 0., 0., 0.],\n", "          [0., 0., 0., ..., 0., 0., 0.]],\n", "  \n", "         [[0., 0., 0., ..., 0., 0., 0.],\n", "          [0., 0., 0., ..., 0., 0., 0.],\n", "          [0., 0., 0., ..., 0., 0., 0.],\n", "          ...,\n", "          [0., 0., 0., ..., 0., 0., 0.],\n", "          [0., 0., 0., ..., 0., 0., 0.],\n", "          [0., 0., 0., ..., 0., 0., 0.]]])\n", "  Dimensions without coordinates: init_time, latitude, longitude}}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "execution_count": 6}, {"metadata": {}, "id": "35f4b1c6-3f18-44a7-936e-27a457aa5d0d", "cell_type": "markdown", "source": ["Take the mean now. Here we do it explicitly for a single metrics. Typically, this would be done in `compute_unique_statistics_for_all_metrics`."]}, {"metadata": {}, "id": "d38e0380-a614-4a05-a3e3-986e0b7a11db", "cell_type": "code", "source": ["statistic_values['SquaredError'] = {k: v.mean() for k,v in statistic_values['SquaredError'].items()}\n", "statistic_values"], "outputs": [{"data": {"text/plain": ["{'SquaredError': {'2m_temperature': <xarray.DataArray '2m_temperature' ()> Size: 8B\n", "  array(0.)}}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "execution_count": 8}, {"metadata": {}, "id": "9837dfd4-f2ea-464d-a595-28e6a743f23b", "cell_type": "markdown", "source": ["Now we can compute the metric (in this case take the square root) from the averaged statistic."]}, {"metadata": {}, "id": "aacd1c8e-de65-490e-8d78-d35588d2ca81", "cell_type": "code", "source": ["rmse.values_from_mean_statistics(statistic_values)"], "outputs": [{"data": {"text/plain": ["{'2m_temperature': <xarray.DataArray '2m_temperature' ()> Size: 8B\n", " array(0.)}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "execution_count": 9}, {"metadata": {}, "id": "19c53917-64a3-4d39-bf7f-b62b3e127b2e", "cell_type": "markdown", "source": ["Note: Some metrics can have more than one statistic. See, for example, the ensemble CRPS implementation."]}, {"metadata": {}, "id": "86e78881-453a-499f-985e-bdb6c522c6a4", "cell_type": "code", "source": [], "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}