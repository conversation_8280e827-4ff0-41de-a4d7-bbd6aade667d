pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Classic Light
  Author: <PERSON> (http://heeris.id.au)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme classic-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #F5F5F5  Default Background
base01  #E0E0E0  Lighter Background (Used for status bars, line number and folding marks)
base02  #D0D0D0  Selection Background
base03  #B0B0B0  Comments, Invisibles, Line Highlighting
base04  #505050  Dark Foreground (Used for status bars)
base05  #303030  Default Foreground, Caret, Delimiters, Operators
base06  #202020  Light Foreground (Not often used)
base07  #151515  Light Background (Not often used)
base08  #AC4142  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #D28445  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #F4BF75  Classes, Markup Bold, Search Text Background
base0B  #90A959  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #75B5AA  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #6A9FB5  Functions, Methods, Attribute IDs, Headings
base0E  #AA759F  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #8F5536  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #303030;
  background: #F5F5F5
}
.hljs::selection,
.hljs ::selection {
  background-color: #D0D0D0;
  color: #303030
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #B0B0B0 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #B0B0B0
}
/* base04 - #505050 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #505050
}
/* base05 - #303030 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #303030
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #AC4142
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #D28445
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #F4BF75
}
.hljs-strong {
  font-weight: bold;
  color: #F4BF75
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #90A959
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #75B5AA
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #6A9FB5
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #AA759F
}
.hljs-emphasis {
  color: #AA759F;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #8F5536
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}