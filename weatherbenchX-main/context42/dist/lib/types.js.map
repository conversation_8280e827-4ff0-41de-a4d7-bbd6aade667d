{"version": 3, "sources": ["../../src/lib/types.ts"], "sourcesContent": ["import type { DB } from \"./database.js\"\n\n// === Callback Types ===\nexport type Callback<T> = (value: T) => void\nexport type AsyncCallback<T> = (value: T) => Promise<void>\n\n// === Language & File Types ===\n// Language is now just a string representing the file extension\nexport type Language = string\n\nexport type FileGroup = {\n  readonly directory: string\n  readonly language: Language\n  readonly files: readonly string[]\n}\n\n// === Database Types ===\nexport type DatabaseRow = {\n  id: number\n  created_at: string\n}\n\nexport type GeminiResponse = DatabaseRow & {\n  project_path: string\n  file_path: string\n  response: string\n  response_time: number\n}\n\nexport type StyleGuide = DatabaseRow & {\n  project_path: string\n  language: string\n  content: string\n}\n\n// === Worker Types ===\nexport type WorkerId = number\nexport type WorkerStatus = \"starting\" | \"idle\" | \"working\" | \"success\" | \"error\"\nexport type WorkerState = {\n  directory: string\n  language: string\n  files: number\n  processed: number\n  error?: string\n  progress?: string\n}\n\nexport type Worker = Partial<WorkerState> & {\n  id: WorkerId\n  status: WorkerStatus\n}\n\nexport type WorkerUpdate = Pick<\n  Worker,\n  \"status\" | \"directory\" | \"language\" | \"files\" | \"processed\" | \"error\" | \"progress\"\n>\n\n// === Options Types ===\nexport type BaseOptions = {\n  onProgress?: Callback<number>\n  onError?: Callback<Error>\n}\n\nexport type ExplorerOptions = {\n  directory: string\n  ignore?: readonly string[]\n}\n\nexport type ProcessorOptions = BaseOptions & {\n  concurrency: number\n  model: string\n  onProgress?: Callback<number>\n  onWorkerUpdate?: Callback<Worker>\n  onQueueUpdate?: Callback<QueuedTask[]>\n  inputDir: string\n  database: DB\n}\n\n// === Processor State & Context ===\nexport type ProcessorState = { completed: undefined; total: undefined } | { completed: number; total: number }\n\nexport type DirectoryContext = {\n  directory: string\n  subdirectories: string[]\n  processedStyleGuides: Map<string, string> // language -> style guide content\n}\n\n// === Processor Run & Interface Types ===\nexport type ProcessorRunOptions = {\n  fileGroups: Map<Language, FileGroup[]>\n  inputDir: string\n  outputDir: string\n}\n\nexport type QueuedTask = {\n  id: string\n  language: Language\n  directory: string\n  status: \"ready\" | \"waiting\"\n  pendingDeps?: number\n}\n\nexport type Processor = ProcessorState & {\n  run(options: ProcessorRunOptions): Promise<Map<Language, string>>\n  reset(): void\n  workers: Worker[]\n  queuedTasks: QueuedTask[]\n}\n\n// === CLI Phases ===\nexport const CLI_PHASES = [\"explore\", \"process\", \"complete\", \"error\"] as const\nexport type CLIPhase = (typeof CLI_PHASES)[number]\n"], "mappings": "AA8GO,MAAM,aAAa,CAAC,WAAW,WAAW,YAAY,OAAO;", "names": []}