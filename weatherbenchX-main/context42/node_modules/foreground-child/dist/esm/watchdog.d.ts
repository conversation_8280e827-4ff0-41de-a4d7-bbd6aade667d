import { ChildProcess } from 'child_process';
/**
 * Pass in a ChildProcess, and this will spawn a watchdog process that
 * will make sure it exits if the parent does, thus preventing any
 * dangling detached zombie processes.
 *
 * If the child ends before the parent, then the watchdog will terminate.
 */
export declare const watchdog: (child: ChildProcess) => ChildProcess;
//# sourceMappingURL=watchdog.d.ts.map