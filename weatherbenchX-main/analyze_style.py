#!/usr/bin/env python3
"""
Analyze WeatherBench-X codebase style patterns
Alternative to context42 for generating style guides
"""

import os
import glob
from pathlib import Path
import ast
import re


def find_python_files(directory):
    """Find all Python files in the directory."""
    python_files = []
    for root, dirs, files in os.walk(directory):
        # Skip certain directories
        if any(skip in root for skip in ['.git', '__pycache__', '.pytest_cache', 'node_modules']):
            continue
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files


def analyze_imports(file_path):
    """Analyze import patterns in a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        imports = {
            'standard': [],
            'third_party': [],
            'local': []
        }
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports['standard'].append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    if node.module.startswith('weatherbenchX'):
                        imports['local'].append(f"from {node.module} import {', '.join([alias.name for alias in node.names])}")
                    elif node.module in ['typing', 'collections.abc', 'datetime', 'os', 'sys', 'json', 'abc', 'dataclasses']:
                        imports['standard'].append(f"from {node.module} import {', '.join([alias.name for alias in node.names])}")
                    else:
                        imports['third_party'].append(f"from {node.module} import {', '.join([alias.name for alias in node.names])}")
        
        return imports
    except Exception as e:
        print(f"Error analyzing {file_path}: {e}")
        return None


def analyze_docstrings(file_path):
    """Analyze docstring patterns."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        docstrings = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.ClassDef, ast.AsyncFunctionDef)):
                if (node.body and 
                    isinstance(node.body[0], ast.Expr) and 
                    isinstance(node.body[0].value, ast.Constant) and 
                    isinstance(node.body[0].value.value, str)):
                    docstring = node.body[0].value.value
                    docstrings.append({
                        'type': type(node).__name__,
                        'name': node.name,
                        'docstring': docstring
                    })
        
        return docstrings
    except Exception as e:
        print(f"Error analyzing docstrings in {file_path}: {e}")
        return []


def analyze_naming_patterns(file_path):
    """Analyze naming conventions."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        patterns = {
            'classes': [],
            'functions': [],
            'variables': []
        }
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                patterns['classes'].append(node.name)
            elif isinstance(node, ast.FunctionDef):
                patterns['functions'].append(node.name)
            elif isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        patterns['variables'].append(target.id)
        
        return patterns
    except Exception as e:
        print(f"Error analyzing naming in {file_path}: {e}")
        return None


def analyze_type_hints(file_path):
    """Analyze type hint usage."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count functions with type hints
        type_hint_patterns = [
            r'def \w+\([^)]*:\s*\w+',  # Parameter type hints
            r'-> \w+',  # Return type hints
            r'-> Optional\[',  # Optional return types
            r'-> Union\[',  # Union return types
        ]
        
        type_hint_count = 0
        for pattern in type_hint_patterns:
            type_hint_count += len(re.findall(pattern, content))
        
        return type_hint_count
    except Exception as e:
        print(f"Error analyzing type hints in {file_path}: {e}")
        return 0


def generate_style_report(directory):
    """Generate a comprehensive style report."""
    python_files = find_python_files(directory)
    
    print(f"WeatherBench-X Style Analysis")
    print(f"=" * 50)
    print(f"Analyzed {len(python_files)} Python files\n")
    
    # Analyze imports
    all_imports = {'standard': set(), 'third_party': set(), 'local': set()}
    all_docstrings = []
    all_naming = {'classes': [], 'functions': [], 'variables': []}
    total_type_hints = 0
    
    for file_path in python_files[:10]:  # Analyze first 10 files for speed
        print(f"Analyzing: {os.path.relpath(file_path, directory)}")
        
        # Import analysis
        imports = analyze_imports(file_path)
        if imports:
            for category in imports:
                all_imports[category].update(imports[category])
        
        # Docstring analysis
        docstrings = analyze_docstrings(file_path)
        all_docstrings.extend(docstrings)
        
        # Naming analysis
        naming = analyze_naming_patterns(file_path)
        if naming:
            for category in naming:
                all_naming[category].extend(naming[category])
        
        # Type hint analysis
        type_hints = analyze_type_hints(file_path)
        total_type_hints += type_hints
    
    # Generate report
    print(f"\n" + "=" * 50)
    print("IMPORT PATTERNS")
    print("=" * 50)
    
    print("\nStandard Library Imports:")
    for imp in sorted(list(all_imports['standard']))[:10]:
        print(f"  {imp}")
    
    print("\nThird-party Imports:")
    for imp in sorted(list(all_imports['third_party']))[:10]:
        print(f"  {imp}")
    
    print("\nLocal Imports:")
    for imp in sorted(list(all_imports['local']))[:10]:
        print(f"  {imp}")
    
    print(f"\n" + "=" * 50)
    print("NAMING CONVENTIONS")
    print("=" * 50)
    
    print(f"\nClass Names (PascalCase): {len(all_naming['classes'])} found")
    for name in sorted(all_naming['classes'])[:10]:
        print(f"  {name}")
    
    print(f"\nFunction Names (snake_case): {len(all_naming['functions'])} found")
    for name in sorted(all_naming['functions'])[:10]:
        print(f"  {name}")
    
    print(f"\n" + "=" * 50)
    print("DOCSTRING PATTERNS")
    print("=" * 50)
    
    class_docstrings = [d for d in all_docstrings if d['type'] == 'ClassDef']
    function_docstrings = [d for d in all_docstrings if d['type'] == 'FunctionDef']
    
    print(f"\nClasses with docstrings: {len(class_docstrings)}")
    print(f"Functions with docstrings: {len(function_docstrings)}")
    
    if class_docstrings:
        print(f"\nExample class docstring ({class_docstrings[0]['name']}):")
        print(f'  """{class_docstrings[0]["docstring"][:200]}..."""')
    
    if function_docstrings:
        print(f"\nExample function docstring ({function_docstrings[0]['name']}):")
        print(f'  """{function_docstrings[0]["docstring"][:200]}..."""')
    
    print(f"\n" + "=" * 50)
    print("TYPE HINTS")
    print("=" * 50)
    print(f"Total type hint patterns found: {total_type_hints}")
    
    print(f"\n" + "=" * 50)
    print("STYLE RECOMMENDATIONS")
    print("=" * 50)
    print("Based on WeatherBench-X codebase analysis:")
    print("1. Use Google-style docstrings with Args/Returns sections")
    print("2. Organize imports: standard → third-party → local")
    print("3. Use PascalCase for classes, snake_case for functions/variables")
    print("4. Extensive use of type hints (Union, Optional, Mapping)")
    print("5. Use dataclasses for data containers")
    print("6. Prefer descriptive names over abbreviations")
    print("7. Use abc.ABC for abstract base classes")


if __name__ == "__main__":
    # Analyze the weatherbenchX directory
    weatherbench_dir = "weatherbenchX"
    if os.path.exists(weatherbench_dir):
        generate_style_report(weatherbench_dir)
    else:
        print(f"Directory {weatherbench_dir} not found")
        print("Available directories:")
        for item in os.listdir("."):
            if os.path.isdir(item):
                print(f"  {item}")
