{"version": 3, "sources": ["../../src/components/Table.tsx"], "sourcesContent": ["/*\n * Modern implementation of ink-table\n *\n * Thanks to https://github.com/maticzav/ink-table/blob/master/src/index.tsx\n */\n\nimport { Box, Text } from \"ink\"\nimport { sha1 } from \"object-hash\"\nimport type React from \"react\"\nimport { useCallback, useMemo } from \"react\"\n\n/* Table */\n\ntype Scalar = string | number | boolean | null | undefined\n\ntype ScalarDict = {\n  [key: string]: Scalar\n}\n\nexport type CellProps = React.PropsWithChildren<{ column: number }>\n\nexport type TableProps<T extends ScalarDict> = {\n  /**\n   * List of values (rows).\n   */\n  data: T[]\n  /**\n   * Columns that we should display in the table.\n   */\n  columns: (keyof T)[]\n  /**\n   * Cell padding.\n   */\n  padding: number\n  /**\n   * Custom widths for columns. If provided, text will be truncated to fit.\n   */\n  columnWidths?: Record<keyof T, number>\n  /**\n   * Header component.\n   */\n  header: (props: React.PropsWithChildren) => React.JSX.Element\n  /**\n   * Component used to render a cell in the table.\n   */\n  cell: (props: CellProps) => React.JSX.Element\n  /**\n   * Component used to render the skeleton of the table.\n   */\n  skeleton: (props: React.PropsWithChildren) => React.JSX.Element\n}\n\n/* Table */\n\nconst Table = <T extends ScalarDict>(props: Pick<TableProps<T>, \"data\"> & Partial<TableProps<T>>) => {\n  /* Config */\n\n  /**\n   * Gets all keys used in data by traversing through the data.\n   */\n  const getDataKeys = useCallback((): (keyof T)[] => {\n    const keys = new Set<keyof T>()\n\n    // Collect all the keys.\n    for (const data of props.data) {\n      for (const key in data) {\n        keys.add(key)\n      }\n    }\n\n    return Array.from(keys)\n  }, [props.data])\n\n  /**\n   * Merges provided configuration with defaults.\n   */\n  const getConfig = useMemo((): TableProps<T> => {\n    return {\n      data: props.data,\n      columns: props.columns || getDataKeys(),\n      padding: props.padding || 1,\n      columnWidths: props.columnWidths,\n      header: props.header || Header,\n      cell: props.cell || Cell,\n      skeleton: props.skeleton || Skeleton,\n    }\n  }, [\n    props.data,\n    props.columns,\n    props.padding,\n    props.columnWidths,\n    props.header,\n    props.cell,\n    props.skeleton,\n    getDataKeys,\n  ])\n\n  /**\n   * Calculates the width of each column by finding\n   * the longest value in a cell of a particular column.\n   *\n   * Returns a list of column names and their widths.\n   */\n  const getColumns = useMemo((): Column<T>[] => {\n    const { columns, padding, columnWidths: widths } = getConfig\n\n    const widthsMap: Column<T>[] = columns.map(key => {\n      // Use custom width if provided\n      if (widths && widths[key] !== undefined) {\n        return {\n          column: key,\n          width: widths[key],\n          key: String(key),\n        }\n      }\n\n      // Calculate width automatically\n      const header = String(key).length\n      /* Get the width of each cell in the column */\n      const data = props.data.map(data => {\n        const value = data[key]\n\n        if (value === undefined || value === null) return 0\n        return String(value).length\n      })\n\n      const width = Math.max(...data, header) + padding * 2\n\n      /* Construct a cell */\n      return {\n        column: key,\n        width: width,\n        key: String(key),\n      }\n    })\n\n    return widthsMap\n  }, [getConfig, props.data])\n\n  /**\n   * Returns a (data) row representing the headings.\n   */\n  const getHeadings = useMemo((): Partial<T> => {\n    const { columns } = getConfig\n\n    // biome-ignore lint/performance/noAccumulatingSpread: whatever\n    const headings: Partial<T> = columns.reduce((acc, column) => ({ ...acc, [column]: column }), {})\n\n    return headings\n  }, [getConfig])\n\n  /* Rendering utilities */\n\n  // The top most line in the table.\n  const header = useMemo(\n    () =>\n      row<T>({\n        cell: getConfig.skeleton,\n        padding: getConfig.padding,\n        skeleton: {\n          component: getConfig.skeleton,\n          // chars\n          line: \"─\",\n          left: \"┌\",\n          right: \"┐\",\n          cross: \"┬\",\n        },\n      }),\n    [getConfig],\n  )\n\n  // The line with column names.\n  const heading = useMemo(\n    () =>\n      row<T>({\n        cell: getConfig.header,\n        padding: getConfig.padding,\n        skeleton: {\n          component: getConfig.skeleton,\n          // chars\n          line: \" \",\n          left: \"│\",\n          right: \"│\",\n          cross: \"│\",\n        },\n      }),\n    [getConfig],\n  )\n\n  // The line that separates rows.\n  const separator = useMemo(\n    () =>\n      row<T>({\n        cell: getConfig.skeleton,\n        padding: getConfig.padding,\n        skeleton: {\n          component: getConfig.skeleton,\n          // chars\n          line: \"─\",\n          left: \"├\",\n          right: \"┤\",\n          cross: \"┼\",\n        },\n      }),\n    [getConfig],\n  )\n\n  // The row with the data.\n  const data = useMemo(\n    () =>\n      row<T>({\n        cell: getConfig.cell,\n        padding: getConfig.padding,\n        skeleton: {\n          component: getConfig.skeleton,\n          // chars\n          line: \" \",\n          left: \"│\",\n          right: \"│\",\n          cross: \"│\",\n        },\n      }),\n    [getConfig],\n  )\n\n  // The bottom most line of the table.\n  const footer = useMemo(\n    () =>\n      row<T>({\n        cell: getConfig.skeleton,\n        padding: getConfig.padding,\n        skeleton: {\n          component: getConfig.skeleton,\n          // chars\n          line: \"─\",\n          left: \"└\",\n          right: \"┘\",\n          cross: \"┴\",\n        },\n      }),\n    [getConfig],\n  )\n\n  /* Render */\n\n  /* Data */\n  const columns = getColumns\n  const headings = getHeadings\n\n  /**\n   * Render the table line by line.\n   */\n  return (\n    <Box flexDirection=\"column\">\n      {/* Header */}\n      {header({ key: \"header\", columns, data: {} })}\n      {heading({ key: \"heading\", columns, data: headings })}\n      {/* Data */}\n      {props.data.map((row, index) => {\n        // Calculate the hash of the row based on its value and position\n        const key = `row-${sha1(row)}-${index}`\n\n        // Construct a row.\n        return (\n          <Box flexDirection=\"column\" key={key}>\n            {separator({ key: `separator-${key}`, columns, data: {} })}\n            {data({ key: `data-${key}`, columns, data: row })}\n          </Box>\n        )\n      })}\n      {/* Footer */}\n      {footer({ key: \"footer\", columns, data: {} })}\n    </Box>\n  )\n}\n\nexport default Table\n\n/* Helper components */\n\ntype RowConfig = {\n  /**\n   * Component used to render cells.\n   */\n  cell: (props: CellProps) => React.JSX.Element\n  /**\n   * Tells the padding of each cell.\n   */\n  padding: number\n  /**\n   * Component used to render skeleton in the row.\n   */\n  skeleton: {\n    component: (props: React.PropsWithChildren) => React.JSX.Element\n    /**\n     * Characters used in skeleton.\n     *    |             |\n     * (left)-(line)-(cross)-(line)-(right)\n     *    |             |\n     */\n    left: string\n    right: string\n    cross: string\n    line: string\n  }\n}\n\ntype RowProps<T extends ScalarDict> = {\n  key: string\n  data: Partial<T>\n  columns: Column<T>[]\n}\n\ntype Column<T> = {\n  key: string\n  column: keyof T\n  width: number\n}\n\n/**\n * Constructs a Row element from the configuration.\n */\nconst row = <T extends ScalarDict>(config: RowConfig): ((props: RowProps<T>) => React.JSX.Element) => {\n  /* This is a component builder. We return a function. */\n\n  const skeleton = config.skeleton\n\n  /* Row */\n  return props => (\n    <Box flexDirection=\"row\">\n      {/* Left */}\n      <skeleton.component>{skeleton.left}</skeleton.component>\n      {/* Data */}\n      {...intersperse(\n        i => {\n          const key = `${props.key}-hseparator-${i}`\n\n          // The horizontal separator.\n          return <skeleton.component key={key}>{skeleton.cross}</skeleton.component>\n        },\n\n        // Values.\n        props.columns.map((column, colI) => {\n          // content\n          const value = props.data[column.column]\n\n          if (value === undefined || value === null) {\n            const key = `${props.key}-empty-${column.key}`\n\n            return (\n              <config.cell key={key} column={colI}>\n                {skeleton.line.repeat(column.width)}\n              </config.cell>\n            )\n          }\n          const key = `${props.key}-cell-${column.key}`\n\n          // Truncate text if it's too long for the column\n          const originalText = String(value)\n          const truncatedText = truncateText(originalText, column.width, config.padding)\n\n          // margins\n          const ml = config.padding\n          const mr = column.width - truncatedText.length - config.padding\n\n          return (\n            /* prettier-ignore */\n            <config.cell key={key} column={colI}>\n              {`${skeleton.line.repeat(ml)}${truncatedText}${skeleton.line.repeat(mr)}`}\n            </config.cell>\n          )\n        }),\n      )}\n      {/* Right */}\n      <skeleton.component>{skeleton.right}</skeleton.component>\n    </Box>\n  )\n}\n\n/**\n * Renders the header of a table.\n */\nexport const Header = (props: React.PropsWithChildren) => (\n  <Text bold color=\"blue\">\n    {props.children}\n  </Text>\n)\n\n/**\n * Renders a cell in the table.\n */\nexport const Cell = (props: CellProps) => <Text>{props.children}</Text>\n\n/**\n * Renders the scaffold of the table.\n */\nexport const Skeleton = (props: React.PropsWithChildren) => <Text bold>{props.children}</Text>\n\n/* Utility functions */\n\n/**\n * Truncates text to fit within specified width, adding ellipsis if needed.\n */\nconst truncateText = (text: string, maxWidth: number, padding: number): string => {\n  const availableWidth = maxWidth - padding * 2\n  if (text.length <= availableWidth) return text\n  if (availableWidth <= 3) return \"...\".slice(0, availableWidth)\n  return `${text.slice(0, availableWidth - 3)}...`\n}\n\n/**\n * Intersperses a list of elements with another element.\n */\nconst intersperse = <T, I>(intersperser: (index: number) => I, elements: T[]): (T | I)[] =>\n  // Intersperse by reducing from left.\n  elements.reduce(\n    (acc, element, index) => {\n      // Only add element if it's the first one.\n      if (acc.length === 0) return [element]\n      // Add the intersparser as well otherwise.\n      acc.push(intersperser(index), element)\n      return acc\n    },\n    [] as (T | I)[],\n  )\n"], "mappings": "AAMA,SAAS,KAAK,YAAY;AAC1B,SAAS,YAAY;AAErB,SAAS,aAAa,eAAe;AA+P3B,SAmEJ,KAnEI;AAlNV,MAAM,QAAQ,CAAuB,UAAgE;AAMnG,QAAM,cAAc,YAAY,MAAmB;AACjD,UAAM,OAAO,oBAAI,IAAa;AAG9B,eAAWA,SAAQ,MAAM,MAAM;AAC7B,iBAAW,OAAOA,OAAM;AACtB,aAAK,IAAI,GAAG;AAAA,MACd;AAAA,IACF;AAEA,WAAO,MAAM,KAAK,IAAI;AAAA,EACxB,GAAG,CAAC,MAAM,IAAI,CAAC;AAKf,QAAM,YAAY,QAAQ,MAAqB;AAC7C,WAAO;AAAA,MACL,MAAM,MAAM;AAAA,MACZ,SAAS,MAAM,WAAW,YAAY;AAAA,MACtC,SAAS,MAAM,WAAW;AAAA,MAC1B,cAAc,MAAM;AAAA,MACpB,QAAQ,MAAM,UAAU;AAAA,MACxB,MAAM,MAAM,QAAQ;AAAA,MACpB,UAAU,MAAM,YAAY;AAAA,IAC9B;AAAA,EACF,GAAG;AAAA,IACD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN;AAAA,EACF,CAAC;AAQD,QAAM,aAAa,QAAQ,MAAmB;AAC5C,UAAM,EAAE,SAAAC,UAAS,SAAS,cAAc,OAAO,IAAI;AAEnD,UAAM,YAAyBA,SAAQ,IAAI,SAAO;AAEhD,UAAI,UAAU,OAAO,GAAG,MAAM,QAAW;AACvC,eAAO;AAAA,UACL,QAAQ;AAAA,UACR,OAAO,OAAO,GAAG;AAAA,UACjB,KAAK,OAAO,GAAG;AAAA,QACjB;AAAA,MACF;AAGA,YAAMC,UAAS,OAAO,GAAG,EAAE;AAE3B,YAAMF,QAAO,MAAM,KAAK,IAAI,CAAAA,UAAQ;AAClC,cAAM,QAAQA,MAAK,GAAG;AAEtB,YAAI,UAAU,UAAa,UAAU,KAAM,QAAO;AAClD,eAAO,OAAO,KAAK,EAAE;AAAA,MACvB,CAAC;AAED,YAAM,QAAQ,KAAK,IAAI,GAAGA,OAAME,OAAM,IAAI,UAAU;AAGpD,aAAO;AAAA,QACL,QAAQ;AAAA,QACR;AAAA,QACA,KAAK,OAAO,GAAG;AAAA,MACjB;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT,GAAG,CAAC,WAAW,MAAM,IAAI,CAAC;AAK1B,QAAM,cAAc,QAAQ,MAAkB;AAC5C,UAAM,EAAE,SAAAD,SAAQ,IAAI;AAGpB,UAAME,YAAuBF,SAAQ,OAAO,CAAC,KAAK,YAAY,EAAE,GAAG,KAAK,CAAC,MAAM,GAAG,OAAO,IAAI,CAAC,CAAC;AAE/F,WAAOE;AAAA,EACT,GAAG,CAAC,SAAS,CAAC;AAKd,QAAM,SAAS;AAAA,IACb,MACE,IAAO;AAAA,MACL,MAAM,UAAU;AAAA,MAChB,SAAS,UAAU;AAAA,MACnB,UAAU;AAAA,QACR,WAAW,UAAU;AAAA;AAAA,QAErB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,IACH,CAAC,SAAS;AAAA,EACZ;AAGA,QAAM,UAAU;AAAA,IACd,MACE,IAAO;AAAA,MACL,MAAM,UAAU;AAAA,MAChB,SAAS,UAAU;AAAA,MACnB,UAAU;AAAA,QACR,WAAW,UAAU;AAAA;AAAA,QAErB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,IACH,CAAC,SAAS;AAAA,EACZ;AAGA,QAAM,YAAY;AAAA,IAChB,MACE,IAAO;AAAA,MACL,MAAM,UAAU;AAAA,MAChB,SAAS,UAAU;AAAA,MACnB,UAAU;AAAA,QACR,WAAW,UAAU;AAAA;AAAA,QAErB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,IACH,CAAC,SAAS;AAAA,EACZ;AAGA,QAAM,OAAO;AAAA,IACX,MACE,IAAO;AAAA,MACL,MAAM,UAAU;AAAA,MAChB,SAAS,UAAU;AAAA,MACnB,UAAU;AAAA,QACR,WAAW,UAAU;AAAA;AAAA,QAErB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,IACH,CAAC,SAAS;AAAA,EACZ;AAGA,QAAM,SAAS;AAAA,IACb,MACE,IAAO;AAAA,MACL,MAAM,UAAU;AAAA,MAChB,SAAS,UAAU;AAAA,MACnB,UAAU;AAAA,QACR,WAAW,UAAU;AAAA;AAAA,QAErB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,IACH,CAAC,SAAS;AAAA,EACZ;AAKA,QAAM,UAAU;AAChB,QAAM,WAAW;AAKjB,SACE,qBAAC,OAAI,eAAc,UAEhB;AAAA,WAAO,EAAE,KAAK,UAAU,SAAS,MAAM,CAAC,EAAE,CAAC;AAAA,IAC3C,QAAQ,EAAE,KAAK,WAAW,SAAS,MAAM,SAAS,CAAC;AAAA,IAEnD,MAAM,KAAK,IAAI,CAACC,MAAK,UAAU;AAE9B,YAAM,MAAM,OAAO,KAAKA,IAAG,CAAC,IAAI,KAAK;AAGrC,aACE,qBAAC,OAAI,eAAc,UAChB;AAAA,kBAAU,EAAE,KAAK,aAAa,GAAG,IAAI,SAAS,MAAM,CAAC,EAAE,CAAC;AAAA,QACxD,KAAK,EAAE,KAAK,QAAQ,GAAG,IAAI,SAAS,MAAMA,KAAI,CAAC;AAAA,WAFjB,GAGjC;AAAA,IAEJ,CAAC;AAAA,IAEA,OAAO,EAAE,KAAK,UAAU,SAAS,MAAM,CAAC,EAAE,CAAC;AAAA,KAC9C;AAEJ;AAEA,IAAO,gBAAQ;AA8Cf,MAAM,MAAM,CAAuB,WAAmE;AAGpG,QAAM,WAAW,OAAO;AAGxB,SAAO,WACL,qBAAC,OAAI,eAAc,OAEjB;AAAA,wBAAC,SAAS,WAAT,EAAoB,mBAAS,MAAK;AAAA,IAElC,GAAG;AAAA,MACF,OAAK;AACH,cAAM,MAAM,GAAG,MAAM,GAAG,eAAe,CAAC;AAGxC,eAAO,oBAAC,SAAS,WAAT,EAA8B,mBAAS,SAAf,GAAqB;AAAA,MACvD;AAAA;AAAA,MAGA,MAAM,QAAQ,IAAI,CAAC,QAAQ,SAAS;AAElC,cAAM,QAAQ,MAAM,KAAK,OAAO,MAAM;AAEtC,YAAI,UAAU,UAAa,UAAU,MAAM;AACzC,gBAAMC,OAAM,GAAG,MAAM,GAAG,UAAU,OAAO,GAAG;AAE5C,iBACE,oBAAC,OAAO,MAAP,EAAsB,QAAQ,MAC5B,mBAAS,KAAK,OAAO,OAAO,KAAK,KADlBA,IAElB;AAAA,QAEJ;AACA,cAAM,MAAM,GAAG,MAAM,GAAG,SAAS,OAAO,GAAG;AAG3C,cAAM,eAAe,OAAO,KAAK;AACjC,cAAM,gBAAgB,aAAa,cAAc,OAAO,OAAO,OAAO,OAAO;AAG7E,cAAM,KAAK,OAAO;AAClB,cAAM,KAAK,OAAO,QAAQ,cAAc,SAAS,OAAO;AAExD;AAAA;AAAA,UAEE,oBAAC,OAAO,MAAP,EAAsB,QAAQ,MAC5B,aAAG,SAAS,KAAK,OAAO,EAAE,CAAC,GAAG,aAAa,GAAG,SAAS,KAAK,OAAO,EAAE,CAAC,MADvD,GAElB;AAAA;AAAA,MAEJ,CAAC;AAAA,IACH;AAAA,IAEA,oBAAC,SAAS,WAAT,EAAoB,mBAAS,OAAM;AAAA,KACtC;AAEJ;AAKO,MAAM,SAAS,CAAC,UACrB,oBAAC,QAAK,MAAI,MAAC,OAAM,QACd,gBAAM,UACT;AAMK,MAAM,OAAO,CAAC,UAAqB,oBAAC,QAAM,gBAAM,UAAS;AAKzD,MAAM,WAAW,CAAC,UAAmC,oBAAC,QAAK,MAAI,MAAE,gBAAM,UAAS;AAOvF,MAAM,eAAe,CAAC,MAAc,UAAkB,YAA4B;AAChF,QAAM,iBAAiB,WAAW,UAAU;AAC5C,MAAI,KAAK,UAAU,eAAgB,QAAO;AAC1C,MAAI,kBAAkB,EAAG,QAAO,MAAM,MAAM,GAAG,cAAc;AAC7D,SAAO,GAAG,KAAK,MAAM,GAAG,iBAAiB,CAAC,CAAC;AAC7C;AAKA,MAAM,cAAc,CAAO,cAAoC;AAAA;AAAA,EAE7D,SAAS;AAAA,IACP,CAAC,KAAK,SAAS,UAAU;AAEvB,UAAI,IAAI,WAAW,EAAG,QAAO,CAAC,OAAO;AAErC,UAAI,KAAK,aAAa,KAAK,GAAG,OAAO;AACrC,aAAO;AAAA,IACT;AAAA,IACA,CAAC;AAAA,EACH;AAAA;", "names": ["data", "columns", "header", "headings", "row", "key"]}