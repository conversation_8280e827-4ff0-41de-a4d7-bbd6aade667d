{"version": 3, "sources": ["../../src/lib/cleanup-registry.ts"], "sourcesContent": ["import { unlink } from \"node:fs/promises\"\n\ntype CleanupHandler = () => void | Promise<void>\n\nclass CleanupRegistry {\n  private handlers = new Set<CleanupHandler>()\n  private isShuttingDown = false\n\n  register(handler: CleanupHandler): () => void {\n    this.handlers.add(handler)\n    return () => this.handlers.delete(handler)\n  }\n\n  async cleanup(): Promise<void> {\n    if (this.isShuttingDown) return\n    this.isShuttingDown = true\n\n    const promises: Promise<void>[] = []\n    for (const handler of this.handlers) {\n      try {\n        const result = handler()\n        if (result instanceof Promise) {\n          promises.push(result.catch(console.error))\n        }\n      } catch (error) {\n        console.error(\"Cleanup handler error:\", error)\n      }\n    }\n\n    await Promise.all(promises)\n  }\n\n  reset(): void {\n    this.handlers.clear()\n    this.isShuttingDown = false\n  }\n}\n\n// Global singleton instance\nexport const cleanupRegistry = new CleanupRegistry()\n\n// Helper for cleaning up files\nexport const createFileCleanupHandler = (files: Set<string>): CleanupHandler => {\n  return async () => {\n    const promises: Promise<void>[] = []\n    for (const file of files) {\n      promises.push(\n        unlink(file).catch(error => {\n          // Ignore file not found errors\n          if (error.code !== \"ENOENT\") {\n            console.error(`Failed to clean up ${file}:`, error.message)\n          }\n        }),\n      )\n    }\n    await Promise.all(promises)\n  }\n}\n"], "mappings": "AAAA,SAAS,cAAc;AAIvB,MAAM,gBAAgB;AAAA,EACZ,WAAW,oBAAI,IAAoB;AAAA,EACnC,iBAAiB;AAAA,EAEzB,SAAS,SAAqC;AAC5C,SAAK,SAAS,IAAI,OAAO;AACzB,WAAO,MAAM,KAAK,SAAS,OAAO,OAAO;AAAA,EAC3C;AAAA,EAEA,MAAM,UAAyB;AAC7B,QAAI,KAAK,eAAgB;AACzB,SAAK,iBAAiB;AAEtB,UAAM,WAA4B,CAAC;AACnC,eAAW,WAAW,KAAK,UAAU;AACnC,UAAI;AACF,cAAM,SAAS,QAAQ;AACvB,YAAI,kBAAkB,SAAS;AAC7B,mBAAS,KAAK,OAAO,MAAM,QAAQ,KAAK,CAAC;AAAA,QAC3C;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,0BAA0B,KAAK;AAAA,MAC/C;AAAA,IACF;AAEA,UAAM,QAAQ,IAAI,QAAQ;AAAA,EAC5B;AAAA,EAEA,QAAc;AACZ,SAAK,SAAS,MAAM;AACpB,SAAK,iBAAiB;AAAA,EACxB;AACF;AAGO,MAAM,kBAAkB,IAAI,gBAAgB;AAG5C,MAAM,2BAA2B,CAAC,UAAuC;AAC9E,SAAO,YAAY;AACjB,UAAM,WAA4B,CAAC;AACnC,eAAW,QAAQ,OAAO;AACxB,eAAS;AAAA,QACP,OAAO,IAAI,EAAE,MAAM,WAAS;AAE1B,cAAI,MAAM,SAAS,UAAU;AAC3B,oBAAQ,MAAM,sBAAsB,IAAI,KAAK,MAAM,OAAO;AAAA,UAC5D;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,UAAM,QAAQ,IAAI,QAAQ;AAAA,EAC5B;AACF;", "names": []}