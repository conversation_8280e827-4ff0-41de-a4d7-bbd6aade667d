"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _react = _interopRequireDefault(require("react"));

var _propTypes = _interopRequireDefault(require("prop-types"));

var _arrify = _interopRequireDefault(require("arrify"));

var _chalk = _interopRequireDefault(require("chalk"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }

function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }

const methods = ['hex', 'hsl', 'hsv', 'hwb', 'rgb', 'keyword', 'bgHex', 'bgHsl', 'bgHsv', 'bgHwb', 'bgRgb', 'bgKeyword'];

const Color = (_ref) => {
  let {
    children
  } = _ref,
      colorProps = _objectWithoutProperties(_ref, ["children"]);

  if (children === '') {
    return null;
  }

  const transformChildren = children => {
    Object.keys(colorProps).forEach(method => {
      if (colorProps[method]) {
        if (methods.includes(method)) {
          children = _chalk.default[method](...(0, _arrify.default)(colorProps[method]))(children);
        } else if (typeof _chalk.default[method] === 'function') {
          children = _chalk.default[method](children);
        }
      }
    });
    return children;
  };

  return _react.default.createElement("span", {
    style: {
      flexDirection: 'row'
    },
    unstable__transformChildren: transformChildren
  }, children);
};

Color.propTypes = {
  children: _propTypes.default.node
};
Color.defaultProps = {
  children: ''
};
var _default = Color;
exports.default = _default;