{"cells": [{"cell_type": "markdown", "id": "8243adf1-338a-4ee2-a759-c39f5d5d9ff0", "metadata": {}, "source": ["(quickstart)=\n", "# WeatherBench-X Quickstart\n", "\n", "<a target=\"_blank\" href=\"https://colab.research.google.com/github/google-research/weatherbenchX/blob/main/docs/source/wbx_quickstart.ipynb\">\n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/>\n", "</a>\n", "\n", "This notebook goes through the basic components of WeatherBench-X."]}, {"cell_type": "markdown", "id": "dcb556ad-2416-49be-925c-82b110fe9764", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "id": "c8501463", "metadata": {}, "outputs": [], "source": ["# Note that pip might complain about some versions but the notebook should still work as expected.\n", "!pip install git+https://github.com/google-research/weatherbenchX.git"]}, {"cell_type": "code", "execution_count": 1, "id": "0c7ee5de-bac3-43e5-adda-e82f3b4160c6", "metadata": {}, "outputs": [], "source": ["import apache_beam as beam\n", "import numpy as np\n", "import xarray as xr\n", "import weatherbenchX\n", "from weatherbenchX.data_loaders import xarray_loaders\n", "from weatherbenchX.metrics import deterministic\n", "from weatherbenchX.metrics import base as metrics_base\n", "from weatherbenchX import aggregation\n", "from weatherbenchX import weighting\n", "from weatherbenchX import binning\n", "from weatherbenchX import time_chunks\n", "from weatherbenchX import beam_pipeline"]}, {"cell_type": "markdown", "id": "3ec40ffd-fa35-4cc0-bb21-102c15826e7d", "metadata": {}, "source": ["**IMPORTANT: If you are running this on Colab, uncomment the cell below to access the cloud datasets.**"]}, {"cell_type": "code", "execution_count": null, "id": "8cf54175-7d7e-44e7-b06a-24cb89f1181b", "metadata": {}, "outputs": [], "source": ["# from google.colab import auth\n", "# auth.authenticate_user()"]}, {"cell_type": "markdown", "id": "8d7e071d-c975-4916-97cf-c1f9182e4e6e", "metadata": {}, "source": ["## Data Loaders\n", "\n", "First, we define the data loaders for the data we would like to use. Data loaders can be implemented to read from any source. The only requirement is that they return data as an Xarray Dataset (or a dictionary of DataArrays).\n", "\n", "It is the data loaders' job to make sure that the returned target and prediction datasets are aligned, i.e. have the same variable names and coordinates that can be broadcast against each other. If this is not the case (e.g. for sparse observations), interpolators can be used to align the data (see How To).\n", "\n", "In this example, we will evaluate gridded HRES predictions against ERA5 targets. We will load the public Zarr datasets on the WeatherBench cloud bucket."]}, {"cell_type": "code", "execution_count": 2, "id": "8eede8eb-1212-4bfd-94b8-9d7de764d8b0", "metadata": {}, "outputs": [], "source": ["prediction_path = 'gs://weatherbench2/datasets/hres/2016-2022-0012-64x32_equiangular_conservative.zarr'\n", "target_path = 'gs://weatherbench2/datasets/era5/1959-2022-6h-64x32_equiangular_conservative.zarr'"]}, {"cell_type": "code", "execution_count": 3, "id": "802d32cd-9346-4e15-9374-92a19bf23f7d", "metadata": {}, "outputs": [], "source": ["variables = ['2m_temperature', 'geopotential']\n", "target_data_loader = xarray_loaders.TargetsFromXarray(\n", "    path=target_path,\n", "    variables=variables,\n", ")\n", "prediction_data_loader = xarray_loaders.PredictionsFromXarray(\n", "    path=prediction_path,\n", "    variables=variables,\n", ")"]}, {"cell_type": "markdown", "id": "46488a92-e13f-492e-bd1c-cb4289540baa", "metadata": {}, "source": ["Now we define the initialization and lead times we would like to load data for. In the beam pipeline, this would be the job of the TimeChunks instance. For now, let's load two init and three lead times, defined as numpy datetime64/timedelta64 objects."]}, {"cell_type": "code", "execution_count": 4, "id": "05860e63-b732-420b-958c-d5d757415f1d", "metadata": {}, "outputs": [], "source": ["init_times = np.array(['2020-01-01T00', '2020-01-01T12'], dtype='datetime64[ns]')\n", "lead_times = np.array([6, 12, 18], dtype='timedelta64[h]').astype('timedelta64[ns]')   # To silence xr warnings."]}, {"cell_type": "code", "execution_count": 5, "id": "d830b286-875f-4dd3-822d-eebe077b75df", "metadata": {}, "outputs": [], "source": ["target_chunk = target_data_loader.load_chunk(init_times, lead_times)\n", "prediction_chunk = prediction_data_loader.load_chunk(init_times, lead_times)"]}, {"cell_type": "code", "execution_count": 6, "id": "fdc1e7cc-8058-4caf-9874-79277ced1925", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 689kB\n", "Dimensions:         (latitude: 32, longitude: 64, init_time: 2, lead_time: 3,\n", "                     level: 13)\n", "Coordinates:\n", "  * latitude        (latitude) float64 256B -87.19 -81.56 -75.94 ... 81.56 87.19\n", "  * longitude       (longitude) float64 512B 0.0 5.625 11.25 ... 348.8 354.4\n", "    valid_time      (init_time, lead_time) datetime64[ns] 48B 2020-01-01T06:0...\n", "  * init_time       (init_time) datetime64[ns] 16B 2020-01-01 2020-01-01T12:0...\n", "  * lead_time       (lead_time) timedelta64[ns] 24B 06:00:00 12:00:00 18:00:00\n", "  * level           (level) int64 104B 50 100 150 200 250 ... 700 ************\n", "Data variables:\n", "    2m_temperature  (init_time, lead_time, longitude, latitude) float32 49kB ...\n", "    geopotential    (init_time, lead_time, level, longitude, latitude) float32 639kB ...\n", "Attributes:\n", "    long_name:   2 metre temperature\n", "    short_name:  t2m\n", "    units:       K</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-0460806f-be06-48ad-9075-473d20528fa0' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-0460806f-be06-48ad-9075-473d20528fa0' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>latitude</span>: 32</li><li><span class='xr-has-index'>longitude</span>: 64</li><li><span class='xr-has-index'>init_time</span>: 2</li><li><span class='xr-has-index'>lead_time</span>: 3</li><li><span class='xr-has-index'>level</span>: 13</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-80f6c2ae-a933-441f-8a7d-ba949e60c410' class='xr-section-summary-in' type='checkbox'  checked><label for='section-80f6c2ae-a933-441f-8a7d-ba949e60c410' class='xr-section-summary' >Coordinates: <span>(6)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>latitude</span></div><div class='xr-var-dims'>(latitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>-87.19 -81.56 ... 81.56 87.19</div><input id='attrs-7ad49d4e-63d4-492c-afa9-e7236b82e04d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-7ad49d4e-63d4-492c-afa9-e7236b82e04d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c8c8a00d-5113-4fe4-9f9d-724ab81b7147' class='xr-var-data-in' type='checkbox'><label for='data-c8c8a00d-5113-4fe4-9f9d-724ab81b7147' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([-87.1875, -81.5625, -75.9375, -70.3125, -64.6875, -59.0625, -53.4375,\n", "       -47.8125, -42.1875, -36.5625, -30.9375, -25.3125, -19.6875, -14.0625,\n", "        -8.4375,  -2.8125,   2.8125,   8.4375,  14.0625,  19.6875,  25.3125,\n", "        30.9375,  36.5625,  42.1875,  47.8125,  53.4375,  59.0625,  64.6875,\n", "        70.3125,  75.9375,  81.5625,  87.1875])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>longitude</span></div><div class='xr-var-dims'>(longitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.0 5.625 11.25 ... 348.8 354.4</div><input id='attrs-c7dd8a8e-1039-4056-9b4d-f50a0a4f8b7a' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c7dd8a8e-1039-4056-9b4d-f50a0a4f8b7a' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-82334b3c-1dab-43c2-a711-907f91b20bb4' class='xr-var-data-in' type='checkbox'><label for='data-82334b3c-1dab-43c2-a711-907f91b20bb4' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  0.   ,   5.625,  11.25 ,  16.875,  22.5  ,  28.125,  33.75 ,  39.375,\n", "        45.   ,  50.625,  56.25 ,  61.875,  67.5  ,  73.125,  78.75 ,  84.375,\n", "        90.   ,  95.625, 101.25 , 106.875, 112.5  , 118.125, 123.75 , 129.375,\n", "       135.   , 140.625, 146.25 , 151.875, 157.5  , 163.125, 168.75 , 174.375,\n", "       180.   , 185.625, 191.25 , 196.875, 202.5  , 208.125, 213.75 , 219.375,\n", "       225.   , 230.625, 236.25 , 241.875, 247.5  , 253.125, 258.75 , 264.375,\n", "       270.   , 275.625, 281.25 , 286.875, 292.5  , 298.125, 303.75 , 309.375,\n", "       315.   , 320.625, 326.25 , 331.875, 337.5  , 343.125, 348.75 , 354.375])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>valid_time</span></div><div class='xr-var-dims'>(init_time, lead_time)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2020-01-01T06:00:00 ... 2020-01-...</div><input id='attrs-93c32998-c399-496f-bcaf-5e09bb995fee' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-93c32998-c399-496f-bcaf-5e09bb995fee' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-81ff7e55-487f-4d76-87d4-3b64a8e6d3ab' class='xr-var-data-in' type='checkbox'><label for='data-81ff7e55-487f-4d76-87d4-3b64a8e6d3ab' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[&#x27;2020-01-01T06:00:00.000000000&#x27;, &#x27;2020-01-01T12:00:00.000000000&#x27;,\n", "        &#x27;2020-01-01T18:00:00.000000000&#x27;],\n", "       [&#x27;2020-01-01T18:00:00.000000000&#x27;, &#x27;2020-01-02T00:00:00.000000000&#x27;,\n", "        &#x27;2020-01-02T06:00:00.000000000&#x27;]], dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>init_time</span></div><div class='xr-var-dims'>(init_time)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2020-01-01 2020-01-01T12:00:00</div><input id='attrs-59937123-0607-4ab5-b9e5-00b1300110b4' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-59937123-0607-4ab5-b9e5-00b1300110b4' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d3861cb6-cefc-403e-a643-34176c1c8293' class='xr-var-data-in' type='checkbox'><label for='data-d3861cb6-cefc-403e-a643-34176c1c8293' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2020-01-01T00:00:00.000000000&#x27;, &#x27;2020-01-01T12:00:00.000000000&#x27;],\n", "      dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>lead_time</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>timedelta64[ns]</div><div class='xr-var-preview xr-preview'>06:00:00 12:00:00 18:00:00</div><input id='attrs-fd6a851b-95fa-40ea-b6dc-e563ec6d7ecd' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-fd6a851b-95fa-40ea-b6dc-e563ec6d7ecd' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e7c7d3e5-faeb-4aa9-95cd-a8ad753504ed' class='xr-var-data-in' type='checkbox'><label for='data-e7c7d3e5-faeb-4aa9-95cd-a8ad753504ed' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([21600000000000, 43200000000000, 64800000000000], dtype=&#x27;timedelta64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>level</span></div><div class='xr-var-dims'>(level)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>50 100 150 200 ... 700 ************</div><input id='attrs-ca8223e8-0e7d-4db4-85c3-ae1c7f43b911' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ca8223e8-0e7d-4db4-85c3-ae1c7f43b911' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-97805053-e810-4284-91c4-70849a361f67' class='xr-var-data-in' type='checkbox'><label for='data-97805053-e810-4284-91c4-70849a361f67' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  50,  100,  150,  200,  250,  300,  400,  500,  600,  700,  850,  925,\n", "       1000])</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-7de018eb-5301-4994-815c-3388ed6f2669' class='xr-section-summary-in' type='checkbox'  checked><label for='section-7de018eb-5301-4994-815c-3388ed6f2669' class='xr-section-summary' >Data variables: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>2m_temperature</span></div><div class='xr-var-dims'>(init_time, lead_time, longitude, latitude)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>248.4 252.2 248.0 ... 252.2 245.4</div><input id='attrs-82900cbf-5c9a-45f9-ba35-f848e2a41fce' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-82900cbf-5c9a-45f9-ba35-f848e2a41fce' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-6f4e9da7-6553-4cf8-8fa0-49720189ccc0' class='xr-var-data-in' type='checkbox'><label for='data-6f4e9da7-6553-4cf8-8fa0-49720189ccc0' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>2 metre temperature</dd><dt><span>short_name :</span></dt><dd>t2m</dd><dt><span>units :</span></dt><dd>K</dd></dl></div><div class='xr-var-data'><pre>array([[[[248.36205, 252.2097 , 247.95477, ..., 268.79034, 258.56357,\n", "          248.36649],\n", "         [248.92216, 251.7552 , 245.48674, ..., 269.54294, 259.23126,\n", "          247.61337],\n", "         [249.58875, 250.87376, 243.08667, ..., 269.0646 , 257.08997,\n", "          246.24663],\n", "         ...,\n", "         [249.15182, 258.12863, 260.88635, ..., 252.60616, 248.07338,\n", "          248.39255],\n", "         [248.83302, 254.66513, 255.88899, ..., 260.2368 , 251.92224,\n", "          248.6063 ],\n", "         [248.52016, 252.95393, 250.98595, ..., 265.79276, 256.78262,\n", "          248.566  ]],\n", "\n", "        [[250.18587, 253.7663 , 251.85052, ..., 270.30908, 257.2979 ,\n", "          247.10568],\n", "         [250.51826, 253.02794, 249.47733, ..., 271.02835, 258.25165,\n", "          246.58774],\n", "         [251.05833, 252.26022, 247.00154, ..., 270.60675, 256.49557,\n", "          245.79356],\n", "...\n", "         [249.79617, 256.1409 , 261.49908, ..., 254.09549, 247.7142 ,\n", "          247.04366],\n", "         [249.25432, 253.02287, 254.89989, ..., 263.7681 , 252.35565,\n", "          246.6539 ],\n", "         [249.17216, 251.53012, 249.4219 , ..., 268.86908, 254.02069,\n", "          245.9641 ]],\n", "\n", "        [[250.02478, 250.67987, 247.67944, ..., 272.7764 , 253.86063,\n", "          244.66318],\n", "         [250.0884 , 249.59056, 245.22005, ..., 272.64655, 254.90814,\n", "          243.98409],\n", "         [250.0357 , 248.26498, 242.95465, ..., 269.99564, 253.53838,\n", "          243.2451 ],\n", "         ...,\n", "         [250.39436, 255.54272, 261.40784, ..., 255.27417, 247.99146,\n", "          246.60626],\n", "         [250.0799 , 252.96887, 255.78105, ..., 266.1055 , 251.43645,\n", "          246.19131],\n", "         [249.99251, 251.63602, 250.39384, ..., 270.5454 , 252.21057,\n", "          245.39186]]]], dtype=float32)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>geopotential</span></div><div class='xr-var-dims'>(init_time, lead_time, level, longitude, latitude)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>2.018e+05 2.015e+05 ... 115.7 532.7</div><input id='attrs-e6f79594-50f1-45d2-9260-37ac58d6c1d1' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-e6f79594-50f1-45d2-9260-37ac58d6c1d1' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-20740443-dda1-410f-8b70-99b8f8eda488' class='xr-var-data-in' type='checkbox'><label for='data-20740443-dda1-410f-8b70-99b8f8eda488' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>Geopotential</dd><dt><span>short_name :</span></dt><dd>z</dd><dt><span>standard_name :</span></dt><dd>geopotential</dd><dt><span>units :</span></dt><dd>m**2 s**-2</dd></dl></div><div class='xr-var-data'><pre>array([[[[[ 2.01821562e+05,  2.01458109e+05,  2.01008562e+05, ...,\n", "            1.86513688e+05,  1.85340703e+05,  1.85361359e+05],\n", "          [ 2.01815422e+05,  2.01437953e+05,  2.00979094e+05, ...,\n", "            1.86409312e+05,  1.85296578e+05,  1.85337922e+05],\n", "          [ 2.01813203e+05,  2.01424922e+05,  2.00974734e+05, ...,\n", "            1.86267969e+05,  1.85249156e+05,  1.85318578e+05],\n", "          ...,\n", "          [ 2.01858625e+05,  2.01577328e+05,  2.01232156e+05, ...,\n", "            1.86602125e+05,  1.85482391e+05,  1.85458094e+05],\n", "          [ 2.01843188e+05,  2.01524016e+05,  2.01136609e+05, ...,\n", "            1.86605266e+05,  1.85431062e+05,  1.85419703e+05],\n", "          [ 2.01831016e+05,  2.01483906e+05,  2.01058234e+05, ...,\n", "            1.86578422e+05,  1.85385109e+05,  1.85388516e+05]],\n", "\n", "         [[ 1.55189531e+05,  1.54985828e+05,  1.54715531e+05, ...,\n", "            1.47446812e+05,  1.46093906e+05,  1.45955781e+05],\n", "          [ 1.55185734e+05,  1.54950453e+05,  1.54652469e+05, ...,\n", "            1.47437234e+05,  1.46102266e+05,  1.45964125e+05],\n", "          [ 1.55184688e+05,  1.54926703e+05,  1.54624562e+05, ...,\n", "            1.47338328e+05,  1.46097391e+05,  1.45973234e+05],\n", "...\n", "            4.30588623e+03,  6.01322949e+03,  6.20537891e+03],\n", "          [ 5.41140625e+03,  5.29359961e+03,  5.07229688e+03, ...,\n", "            3.85378809e+03,  5.84947461e+03,  6.15152051e+03],\n", "          [ 5.42598926e+03,  5.36046045e+03,  5.19691162e+03, ...,\n", "            4.01446777e+03,  5.73851562e+03,  6.08759326e+03]],\n", "\n", "         [[-5.44226135e+02, -5.27426758e+02, -6.74698120e+02, ...,\n", "           -1.80646655e+03, -3.43118286e+00,  4.65475739e+02],\n", "          [-5.21866943e+02, -4.83067383e+02, -6.54941528e+02, ...,\n", "           -1.51751489e+03, -8.84711456e+01,  4.01469910e+02],\n", "          [-4.83724915e+02, -4.07620209e+02, -6.13379883e+02, ...,\n", "           -1.18497717e+03, -1.40483887e+02,  3.40848877e+02],\n", "          ...,\n", "          [-5.74937317e+02, -7.37791138e+02, -9.72688599e+02, ...,\n", "           -1.38644202e+03,  4.65942078e+02,  6.55981750e+02],\n", "          [-5.70087280e+02, -6.54353394e+02, -9.53467102e+02, ...,\n", "           -2.11547852e+03,  2.69991852e+02,  5.96104614e+02],\n", "          [-5.60803589e+02, -5.93934448e+02, -8.16667969e+02, ...,\n", "           -2.03225757e+03,  1.15710251e+02,  5.32743408e+02]]]]],\n", "      dtype=float32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-6a27ba39-2146-4ba1-bb52-fdc63f504204' class='xr-section-summary-in' type='checkbox'  ><label for='section-6a27ba39-2146-4ba1-bb52-fdc63f504204' class='xr-section-summary' >Indexes: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>latitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-6b733a52-a1f0-4286-9251-62fb2e75c8e4' class='xr-index-data-in' type='checkbox'/><label for='index-6b733a52-a1f0-4286-9251-62fb2e75c8e4' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([ -87.18750000000003,  -81.56250000000001,            -75.9375,\n", "        -70.31249999999999,  -64.68750000000001,            -59.0625,\n", "                  -53.4375,            -47.8125,            -42.1875,\n", "                  -36.5625, -30.937499999999996, -25.312500000000004,\n", "       -19.687499999999996, -14.062499999999991,  -8.437499999999996,\n", "        -2.812500000000003,   2.812500000000003,   8.437500000000009,\n", "        14.062500000000004,  19.687499999999996,  25.312500000000004,\n", "         30.93750000000001,  36.562499999999986,             42.1875,\n", "                   47.8125,             53.4375,  59.062500000000014,\n", "         64.68750000000001,             70.3125,             75.9375,\n", "         81.56249999999997,   87.18750000000003],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;latitude&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>longitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-56145ae7-151f-4c15-b192-773f2d30831c' class='xr-index-data-in' type='checkbox'/><label for='index-56145ae7-151f-4c15-b192-773f2d30831c' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([               0.0,              5.625,              11.25,\n", "                   16.875,               22.5,             28.125,\n", "                    33.75,             39.375,               45.0,\n", "                   50.625,              56.25,  61.87499999999999,\n", "                     67.5,             73.125,              78.75,\n", "                   84.375,               90.0,             95.625,\n", "                   101.25,            106.875,              112.5,\n", "                  118.125, 123.74999999999999,            129.375,\n", "                    135.0,            140.625,             146.25,\n", "                  151.875,              157.5,            163.125,\n", "                   168.75,            174.375,              180.0,\n", "                  185.625,             191.25,            196.875,\n", "                    202.5,            208.125,             213.75,\n", "                  219.375,              225.0, 230.62499999999997,\n", "                   236.25,            241.875, 247.49999999999997,\n", "                  253.125,             258.75,            264.375,\n", "                    270.0,            275.625,             281.25,\n", "                  286.875,              292.5,            298.125,\n", "                   303.75,            309.375,              315.0,\n", "                  320.625,             326.25,            331.875,\n", "                    337.5,            343.125,             348.75,\n", "                  354.375],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;longitude&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>init_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-e20167d6-110c-43cf-9091-ead232378ab5' class='xr-index-data-in' type='checkbox'/><label for='index-e20167d6-110c-43cf-9091-ead232378ab5' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2020-01-01 00:00:00&#x27;, &#x27;2020-01-01 12:00:00&#x27;], dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;init_time&#x27;, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>lead_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-3a1dff1d-2db9-4ec7-8849-9872cf3234c3' class='xr-index-data-in' type='checkbox'/><label for='index-3a1dff1d-2db9-4ec7-8849-9872cf3234c3' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(TimedeltaIndex([&#x27;0 days 06:00:00&#x27;, &#x27;0 days 12:00:00&#x27;, &#x27;0 days 18:00:00&#x27;], dtype=&#x27;timedelta64[ns]&#x27;, name=&#x27;lead_time&#x27;, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>level</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-3fbf5a7b-cfab-4cf1-b25b-80559455b1ed' class='xr-index-data-in' type='checkbox'/><label for='index-3fbf5a7b-cfab-4cf1-b25b-80559455b1ed' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([50, 100, 150, 200, 250, 300, 400, 500, 600, 700, 850, 925, 1000], dtype=&#x27;int64&#x27;, name=&#x27;level&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-edbdf43f-632a-49f7-a6d0-4d85c691982c' class='xr-section-summary-in' type='checkbox'  checked><label for='section-edbdf43f-632a-49f7-a6d0-4d85c691982c' class='xr-section-summary' >Attributes: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>2 metre temperature</dd><dt><span>short_name :</span></dt><dd>t2m</dd><dt><span>units :</span></dt><dd>K</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 689kB\n", "Dimensions:         (latitude: 32, longitude: 64, init_time: 2, lead_time: 3,\n", "                     level: 13)\n", "Coordinates:\n", "  * latitude        (latitude) float64 256B -87.19 -81.56 -75.94 ... 81.56 87.19\n", "  * longitude       (longitude) float64 512B 0.0 5.625 11.25 ... 348.8 354.4\n", "    valid_time      (init_time, lead_time) datetime64[ns] 48B 2020-01-01T06:0...\n", "  * init_time       (init_time) datetime64[ns] 16B 2020-01-01 2020-01-01T12:0...\n", "  * lead_time       (lead_time) timedelta64[ns] 24B 06:00:00 12:00:00 18:00:00\n", "  * level           (level) int64 104B 50 100 150 200 250 ... 700 ************\n", "Data variables:\n", "    2m_temperature  (init_time, lead_time, longitude, latitude) float32 49kB ...\n", "    geopotential    (init_time, lead_time, level, longitude, latitude) float32 639kB ...\n", "Attributes:\n", "    long_name:   2 metre temperature\n", "    short_name:  t2m\n", "    units:       K"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["target_chunk"]}, {"cell_type": "code", "execution_count": 7, "id": "ae177dbf-7125-4779-96ca-8d2dba354a6a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 689kB\n", "Dimensions:         (latitude: 32, longitude: 64, lead_time: 3, init_time: 2,\n", "                     level: 13)\n", "Coordinates:\n", "  * latitude        (latitude) float64 256B -87.19 -81.56 -75.94 ... 81.56 87.19\n", "  * longitude       (longitude) float64 512B 0.0 5.625 11.25 ... 348.8 354.4\n", "  * lead_time       (lead_time) timedelta64[ns] 24B 06:00:00 12:00:00 18:00:00\n", "  * init_time       (init_time) datetime64[ns] 16B 2020-01-01 2020-01-01T12:0...\n", "  * level           (level) int32 52B 50 100 150 200 250 ... 700 ************\n", "Data variables:\n", "    2m_temperature  (init_time, lead_time, longitude, latitude) float32 49kB ...\n", "    geopotential    (init_time, lead_time, level, longitude, latitude) float32 639kB ...\n", "Attributes:\n", "    long_name:      2 metre temperature\n", "    short_name:     t2m\n", "    standard_name:  unknown\n", "    units:          K</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-a99dc57c-9f2c-4691-a286-0f9093c0a2fa' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-a99dc57c-9f2c-4691-a286-0f9093c0a2fa' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>latitude</span>: 32</li><li><span class='xr-has-index'>longitude</span>: 64</li><li><span class='xr-has-index'>lead_time</span>: 3</li><li><span class='xr-has-index'>init_time</span>: 2</li><li><span class='xr-has-index'>level</span>: 13</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-720bd442-eea3-48b4-941f-3f8d761fe05a' class='xr-section-summary-in' type='checkbox'  checked><label for='section-720bd442-eea3-48b4-941f-3f8d761fe05a' class='xr-section-summary' >Coordinates: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>latitude</span></div><div class='xr-var-dims'>(latitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>-87.19 -81.56 ... 81.56 87.19</div><input id='attrs-4cadbb77-5f13-4af4-8b5b-9358c21d02a4' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-4cadbb77-5f13-4af4-8b5b-9358c21d02a4' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d70fcad5-3bd4-45a1-9b63-3e1d2790c70f' class='xr-var-data-in' type='checkbox'><label for='data-d70fcad5-3bd4-45a1-9b63-3e1d2790c70f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([-87.1875, -81.5625, -75.9375, -70.3125, -64.6875, -59.0625, -53.4375,\n", "       -47.8125, -42.1875, -36.5625, -30.9375, -25.3125, -19.6875, -14.0625,\n", "        -8.4375,  -2.8125,   2.8125,   8.4375,  14.0625,  19.6875,  25.3125,\n", "        30.9375,  36.5625,  42.1875,  47.8125,  53.4375,  59.0625,  64.6875,\n", "        70.3125,  75.9375,  81.5625,  87.1875])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>longitude</span></div><div class='xr-var-dims'>(longitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.0 5.625 11.25 ... 348.8 354.4</div><input id='attrs-1acb5f9a-5e16-4c59-89d4-5954152b9818' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-1acb5f9a-5e16-4c59-89d4-5954152b9818' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-9723cdb0-7370-4557-90ed-a9223d1763f9' class='xr-var-data-in' type='checkbox'><label for='data-9723cdb0-7370-4557-90ed-a9223d1763f9' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  0.   ,   5.625,  11.25 ,  16.875,  22.5  ,  28.125,  33.75 ,  39.375,\n", "        45.   ,  50.625,  56.25 ,  61.875,  67.5  ,  73.125,  78.75 ,  84.375,\n", "        90.   ,  95.625, 101.25 , 106.875, 112.5  , 118.125, 123.75 , 129.375,\n", "       135.   , 140.625, 146.25 , 151.875, 157.5  , 163.125, 168.75 , 174.375,\n", "       180.   , 185.625, 191.25 , 196.875, 202.5  , 208.125, 213.75 , 219.375,\n", "       225.   , 230.625, 236.25 , 241.875, 247.5  , 253.125, 258.75 , 264.375,\n", "       270.   , 275.625, 281.25 , 286.875, 292.5  , 298.125, 303.75 , 309.375,\n", "       315.   , 320.625, 326.25 , 331.875, 337.5  , 343.125, 348.75 , 354.375])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>lead_time</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>timedelta64[ns]</div><div class='xr-var-preview xr-preview'>06:00:00 12:00:00 18:00:00</div><input id='attrs-43709063-dfed-4745-b80f-1a2d4093bad3' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-43709063-dfed-4745-b80f-1a2d4093bad3' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a72eaa99-6d33-4576-8b3a-8d270b06e62d' class='xr-var-data-in' type='checkbox'><label for='data-a72eaa99-6d33-4576-8b3a-8d270b06e62d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>time since forecast_reference_time</dd><dt><span>standard_name :</span></dt><dd>forecast_period</dd></dl></div><div class='xr-var-data'><pre>array([21600000000000, 43200000000000, 64800000000000], dtype=&#x27;timedelta64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>init_time</span></div><div class='xr-var-dims'>(init_time)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2020-01-01 2020-01-01T12:00:00</div><input id='attrs-18a624d3-cff0-4e0d-b862-10ef31240f77' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-18a624d3-cff0-4e0d-b862-10ef31240f77' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b71d8f3e-1b3c-48c6-af51-7db04e1bf55e' class='xr-var-data-in' type='checkbox'><label for='data-b71d8f3e-1b3c-48c6-af51-7db04e1bf55e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>initial time of forecast</dd><dt><span>standard_name :</span></dt><dd>forecast_reference_time</dd></dl></div><div class='xr-var-data'><pre>array([&#x27;2020-01-01T00:00:00.000000000&#x27;, &#x27;2020-01-01T12:00:00.000000000&#x27;],\n", "      dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>level</span></div><div class='xr-var-dims'>(level)</div><div class='xr-var-dtype'>int32</div><div class='xr-var-preview xr-preview'>50 100 150 200 ... 700 ************</div><input id='attrs-2582e38d-734a-4d5d-a904-8ed77d6895cb' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-2582e38d-734a-4d5d-a904-8ed77d6895cb' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-0d6f9735-46bb-49e7-ad21-8d7092a27d27' class='xr-var-data-in' type='checkbox'><label for='data-0d6f9735-46bb-49e7-ad21-8d7092a27d27' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  50,  100,  150,  200,  250,  300,  400,  500,  600,  700,  850,  925,\n", "       1000], dtype=int32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-be18bc50-f210-4a9b-9620-b4042d999d20' class='xr-section-summary-in' type='checkbox'  checked><label for='section-be18bc50-f210-4a9b-9620-b4042d999d20' class='xr-section-summary' >Data variables: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>2m_temperature</span></div><div class='xr-var-dims'>(init_time, lead_time, longitude, latitude)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>247.7 250.9 246.3 ... 251.8 245.4</div><input id='attrs-52dd9496-4b70-4d7f-a1b7-d273e9ca5f4e' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-52dd9496-4b70-4d7f-a1b7-d273e9ca5f4e' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-5f02c32b-82fe-4d73-b450-1c7d73f0b961' class='xr-var-data-in' type='checkbox'><label for='data-5f02c32b-82fe-4d73-b450-1c7d73f0b961' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>2 metre temperature</dd><dt><span>short_name :</span></dt><dd>t2m</dd><dt><span>standard_name :</span></dt><dd>unknown</dd><dt><span>units :</span></dt><dd>K</dd></dl></div><div class='xr-var-data'><pre>array([[[[247.69328, 250.85565, 246.323  , ..., 269.03973, 260.02847,\n", "          247.34961],\n", "         [247.73067, 249.8304 , 243.97044, ..., 269.5599 , 259.93457,\n", "          246.97052],\n", "         [247.83752, 249.21178, 241.8654 , ..., 269.3762 , 256.90005,\n", "          246.3403 ],\n", "         ...,\n", "         [248.0823 , 255.96936, 260.3537 , ..., 254.58119, 247.61133,\n", "          247.2649 ],\n", "         [247.86903, 253.47942, 254.25087, ..., 261.7141 , 250.78409,\n", "          247.3027 ],\n", "         [247.82603, 252.13803, 249.45506, ..., 266.37958, 255.17102,\n", "          247.34445]],\n", "\n", "        [[248.94199, 252.22739, 249.80328, ..., 270.18912, 258.01004,\n", "          246.65384],\n", "         [248.56845, 251.18413, 247.52945, ..., 270.73737, 258.35565,\n", "          246.39653],\n", "         [248.66196, 250.47433, 245.57146, ..., 270.34366, 256.1504 ,\n", "          245.93727],\n", "...\n", "         [248.91452, 254.68246, 259.32538, ..., 253.1698 , 248.35475,\n", "          246.54271],\n", "         [248.52327, 251.92426, 252.99542, ..., 262.19604, 252.18025,\n", "          246.21764],\n", "         [248.09544, 250.4874 , 247.31923, ..., 269.40958, 254.05913,\n", "          245.89488]],\n", "\n", "        [[248.00671, 249.56723, 245.84102, ..., 272.98486, 253.50638,\n", "          244.95746],\n", "         [248.2394 , 248.58055, 243.8972 , ..., 272.46222, 254.66048,\n", "          244.35309],\n", "         [248.26817, 247.48419, 242.09363, ..., 269.751  , 253.0976 ,\n", "          243.64192],\n", "         ...,\n", "         [248.42058, 254.38881, 259.1335 , ..., 254.7534 , 248.1372 ,\n", "          245.99579],\n", "         [248.04472, 251.91742, 253.42262, ..., 266.0077 , 250.54327,\n", "          245.71402],\n", "         [247.94028, 250.43552, 248.35391, ..., 271.24768, 251.75734,\n", "          245.3703 ]]]], dtype=float32)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>geopotential</span></div><div class='xr-var-dims'>(init_time, lead_time, level, longitude, latitude)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>2.018e+05 2.014e+05 ... 105.7 531.8</div><input id='attrs-74315d18-339b-405d-95f8-ec39078aad21' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-74315d18-339b-405d-95f8-ec39078aad21' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-765e5b03-5b87-4a09-bde9-e386ec6cf136' class='xr-var-data-in' type='checkbox'><label for='data-765e5b03-5b87-4a09-bde9-e386ec6cf136' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>Geopotential</dd><dt><span>short_name :</span></dt><dd>z</dd><dt><span>standard_name :</span></dt><dd>geopotential</dd><dt><span>units :</span></dt><dd>m**2 s**-2</dd></dl></div><div class='xr-var-data'><pre>array([[[[[ 2.01784734e+05,  2.01377188e+05,  2.00983625e+05, ...,\n", "            1.86421719e+05,  1.85243812e+05,  1.85253938e+05],\n", "          [ 2.01772219e+05,  2.01349125e+05,  2.00952766e+05, ...,\n", "            1.86319078e+05,  1.85195062e+05,  1.85232031e+05],\n", "          [ 2.01764094e+05,  2.01337062e+05,  2.00947297e+05, ...,\n", "            1.86180047e+05,  1.85144328e+05,  1.85212516e+05],\n", "          ...,\n", "          [ 2.01831375e+05,  2.01516906e+05,  2.01201953e+05, ...,\n", "            1.86500328e+05,  1.85391609e+05,  1.85349828e+05],\n", "          [ 2.01813438e+05,  2.01460281e+05,  2.01107516e+05, ...,\n", "            1.86487484e+05,  1.85342578e+05,  1.85313547e+05],\n", "          [ 2.01798688e+05,  2.01414234e+05,  2.01037125e+05, ...,\n", "            1.86475656e+05,  1.85291641e+05,  1.85281625e+05]],\n", "\n", "         [[ 1.55192375e+05,  1.54935469e+05,  1.54705922e+05, ...,\n", "            1.47412453e+05,  1.46081016e+05,  1.45948328e+05],\n", "          [ 1.55183047e+05,  1.54900125e+05,  1.54643453e+05, ...,\n", "            1.47409453e+05,  1.46093797e+05,  1.45957312e+05],\n", "          [ 1.55177062e+05,  1.54873688e+05,  1.54614531e+05, ...,\n", "            1.47322281e+05,  1.46083766e+05,  1.45966016e+05],\n", "...\n", "            4.34142236e+03,  5.98030908e+03,  6.18612305e+03],\n", "          [ 5.46739893e+03,  5.32205859e+03,  5.10882568e+03, ...,\n", "            3.85923828e+03,  5.81807910e+03,  6.13991797e+03],\n", "          [ 5.48535059e+03,  5.38945312e+03,  5.19915283e+03, ...,\n", "            3.97769434e+03,  5.72538232e+03,  6.08225488e+03]],\n", "\n", "         [[-4.70543945e+02, -4.86260956e+02, -6.63543457e+02, ...,\n", "           -1.82177539e+03,  1.06491184e+00,  4.69359558e+02],\n", "          [-4.55857635e+02, -4.49082001e+02, -6.52572998e+02, ...,\n", "           -1.49216016e+03, -7.57279358e+01,  4.06034790e+02],\n", "          [-4.23886353e+02, -3.90820129e+02, -6.08142700e+02, ...,\n", "           -1.15180884e+03, -1.31556961e+02,  3.42424469e+02],\n", "          ...,\n", "          [-5.15016418e+02, -7.05430115e+02, -9.42312866e+02, ...,\n", "           -1.33690637e+03,  4.30159851e+02,  6.47592346e+02],\n", "          [-4.99806396e+02, -6.23737732e+02, -9.08087463e+02, ...,\n", "           -2.09733276e+03,  2.49029312e+02,  5.91336182e+02],\n", "          [-4.85230835e+02, -5.58110474e+02, -8.13025635e+02, ...,\n", "           -2.08222266e+03,  1.05714355e+02,  5.31816589e+02]]]]],\n", "      dtype=float32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-7b8f0832-c47a-4a08-87e4-fae4f9edaaac' class='xr-section-summary-in' type='checkbox'  ><label for='section-7b8f0832-c47a-4a08-87e4-fae4f9edaaac' class='xr-section-summary' >Indexes: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>latitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-e1124d68-9a08-4380-a823-73db545d7926' class='xr-index-data-in' type='checkbox'/><label for='index-e1124d68-9a08-4380-a823-73db545d7926' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([ -87.18750000000003,  -81.56250000000001,            -75.9375,\n", "        -70.31249999999999,  -64.68750000000001,            -59.0625,\n", "                  -53.4375,            -47.8125,            -42.1875,\n", "                  -36.5625, -30.937499999999996, -25.312500000000004,\n", "       -19.687499999999996, -14.062499999999991,  -8.437499999999996,\n", "        -2.812500000000003,   2.812500000000003,   8.437500000000009,\n", "        14.062500000000004,  19.687499999999996,  25.312500000000004,\n", "         30.93750000000001,  36.562499999999986,             42.1875,\n", "                   47.8125,             53.4375,  59.062500000000014,\n", "         64.68750000000001,             70.3125,             75.9375,\n", "         81.56249999999997,   87.18750000000003],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;latitude&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>longitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-83b5cb3a-858d-4fd2-a55f-b6a597f6ae4c' class='xr-index-data-in' type='checkbox'/><label for='index-83b5cb3a-858d-4fd2-a55f-b6a597f6ae4c' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([               0.0,              5.625,              11.25,\n", "                   16.875,               22.5,             28.125,\n", "                    33.75,             39.375,               45.0,\n", "                   50.625,              56.25,  61.87499999999999,\n", "                     67.5,             73.125,              78.75,\n", "                   84.375,               90.0,             95.625,\n", "                   101.25,            106.875,              112.5,\n", "                  118.125, 123.74999999999999,            129.375,\n", "                    135.0,            140.625,             146.25,\n", "                  151.875,              157.5,            163.125,\n", "                   168.75,            174.375,              180.0,\n", "                  185.625,             191.25,            196.875,\n", "                    202.5,            208.125,             213.75,\n", "                  219.375,              225.0, 230.62499999999997,\n", "                   236.25,            241.875, 247.49999999999997,\n", "                  253.125,             258.75,            264.375,\n", "                    270.0,            275.625,             281.25,\n", "                  286.875,              292.5,            298.125,\n", "                   303.75,            309.375,              315.0,\n", "                  320.625,             326.25,            331.875,\n", "                    337.5,            343.125,             348.75,\n", "                  354.375],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;longitude&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>lead_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-2b06b7f0-9deb-4eba-a84e-d0b835de220b' class='xr-index-data-in' type='checkbox'/><label for='index-2b06b7f0-9deb-4eba-a84e-d0b835de220b' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(TimedeltaIndex([&#x27;0 days 06:00:00&#x27;, &#x27;0 days 12:00:00&#x27;, &#x27;0 days 18:00:00&#x27;], dtype=&#x27;timedelta64[ns]&#x27;, name=&#x27;lead_time&#x27;, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>init_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-c1a48799-ee48-4d8d-9604-7a88206c2e44' class='xr-index-data-in' type='checkbox'/><label for='index-c1a48799-ee48-4d8d-9604-7a88206c2e44' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2020-01-01 00:00:00&#x27;, &#x27;2020-01-01 12:00:00&#x27;], dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;init_time&#x27;, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>level</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-61c26ab7-8db1-4aa7-9690-856164be892e' class='xr-index-data-in' type='checkbox'/><label for='index-61c26ab7-8db1-4aa7-9690-856164be892e' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([50, 100, 150, 200, 250, 300, 400, 500, 600, 700, 850, 925, 1000], dtype=&#x27;int32&#x27;, name=&#x27;level&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-6ac4d6a8-18ac-49c2-baf9-c01829ba6e11' class='xr-section-summary-in' type='checkbox'  checked><label for='section-6ac4d6a8-18ac-49c2-baf9-c01829ba6e11' class='xr-section-summary' >Attributes: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>2 metre temperature</dd><dt><span>short_name :</span></dt><dd>t2m</dd><dt><span>standard_name :</span></dt><dd>unknown</dd><dt><span>units :</span></dt><dd>K</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 689kB\n", "Dimensions:         (latitude: 32, longitude: 64, lead_time: 3, init_time: 2,\n", "                     level: 13)\n", "Coordinates:\n", "  * latitude        (latitude) float64 256B -87.19 -81.56 -75.94 ... 81.56 87.19\n", "  * longitude       (longitude) float64 512B 0.0 5.625 11.25 ... 348.8 354.4\n", "  * lead_time       (lead_time) timedelta64[ns] 24B 06:00:00 12:00:00 18:00:00\n", "  * init_time       (init_time) datetime64[ns] 16B 2020-01-01 2020-01-01T12:0...\n", "  * level           (level) int32 52B 50 100 150 200 250 ... 700 ************\n", "Data variables:\n", "    2m_temperature  (init_time, lead_time, longitude, latitude) float32 49kB ...\n", "    geopotential    (init_time, lead_time, level, longitude, latitude) float32 639kB ...\n", "Attributes:\n", "    long_name:      2 metre temperature\n", "    short_name:     t2m\n", "    standard_name:  unknown\n", "    units:          K"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["prediction_chunk"]}, {"cell_type": "markdown", "id": "1d840001-c46a-49e8-8ccd-e74f805281fe", "metadata": {}, "source": ["Here we can see that the data loader took care of aligning the datasets, i.e. the target data (ERA5) has already been assigned an init and lead time coordinate."]}, {"cell_type": "markdown", "id": "5491b710-9278-4a7c-b151-9dfdb12e7baf", "metadata": {}, "source": ["## Metrics\n", "\n", "Next, we define the metrics to compute."]}, {"cell_type": "code", "execution_count": 8, "id": "bb689c8e-3ab3-4653-9393-baab495d9a89", "metadata": {}, "outputs": [], "source": ["metrics = {\n", "  'rmse': deterministic.RMSE(),\n", "  'mae': deterministic.MAE(),\n", "}"]}, {"cell_type": "markdown", "id": "202997ab-6b56-40fb-97a7-52d139354696", "metadata": {}, "source": ["Computing metrics happens in two steps. First, each metric defines one or several statistics that are required for computing the metric. A statistic is defined for each element of the prediction and target arrays, so e.g. for every init time, lead time, latitude and longitude.\n", "\n", "In the case of RMSE, the statistic would be the Squared Error."]}, {"cell_type": "code", "execution_count": 9, "id": "db50cf27-69d8-48c6-8baa-73ee6ebddb14", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'SquaredError': <weatherbenchX.metrics.deterministic.SquaredError at 0x15fe93890>}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["metrics['rmse'].statistics"]}, {"cell_type": "markdown", "id": "e0f7b3c3-ec6b-4ab2-a32f-50465d825b63", "metadata": {}, "source": ["The helper function below computes all the statistics for a dictionary of metrics. If several metrics use the same underlying statistic (e.g. RMSE and MSE), the statistic is only computed once. This requires all statistics to have unique names, which they define themselves (more on that later)."]}, {"cell_type": "code", "execution_count": 10, "id": "085dd754-2aed-4144-b85d-1d3990bc944c", "metadata": {}, "outputs": [], "source": ["statistics = metrics_base.compute_unique_statistics_for_all_metrics(\n", "  metrics, prediction_chunk, target_chunk\n", ")"]}, {"cell_type": "code", "execution_count": 11, "id": "d5bf7fee-fffe-40a3-85c3-f4b918710991", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['SquaredError', 'AbsoluteError'])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["statistics.keys()"]}, {"cell_type": "markdown", "id": "5bfc0252-3475-46fa-8aaf-84c7497e8520", "metadata": {}, "source": ["## Aggregation"]}, {"cell_type": "markdown", "id": "1796b187-b8d5-4d01-8673-b7a308d44c2d", "metadata": {}, "source": ["Then we average the statistics over the desired dimensions. In this simple case, we could just call stat.mean(dims). However, eventually the aggegation will have to happen over many chunks in the beam pipeline.\n", "\n", "To allow for multi-step aggregation, we first define an aggregator to reduce over a set of dimensions reduce_dims."]}, {"cell_type": "code", "execution_count": 12, "id": "dac1b362-35ba-42e9-8455-a6c569f89b44", "metadata": {}, "outputs": [], "source": ["aggregator = aggregation.Aggregator(\n", "  reduce_dims=['init_time', 'latitude', 'longitude'],\n", ")"]}, {"cell_type": "code", "execution_count": 13, "id": "728a9392-25f4-436f-ba5e-4698d9974001", "metadata": {}, "outputs": [], "source": ["aggregation_state = aggregator.aggregate_statistics(statistics)"]}, {"cell_type": "markdown", "id": "6fc791f1-2a20-4f7a-bc78-bfb6ae71ce23", "metadata": {}, "source": ["The aggregator then aggregates the statistics and produces an aggregation state. An aggregation state contains the sum of the aggregated statistics and the sum of the aggregated weights (without any additional weighting, this will just be 1 for each element in the original statistic arrays). These two can later be summed over many beam chunks.\n", "\n", "To get the final averaged statistics, we then divide the aggregated statistics over the aggregated weights. We can simple use the .mean_statistics() method for this."]}, {"cell_type": "code", "execution_count": 14, "id": "34313827-2044-4aeb-85af-0bee84732d75", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'AbsoluteError': <xarray.Dataset> Size: 244B\n", " Dimensions:         (lead_time: 3, level: 13)\n", " Coordinates:\n", "   * lead_time       (lead_time) timedelta64[ns] 24B 06:00:00 12:00:00 18:00:00\n", "   * level           (level) int32 52B 50 100 150 200 250 ... 700 ************\n", " Data variables:\n", "     2m_temperature  (lead_time) float32 12B 0.4815 0.5126 0.5184\n", "     geopotential    (lead_time, level) float32 156B 63.8 29.22 ... 27.43 29.05,\n", " 'SquaredError': <xarray.Dataset> Size: 244B\n", " Dimensions:         (lead_time: 3, level: 13)\n", " Coordinates:\n", "   * lead_time       (lead_time) timedelta64[ns] 24B 06:00:00 12:00:00 18:00:00\n", "   * level           (level) int32 52B 50 100 150 200 250 ... 700 ************\n", " Data variables:\n", "     2m_temperature  (lead_time) float32 12B 0.6213 0.7352 0.7081\n", "     geopotential    (lead_time, level) float32 156B 5.699e+03 ... 1.586e+03}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["aggregation_state.mean_statistics()"]}, {"cell_type": "markdown", "id": "5291047f-ddba-4304-8231-bd567e77f518", "metadata": {}, "source": ["The final step in computing the metrics is to now call the .value_from_mean_statistics() method for each metric, that takes the averaged statistics and converts it to the final metric. In the case of the RMSE, this would be taking the square root of the averaged squared error.\n", "\n", "The aggregation state has a handy shortcut for this that also packs up all metrics into a single Dataset with naming convention: `<metric>.<variable>`"]}, {"cell_type": "code", "execution_count": 15, "id": "88bb2475-a3da-418f-9c55-5a86386dc1a2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 412B\n", "Dimensions:              (lead_time: 3, level: 13)\n", "Coordinates:\n", "  * lead_time            (lead_time) timedelta64[ns] 24B 06:00:00 ... 18:00:00\n", "  * level                (level) int32 52B 50 100 150 200 ... 700 ************\n", "Data variables:\n", "    rmse.geopotential    (lead_time, level) float32 156B 75.49 37.59 ... 39.83\n", "    rmse.2m_temperature  (lead_time) float32 12B 0.7882 0.8575 0.8415\n", "    mae.geopotential     (lead_time, level) float32 156B 63.8 29.22 ... 29.05\n", "    mae.2m_temperature   (lead_time) float32 12B 0.4815 0.5126 0.5184</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-aae6dace-fda8-451f-b171-2d085abe3cf0' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-aae6dace-fda8-451f-b171-2d085abe3cf0' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>lead_time</span>: 3</li><li><span class='xr-has-index'>level</span>: 13</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-e1dd2e8d-2899-4610-8fab-1740a1442596' class='xr-section-summary-in' type='checkbox'  checked><label for='section-e1dd2e8d-2899-4610-8fab-1740a1442596' class='xr-section-summary' >Coordinates: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>lead_time</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>timedelta64[ns]</div><div class='xr-var-preview xr-preview'>06:00:00 12:00:00 18:00:00</div><input id='attrs-c4c9d645-8f86-4c84-ab84-6afb3631a953' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-c4c9d645-8f86-4c84-ab84-6afb3631a953' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e362777b-2d41-4980-bee2-eddcc932d8e2' class='xr-var-data-in' type='checkbox'><label for='data-e362777b-2d41-4980-bee2-eddcc932d8e2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>time since forecast_reference_time</dd><dt><span>standard_name :</span></dt><dd>forecast_period</dd></dl></div><div class='xr-var-data'><pre>array([21600000000000, 43200000000000, 64800000000000], dtype=&#x27;timedelta64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>level</span></div><div class='xr-var-dims'>(level)</div><div class='xr-var-dtype'>int32</div><div class='xr-var-preview xr-preview'>50 100 150 200 ... 700 ************</div><input id='attrs-7a610e25-65c4-4f40-be46-23dcd619fa7c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-7a610e25-65c4-4f40-be46-23dcd619fa7c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-75ac178d-7763-47c5-946a-07c7ba0d2e82' class='xr-var-data-in' type='checkbox'><label for='data-75ac178d-7763-47c5-946a-07c7ba0d2e82' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  50,  100,  150,  200,  250,  300,  400,  500,  600,  700,  850,  925,\n", "       1000], dtype=int32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-c4caaa57-807f-4565-a695-e05ec46031a5' class='xr-section-summary-in' type='checkbox'  checked><label for='section-c4caaa57-807f-4565-a695-e05ec46031a5' class='xr-section-summary' >Data variables: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>rmse.geopotential</span></div><div class='xr-var-dims'>(lead_time, level)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>75.49 37.59 31.97 ... 36.88 39.83</div><input id='attrs-0547c0f8-f32d-40d7-891d-ef03c36520c7' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0547c0f8-f32d-40d7-891d-ef03c36520c7' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-2bf30d4b-2de9-451f-96ce-bb7d572f0331' class='xr-var-data-in' type='checkbox'><label for='data-2bf30d4b-2de9-451f-96ce-bb7d572f0331' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[75.49132 , 37.58569 , 31.970161, 30.644703, 30.151325, 31.598156,\n", "        31.044577, 28.699232, 26.220144, 24.806454, 26.15554 , 27.328508,\n", "        29.755255],\n", "       [94.789825, 52.165535, 46.70831 , 45.450172, 45.48635 , 46.279625,\n", "        45.005222, 41.79898 , 37.44367 , 34.133392, 34.392757, 36.026566,\n", "        38.95175 ],\n", "       [93.16943 , 53.39042 , 48.961945, 48.343647, 48.601547, 49.942287,\n", "        47.54569 , 42.956726, 38.259327, 35.028004, 34.88078 , 36.881992,\n", "        39.82879 ]], dtype=float32)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>rmse.2m_temperature</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>0.7882 0.8575 0.8415</div><input id='attrs-2f435184-efc3-4989-b955-cebc1a7deb1d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-2f435184-efc3-4989-b955-cebc1a7deb1d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d02b6ef4-f9c9-4499-ad7a-b098b967fa6e' class='xr-var-data-in' type='checkbox'><label for='data-d02b6ef4-f9c9-4499-ad7a-b098b967fa6e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([0.7881963, 0.8574583, 0.8414594], dtype=float32)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>mae.geopotential</span></div><div class='xr-var-dims'>(lead_time, level)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>63.8 29.22 24.69 ... 27.43 29.05</div><input id='attrs-bd7b3b02-7502-4db9-b221-ee089e1f2b31' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-bd7b3b02-7502-4db9-b221-ee089e1f2b31' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-511cc0cb-bb6a-442c-bd9c-3af37a191cd3' class='xr-var-data-in' type='checkbox'><label for='data-511cc0cb-bb6a-442c-bd9c-3af37a191cd3' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[63.80334 , 29.218952, 24.687426, 23.915564, 23.580402, 24.685104,\n", "        23.680792, 21.687527, 20.167805, 19.34892 , 19.435905, 19.47594 ,\n", "        20.89297 ],\n", "       [81.08994 , 40.14852 , 35.753677, 35.544586, 35.310184, 35.826794,\n", "        33.757935, 30.76265 , 28.142145, 25.904856, 25.543291, 26.42314 ,\n", "        28.11867 ],\n", "       [79.2354  , 41.34342 , 37.62435 , 38.091377, 37.941566, 38.578632,\n", "        35.786827, 31.889713, 28.923437, 26.773956, 26.267748, 27.427967,\n", "        29.049171]], dtype=float32)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>mae.2m_temperature</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>0.4815 0.5126 0.5184</div><input id='attrs-92fb7dca-48c1-4a8e-a362-01d698ce5a63' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-92fb7dca-48c1-4a8e-a362-01d698ce5a63' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-0be1a777-7070-469e-b0de-a1f41593ff3f' class='xr-var-data-in' type='checkbox'><label for='data-0be1a777-7070-469e-b0de-a1f41593ff3f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([0.48146248, 0.512573  , 0.5183978 ], dtype=float32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-04ef6d52-569d-4c2d-adb3-1742d0c518ae' class='xr-section-summary-in' type='checkbox'  ><label for='section-04ef6d52-569d-4c2d-adb3-1742d0c518ae' class='xr-section-summary' >Indexes: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>lead_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-c4ca6e42-2e10-4077-b2db-de25fcf476f0' class='xr-index-data-in' type='checkbox'/><label for='index-c4ca6e42-2e10-4077-b2db-de25fcf476f0' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(TimedeltaIndex([&#x27;0 days 06:00:00&#x27;, &#x27;0 days 12:00:00&#x27;, &#x27;0 days 18:00:00&#x27;], dtype=&#x27;timedelta64[ns]&#x27;, name=&#x27;lead_time&#x27;, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>level</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-e7f85803-3a62-479b-9cc5-50dea45bcf32' class='xr-index-data-in' type='checkbox'/><label for='index-e7f85803-3a62-479b-9cc5-50dea45bcf32' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([50, 100, 150, 200, 250, 300, 400, 500, 600, 700, 850, 925, 1000], dtype=&#x27;int32&#x27;, name=&#x27;level&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-6be193f6-b40a-4bd1-871a-7207f617fd8d' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-6be193f6-b40a-4bd1-871a-7207f617fd8d' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 412B\n", "Dimensions:              (lead_time: 3, level: 13)\n", "Coordinates:\n", "  * lead_time            (lead_time) timedelta64[ns] 24B 06:00:00 ... 18:00:00\n", "  * level                (level) int32 52B 50 100 150 200 ... 700 ************\n", "Data variables:\n", "    rmse.geopotential    (lead_time, level) float32 156B 75.49 37.59 ... 39.83\n", "    rmse.2m_temperature  (lead_time) float32 12B 0.7882 0.8575 0.8415\n", "    mae.geopotential     (lead_time, level) float32 156B 63.8 29.22 ... 29.05\n", "    mae.2m_temperature   (lead_time) float32 12B 0.4815 0.5126 0.5184"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["aggregation_state.metric_values(metrics)"]}, {"cell_type": "markdown", "id": "20f5f22c-f0d1-46c1-ac6b-7daf3d215314", "metadata": {}, "source": ["This may seem like a lot of separate steps to get to the final result. This is necessary because, in many use cases, the computation will be parallelized over many chunks. There is a shortcut function for a single chunks that includes the steps above:"]}, {"cell_type": "code", "execution_count": 16, "id": "34608fdf-6c11-42ed-b3dc-fea625b810c4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 412B\n", "Dimensions:              (lead_time: 3, level: 13)\n", "Coordinates:\n", "  * lead_time            (lead_time) timedelta64[ns] 24B 06:00:00 ... 18:00:00\n", "  * level                (level) int32 52B 50 100 150 200 ... 700 ************\n", "Data variables:\n", "    rmse.geopotential    (lead_time, level) float32 156B 75.49 37.59 ... 39.83\n", "    rmse.2m_temperature  (lead_time) float32 12B 0.7882 0.8575 0.8415\n", "    mae.geopotential     (lead_time, level) float32 156B 63.8 29.22 ... 29.05\n", "    mae.2m_temperature   (lead_time) float32 12B 0.4815 0.5126 0.5184</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-00f20380-a09b-4016-893a-35960cc60919' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-00f20380-a09b-4016-893a-35960cc60919' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>lead_time</span>: 3</li><li><span class='xr-has-index'>level</span>: 13</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-cc472887-6b59-414b-8bf1-28260f1fcd95' class='xr-section-summary-in' type='checkbox'  checked><label for='section-cc472887-6b59-414b-8bf1-28260f1fcd95' class='xr-section-summary' >Coordinates: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>lead_time</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>timedelta64[ns]</div><div class='xr-var-preview xr-preview'>06:00:00 12:00:00 18:00:00</div><input id='attrs-51cb5936-a133-428b-92ca-d6932268d7d4' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-51cb5936-a133-428b-92ca-d6932268d7d4' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-3a39a139-ef8a-417b-b911-e80534222cf3' class='xr-var-data-in' type='checkbox'><label for='data-3a39a139-ef8a-417b-b911-e80534222cf3' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>time since forecast_reference_time</dd><dt><span>standard_name :</span></dt><dd>forecast_period</dd></dl></div><div class='xr-var-data'><pre>array([21600000000000, 43200000000000, 64800000000000], dtype=&#x27;timedelta64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>level</span></div><div class='xr-var-dims'>(level)</div><div class='xr-var-dtype'>int32</div><div class='xr-var-preview xr-preview'>50 100 150 200 ... 700 ************</div><input id='attrs-a3d421e1-97c0-48f1-8f10-7a44d709eb8d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-a3d421e1-97c0-48f1-8f10-7a44d709eb8d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-57a6fedd-bb96-4a28-b935-93c626ac5314' class='xr-var-data-in' type='checkbox'><label for='data-57a6fedd-bb96-4a28-b935-93c626ac5314' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  50,  100,  150,  200,  250,  300,  400,  500,  600,  700,  850,  925,\n", "       1000], dtype=int32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-a0ad8f4f-4529-4267-8c02-5c4eadd92fb7' class='xr-section-summary-in' type='checkbox'  checked><label for='section-a0ad8f4f-4529-4267-8c02-5c4eadd92fb7' class='xr-section-summary' >Data variables: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>rmse.geopotential</span></div><div class='xr-var-dims'>(lead_time, level)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>75.49 37.59 31.97 ... 36.88 39.83</div><input id='attrs-f6ca82b9-8b71-4b69-8260-5a42c4a18c7e' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-f6ca82b9-8b71-4b69-8260-5a42c4a18c7e' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d7405c79-20e8-424f-8bca-b51e75c21262' class='xr-var-data-in' type='checkbox'><label for='data-d7405c79-20e8-424f-8bca-b51e75c21262' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[75.49132 , 37.58569 , 31.970161, 30.644703, 30.151325, 31.598156,\n", "        31.044577, 28.699232, 26.220144, 24.806454, 26.15554 , 27.328508,\n", "        29.755255],\n", "       [94.789825, 52.165535, 46.70831 , 45.450172, 45.48635 , 46.279625,\n", "        45.005222, 41.79898 , 37.44367 , 34.133392, 34.392757, 36.026566,\n", "        38.95175 ],\n", "       [93.16943 , 53.39042 , 48.961945, 48.343647, 48.601547, 49.942287,\n", "        47.54569 , 42.956726, 38.259327, 35.028004, 34.88078 , 36.881992,\n", "        39.82879 ]], dtype=float32)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>rmse.2m_temperature</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>0.7882 0.8575 0.8415</div><input id='attrs-5b717a67-c679-4a21-bc33-d4c882cdc3d0' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-5b717a67-c679-4a21-bc33-d4c882cdc3d0' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d8ab21dc-5f0e-4cb4-9fe6-59fc630134d2' class='xr-var-data-in' type='checkbox'><label for='data-d8ab21dc-5f0e-4cb4-9fe6-59fc630134d2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([0.7881963, 0.8574583, 0.8414594], dtype=float32)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>mae.geopotential</span></div><div class='xr-var-dims'>(lead_time, level)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>63.8 29.22 24.69 ... 27.43 29.05</div><input id='attrs-41a34902-0b12-4be6-93c4-63f1b43713b8' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-41a34902-0b12-4be6-93c4-63f1b43713b8' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-2a7052e7-13c0-4ce2-a529-18cd3b7f0b95' class='xr-var-data-in' type='checkbox'><label for='data-2a7052e7-13c0-4ce2-a529-18cd3b7f0b95' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[63.80334 , 29.218952, 24.687426, 23.915564, 23.580402, 24.685104,\n", "        23.680792, 21.687527, 20.167805, 19.34892 , 19.435905, 19.47594 ,\n", "        20.89297 ],\n", "       [81.08994 , 40.14852 , 35.753677, 35.544586, 35.310184, 35.826794,\n", "        33.757935, 30.76265 , 28.142145, 25.904856, 25.543291, 26.42314 ,\n", "        28.11867 ],\n", "       [79.2354  , 41.34342 , 37.62435 , 38.091377, 37.941566, 38.578632,\n", "        35.786827, 31.889713, 28.923437, 26.773956, 26.267748, 27.427967,\n", "        29.049171]], dtype=float32)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>mae.2m_temperature</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>0.4815 0.5126 0.5184</div><input id='attrs-ef30906c-a1be-4ac2-803f-cea961eba457' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ef30906c-a1be-4ac2-803f-cea961eba457' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7d9c49ef-88c7-4335-8af1-a8a8080deee4' class='xr-var-data-in' type='checkbox'><label for='data-7d9c49ef-88c7-4335-8af1-a8a8080deee4' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([0.48146248, 0.512573  , 0.5183978 ], dtype=float32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-d55d4a03-1a68-4547-b8df-593a8968ff7d' class='xr-section-summary-in' type='checkbox'  ><label for='section-d55d4a03-1a68-4547-b8df-593a8968ff7d' class='xr-section-summary' >Indexes: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>lead_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-9450bd78-586f-41c2-b3bb-e42ae671954d' class='xr-index-data-in' type='checkbox'/><label for='index-9450bd78-586f-41c2-b3bb-e42ae671954d' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(TimedeltaIndex([&#x27;0 days 06:00:00&#x27;, &#x27;0 days 12:00:00&#x27;, &#x27;0 days 18:00:00&#x27;], dtype=&#x27;timedelta64[ns]&#x27;, name=&#x27;lead_time&#x27;, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>level</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-0e8076b8-a959-4076-84ad-23ff4f60060d' class='xr-index-data-in' type='checkbox'/><label for='index-0e8076b8-a959-4076-84ad-23ff4f60060d' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([50, 100, 150, 200, 250, 300, 400, 500, 600, 700, 850, 925, 1000], dtype=&#x27;int32&#x27;, name=&#x27;level&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-ca1c76cd-e7da-42f1-bdc3-4cc2548fb94b' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-ca1c76cd-e7da-42f1-bdc3-4cc2548fb94b' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 412B\n", "Dimensions:              (lead_time: 3, level: 13)\n", "Coordinates:\n", "  * lead_time            (lead_time) timedelta64[ns] 24B 06:00:00 ... 18:00:00\n", "  * level                (level) int32 52B 50 100 150 200 ... 700 ************\n", "Data variables:\n", "    rmse.geopotential    (lead_time, level) float32 156B 75.49 37.59 ... 39.83\n", "    rmse.2m_temperature  (lead_time) float32 12B 0.7882 0.8575 0.8415\n", "    mae.geopotential     (lead_time, level) float32 156B 63.8 29.22 ... 29.05\n", "    mae.2m_temperature   (lead_time) float32 12B 0.4815 0.5126 0.5184"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["aggregation.compute_metric_values_for_single_chunk(\n", "    metrics,\n", "    aggregator,\n", "    prediction_chunk,\n", "    target_chunk\n", ")"]}, {"cell_type": "markdown", "id": "4830758f-f6ef-4257-9cdc-87fd50bdf369", "metadata": {}, "source": ["## Weighting and Binning\n", "\n", "This is already it for the simplest example. However, in many cases, we might want more fine-grained aggregation.\n", "\n", "One common case is weighting each element differently in the aggregation. For lat-lon datasets, for example, it is common to weigh each grid point by area. This can be done using a GridAreaWeighting object."]}, {"cell_type": "code", "execution_count": 17, "id": "bdefca62-c9f3-4f5e-8e14-e46c83848ae4", "metadata": {}, "outputs": [], "source": ["weigh_by = [weighting.GridAreaWeighting()]"]}, {"cell_type": "markdown", "id": "8898b5ce-26d0-465d-8dbf-87d57deef5bf", "metadata": {}, "source": ["Another common case is f<PERSON><PERSON> subdividing the aggregation, e.g. computing metrics for several regions. This is done using binning instances.\n", "\n", "Important: Make sure the longitude conventions (-180 to 180 or 0 to 360) match between the data and the regions."]}, {"cell_type": "code", "execution_count": 18, "id": "854238d7-9a1e-4bd7-8520-313346ffe010", "metadata": {}, "outputs": [], "source": ["regions = {\n", "    # ((lat_min, lat_max), (lon_min, lon_max))\n", "    'global': ((-90, 90), (0, 360)),\n", "    'na': ((24.08, 50), (360 - 126, 360 - 65)),\n", "    'europe': ((35, 71), (360 - 10, 36)),\n", "}\n", "bin_by = [binning.Regions(regions)]"]}, {"cell_type": "code", "execution_count": 19, "id": "2fefd193-c024-4411-bb6b-c1b3d43dc123", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 2kB\n", "Dimensions:              (region: 3, lead_time: 3, level: 13)\n", "Coordinates:\n", "  * region               (region) &lt;U6 72B &#x27;global&#x27; &#x27;na&#x27; &#x27;europe&#x27;\n", "  * lead_time            (lead_time) timedelta64[ns] 24B 06:00:00 ... 18:00:00\n", "  * level                (level) int32 52B 50 100 150 200 ... 700 ************\n", "Data variables:\n", "    rmse.geopotential    (region, lead_time, level) float64 936B 79.77 ... 31.93\n", "    rmse.2m_temperature  (region, lead_time) float64 72B 0.6428 ... 0.6352\n", "    mae.geopotential     (region, lead_time, level) float64 936B 68.75 ... 24.72\n", "    mae.2m_temperature   (region, lead_time) float64 72B 0.3706 0.397 ... 0.4744</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-33373db3-872f-405d-9b33-8ab32fe84aa9' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-33373db3-872f-405d-9b33-8ab32fe84aa9' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>region</span>: 3</li><li><span class='xr-has-index'>lead_time</span>: 3</li><li><span class='xr-has-index'>level</span>: 13</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-556a054d-4e93-4776-b06d-d3f5ef642557' class='xr-section-summary-in' type='checkbox'  checked><label for='section-556a054d-4e93-4776-b06d-d3f5ef642557' class='xr-section-summary' >Coordinates: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>region</span></div><div class='xr-var-dims'>(region)</div><div class='xr-var-dtype'>&lt;U6</div><div class='xr-var-preview xr-preview'>&#x27;global&#x27; &#x27;na&#x27; &#x27;europe&#x27;</div><input id='attrs-748008e6-9685-423a-9e7e-3c2b67c4ecc9' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-748008e6-9685-423a-9e7e-3c2b67c4ecc9' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7dc514e1-9ce4-4939-8404-03571112e4bd' class='xr-var-data-in' type='checkbox'><label for='data-7dc514e1-9ce4-4939-8404-03571112e4bd' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;global&#x27;, &#x27;na&#x27;, &#x27;europe&#x27;], dtype=&#x27;&lt;U6&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>lead_time</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>timedelta64[ns]</div><div class='xr-var-preview xr-preview'>06:00:00 12:00:00 18:00:00</div><input id='attrs-705427fa-8709-4254-87da-580ad9b41a63' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-705427fa-8709-4254-87da-580ad9b41a63' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-98212219-d3a9-4b1d-9792-469944dc101b' class='xr-var-data-in' type='checkbox'><label for='data-98212219-d3a9-4b1d-9792-469944dc101b' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>time since forecast_reference_time</dd><dt><span>standard_name :</span></dt><dd>forecast_period</dd></dl></div><div class='xr-var-data'><pre>array([21600000000000, 43200000000000, 64800000000000], dtype=&#x27;timedelta64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>level</span></div><div class='xr-var-dims'>(level)</div><div class='xr-var-dtype'>int32</div><div class='xr-var-preview xr-preview'>50 100 150 200 ... 700 ************</div><input id='attrs-6ddd3b8c-d031-43fa-bf98-0072600c8852' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-6ddd3b8c-d031-43fa-bf98-0072600c8852' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-dc37e8f1-7e29-4f0d-bacd-9d814fa274e3' class='xr-var-data-in' type='checkbox'><label for='data-dc37e8f1-7e29-4f0d-bacd-9d814fa274e3' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  50,  100,  150,  200,  250,  300,  400,  500,  600,  700,  850,  925,\n", "       1000], dtype=int32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-ab723f6b-b8b5-4c52-81df-47880ab9f261' class='xr-section-summary-in' type='checkbox'  checked><label for='section-ab723f6b-b8b5-4c52-81df-47880ab9f261' class='xr-section-summary' >Data variables: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>rmse.geopotential</span></div><div class='xr-var-dims'>(region, lead_time, level)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>79.77 37.95 32.06 ... 30.18 31.93</div><input id='attrs-790db022-3599-4e96-9c95-83aa91db3edb' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-790db022-3599-4e96-9c95-83aa91db3edb' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-3e4b3a3c-c5d3-4ff9-b191-f7f4dfd4851c' class='xr-var-data-in' type='checkbox'><label for='data-3e4b3a3c-c5d3-4ff9-b191-f7f4dfd4851c' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[[ 79.76629829,  37.94646238,  32.06301895,  29.97924039,\n", "          29.32525194,  29.24569887,  27.30035932,  24.56926637,\n", "          23.3812289 ,  23.31419235,  25.05560183,  25.87004026,\n", "          27.36407861],\n", "        [101.86635208,  54.94044223,  47.68830888,  45.02587606,\n", "          43.97650289,  42.88138943,  38.86206296,  35.28874463,\n", "          32.83484367,  30.98304431,  31.54030545,  32.74269898,\n", "          34.80828341],\n", "        [ 96.84978498,  56.10706353,  50.97625234,  48.40848469,\n", "          46.64171709,  45.374744  ,  41.24996544,  36.95531341,\n", "          33.7727835 ,  32.38636182,  33.09332058,  34.80985368,\n", "          37.01123082]],\n", "\n", "       [[ 70.62260312,  29.48207397,  19.59311458,  23.27281906,\n", "          26.95701754,  30.10171432,  29.67201467,  25.59203393,\n", "          22.97974628,  21.40584277,  20.50531191,  21.50531977,\n", "          23.33405298],\n", "        [ 89.42533481,  35.86227665,  27.56598077,  35.81220546,\n", "          40.01182231,  47.24858566,  43.88162922,  36.38628228,\n", "          30.69355233,  26.89782182,  32.52047659,  38.57356418,\n", "          45.03002692],\n", "        [ 82.78097647,  46.88834746,  50.13664673,  59.0097692 ,\n", "          63.04512473,  69.88831146,  67.9072849 ,  60.87778727,\n", "          54.37377436,  49.81147514,  52.45794264,  56.20617036,\n", "          59.06623108]],\n", "\n", "       [[ 77.70640581,  34.18196047,  18.62135082,  22.0503069 ,\n", "          23.30801771,  24.28794956,  24.70127475,  19.93573632,\n", "          18.75041818,  17.21248291,  15.31772237,  15.86119079,\n", "          16.25773208],\n", "        [111.52613072,  52.586227  ,  37.57662097,  36.08346072,\n", "          41.46785886,  42.64541009,  38.61561981,  34.54016518,\n", "          31.35536107,  28.99355461,  26.04527963,  24.90386588,\n", "          26.43495652],\n", "        [105.87535034,  48.18940913,  37.98210797,  45.36724791,\n", "          49.86399498,  49.93141125,  45.08430114,  38.30552358,\n", "          34.99226709,  31.89099813,  28.44465655,  30.18268067,\n", "          31.93419194]]])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>rmse.2m_temperature</span></div><div class='xr-var-dims'>(region, lead_time)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.6428 0.7127 ... 0.6404 0.6352</div><input id='attrs-f2d56e19-fc0b-4fb7-aede-bd4e33c2315f' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-f2d56e19-fc0b-4fb7-aede-bd4e33c2315f' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-383e514f-22f3-43a2-8898-cbccbad079e2' class='xr-var-data-in' type='checkbox'><label for='data-383e514f-22f3-43a2-8898-cbccbad079e2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[0.64278982, 0.71273283, 0.67592313],\n", "       [0.6562773 , 0.89739375, 0.74780693],\n", "       [0.55461279, 0.64042203, 0.63517952]])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>mae.geopotential</span></div><div class='xr-var-dims'>(region, lead_time, level)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>68.75 29.36 24.83 ... 24.17 24.72</div><input id='attrs-57795dd9-994a-420e-bf15-28a8c9d3f0dc' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-57795dd9-994a-420e-bf15-28a8c9d3f0dc' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a207ac2c-a35f-4a9a-9965-85e4cf10ca32' class='xr-var-data-in' type='checkbox'><label for='data-a207ac2c-a35f-4a9a-9965-85e4cf10ca32' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[[ 68.75114898,  29.3608345 ,  24.83096075,  23.48512031,\n", "          22.81636017,  22.73525152,  21.09874323,  19.01389104,\n", "          18.06762836,  17.86973539,  18.50426949,  18.56987456,\n", "          19.46776479],\n", "        [ 89.09027754,  42.13302037,  36.46676404,  35.24234086,\n", "          34.09886122,  33.13590761,  29.92097152,  27.01545809,\n", "          25.11764692,  23.57555004,  23.53934068,  24.24741118,\n", "          25.50848126],\n", "        [ 83.7116233 ,  43.13140671,  39.07810638,  38.09978482,\n", "          36.24365736,  34.96731345,  31.23281162,  27.82225796,\n", "          25.58371858,  24.65189645,  24.72637447,  25.63279005,\n", "          26.79603224]],\n", "\n", "       [[ 66.43880133,  24.68500933,  16.04461745,  19.57601383,\n", "          22.19197405,  24.76312361,  25.556364  ,  21.90362346,\n", "          19.56323222,  18.27254265,  16.29256371,  17.15104531,\n", "          18.74105319],\n", "        [ 80.8431865 ,  28.83043205,  22.22330657,  27.47371585,\n", "          30.739891  ,  36.5020035 ,  32.67690531,  26.42677917,\n", "          22.3445659 ,  20.34470197,  24.38076037,  29.51847395,\n", "          34.05762476],\n", "        [ 68.39839157,  41.5004217 ,  43.8707774 ,  48.91881748,\n", "          52.05906865,  56.84714928,  53.4842058 ,  47.98855849,\n", "          42.99958314,  40.61686435,  44.35207695,  46.89023455,\n", "          47.85330653]],\n", "\n", "       [[ 72.68999124,  28.51455261,  15.12592303,  18.04095495,\n", "          19.39226406,  20.30608331,  20.55884504,  16.35644111,\n", "          15.33494195,  13.86799541,  11.68726124,  12.24016604,\n", "          12.85787486],\n", "        [104.42660994,  44.55940415,  30.47125628,  29.35158681,\n", "          32.77217391,  33.23077002,  29.85587965,  26.0563324 ,\n", "          23.77813008,  21.42724151,  18.53629504,  18.05660711,\n", "          19.87013726],\n", "        [ 99.73641858,  40.16488972,  29.53748277,  36.06006352,\n", "          38.4056916 ,  37.72692925,  34.94317219,  28.06726286,\n", "          24.867978  ,  24.1051114 ,  22.90255763,  24.16618509,\n", "          24.72274949]]])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>mae.2m_temperature</span></div><div class='xr-var-dims'>(region, lead_time)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.3706 0.397 ... 0.4824 0.4744</div><input id='attrs-2e5f8206-c2ff-44a3-87bf-058ace15fd80' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-2e5f8206-c2ff-44a3-87bf-058ace15fd80' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d7558aad-ce97-4d5f-895c-1e4e0b04b103' class='xr-var-data-in' type='checkbox'><label for='data-d7558aad-ce97-4d5f-895c-1e4e0b04b103' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[0.37055574, 0.39696854, 0.39215647],\n", "       [0.4901805 , 0.62216171, 0.51013294],\n", "       [0.41340372, 0.48240374, 0.47439991]])</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-ee98559d-7956-4e84-9acc-f8e2da082295' class='xr-section-summary-in' type='checkbox'  ><label for='section-ee98559d-7956-4e84-9acc-f8e2da082295' class='xr-section-summary' >Indexes: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>region</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-640f923e-5cdd-421f-afbb-7525d1d62eb0' class='xr-index-data-in' type='checkbox'/><label for='index-640f923e-5cdd-421f-afbb-7525d1d62eb0' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([&#x27;global&#x27;, &#x27;na&#x27;, &#x27;europe&#x27;], dtype=&#x27;object&#x27;, name=&#x27;region&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>lead_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-94d22c14-6318-410a-a452-685b730152f8' class='xr-index-data-in' type='checkbox'/><label for='index-94d22c14-6318-410a-a452-685b730152f8' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(TimedeltaIndex([&#x27;0 days 06:00:00&#x27;, &#x27;0 days 12:00:00&#x27;, &#x27;0 days 18:00:00&#x27;], dtype=&#x27;timedelta64[ns]&#x27;, name=&#x27;lead_time&#x27;, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>level</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-03d9136c-876e-4a71-a3f4-a1ae40ade6df' class='xr-index-data-in' type='checkbox'/><label for='index-03d9136c-876e-4a71-a3f4-a1ae40ade6df' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([50, 100, 150, 200, 250, 300, 400, 500, 600, 700, 850, 925, 1000], dtype=&#x27;int32&#x27;, name=&#x27;level&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-10806100-8d02-4922-b130-4c7eb623d278' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-10806100-8d02-4922-b130-4c7eb623d278' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 2kB\n", "Dimensions:              (region: 3, lead_time: 3, level: 13)\n", "Coordinates:\n", "  * region               (region) <U6 72B 'global' 'na' 'europe'\n", "  * lead_time            (lead_time) timedelta64[ns] 24B 06:00:00 ... 18:00:00\n", "  * level                (level) int32 52B 50 100 150 200 ... 700 ************\n", "Data variables:\n", "    rmse.geopotential    (region, lead_time, level) float64 936B 79.77 ... 31.93\n", "    rmse.2m_temperature  (region, lead_time) float64 72B 0.6428 ... 0.6352\n", "    mae.geopotential     (region, lead_time, level) float64 936B 68.75 ... 24.72\n", "    mae.2m_temperature   (region, lead_time) float64 72B 0.3706 0.397 ... 0.4744"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["aggregator = aggregation.Aggregator(\n", "  reduce_dims=['init_time', 'latitude', 'longitude'],\n", "  bin_by=bin_by,\n", "  weigh_by=weigh_by,\n", ")\n", "aggregation.compute_metric_values_for_single_chunk(\n", "    metrics,\n", "    aggregator,\n", "    prediction_chunk,\n", "    target_chunk\n", ")"]}, {"cell_type": "markdown", "id": "f9f417ca-3333-43ba-b62c-c56ccd819377", "metadata": {}, "source": ["The results will now have an additional dimension for the region bins."]}, {"cell_type": "markdown", "id": "d3adb6f4-302e-42e7-af5a-b33dcbd8f9b7", "metadata": {}, "source": ["## Beam pipeline"]}, {"cell_type": "markdown", "id": "1c8b1cc9-0ad5-48a4-b4fc-5bf3ad255a53", "metadata": {}, "source": ["Now let's put this same example into a beam pipeline that could be scaled to much larger datasets.\n", "\n", "The first step in defining a beam pipeline is to define the time chunks. The beam computation will be split into chunks according to init/lead time chunks. Currently, only chunking over the two time dimensions is supported (i.e. not over other coordinates like latitude or longitude).\n", "\n", "To define these, there is a TimeChunks class that handles the chunking.\n", "\n", "Let's define a range of 4 init and 3 lead times."]}, {"cell_type": "code", "execution_count": 20, "id": "a4dfb2ad-c752-433a-9b88-af3d0e2afc9a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(array(['2020-01-01T00:00:00.000000000', '2020-01-01T12:00:00.000000000',\n", "        '2020-01-02T00:00:00.000000000', '2020-01-02T12:00:00.000000000'],\n", "       dtype='datetime64[ns]'),\n", " array([             0, 21600000000000, 43200000000000],\n", "       dtype='timedelta64[ns]'))"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["init_times = np.arange('2020-01-01T00', '2020-01-03T00', np.timedelta64(12, 'h'), dtype='datetime64[ns]')\n", "lead_times = np.arange(0, 18, 6, dtype='timedelta64[h]').astype('timedelta64[ns]')\n", "init_times, lead_times"]}, {"cell_type": "markdown", "id": "9b55c379-f819-4f17-9fb7-d67110ccf41b", "metadata": {}, "source": ["Now we need to tell the time chunker what chunk sizes to use in init/lead time.\n", "\n", "The time chunker is an iterator that returns the appropriate init/lead time chunks for the chosen chunk sizes.\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "id": "d3544c51-e6ef-4046-8876-cd58ddfa0357", "metadata": {}, "outputs": [{"data": {"text/plain": ["(array(['2020-01-01T00:00:00.000000000', '2020-01-01T12:00:00.000000000'],\n", "       dtype='datetime64[ns]'),\n", " array([0], dtype='timedelta64[ns]'))"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["times = time_chunks.TimeChunks(\n", "  init_times,\n", "  lead_times,\n", "  init_time_chunk_size=2,\n", "  lead_time_chunk_size=1\n", ")\n", "next(iter(times))"]}, {"cell_type": "markdown", "id": "089a8fa4-1a16-4aad-aa04-7aa30e32b7f8", "metadata": {}, "source": ["Finally we can pass all these arguments to define_pipeline(). This will set up the beam pipline. The metric results will be saved as a NetCDF file."]}, {"cell_type": "code", "execution_count": null, "id": "fbac059a-dd89-4f2e-99e5-7b7f3d1b8537", "metadata": {}, "outputs": [], "source": ["root = beam.Pipeline(runner='DirectRunner')\n", "beam_pipeline.define_pipeline(\n", "    root=root,\n", "    times=times,\n", "    predictions_loader=prediction_data_loader,\n", "    targets_loader=target_data_loader,\n", "    metrics=metrics,\n", "    aggregator=aggregator,\n", "    out_path='./out.nc',\n", ")\n", "root.run()"]}, {"cell_type": "code", "execution_count": 23, "id": "fc0439e2-2f1c-4517-a9a6-01384503664e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 2kB\n", "Dimensions:              (level: 13, region: 3, lead_time: 3)\n", "Coordinates:\n", "  * level                (level) int32 52B 50 100 150 200 ... 700 ************\n", "  * region               (region) object 24B &#x27;global&#x27; &#x27;na&#x27; &#x27;europe&#x27;\n", "  * lead_time            (lead_time) timedelta64[ns] 24B 00:00:00 ... 12:00:00\n", "Data variables:\n", "    rmse.geopotential    (region, lead_time, level) float64 936B 82.88 ... 29.48\n", "    mae.geopotential     (region, lead_time, level) float64 936B 74.44 ... 22.35\n", "    rmse.2m_temperature  (region, lead_time) float64 72B 0.6832 ... 0.6497\n", "    mae.2m_temperature   (region, lead_time) float64 72B 0.3887 ... 0.4813</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-d453e25e-c9a9-47aa-b605-a4d561f4752c' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-d453e25e-c9a9-47aa-b605-a4d561f4752c' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>level</span>: 13</li><li><span class='xr-has-index'>region</span>: 3</li><li><span class='xr-has-index'>lead_time</span>: 3</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-fab06eec-c5ab-43f7-85d0-cb9153830bc5' class='xr-section-summary-in' type='checkbox'  checked><label for='section-fab06eec-c5ab-43f7-85d0-cb9153830bc5' class='xr-section-summary' >Coordinates: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>level</span></div><div class='xr-var-dims'>(level)</div><div class='xr-var-dtype'>int32</div><div class='xr-var-preview xr-preview'>50 100 150 200 ... 700 ************</div><input id='attrs-4689fb18-5a66-45d9-9735-2880502797ac' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-4689fb18-5a66-45d9-9735-2880502797ac' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c60c6604-9a19-4ca0-b060-2c064ece0b88' class='xr-var-data-in' type='checkbox'><label for='data-c60c6604-9a19-4ca0-b060-2c064ece0b88' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  50,  100,  150,  200,  250,  300,  400,  500,  600,  700,  850,  925,\n", "       1000], dtype=int32)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>region</span></div><div class='xr-var-dims'>(region)</div><div class='xr-var-dtype'>object</div><div class='xr-var-preview xr-preview'>&#x27;global&#x27; &#x27;na&#x27; &#x27;europe&#x27;</div><input id='attrs-f9d7f4f0-6ffb-4cb8-aac6-02fea36892ac' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-f9d7f4f0-6ffb-4cb8-aac6-02fea36892ac' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-77907a24-b59e-4583-86ba-5c895506b9cb' class='xr-var-data-in' type='checkbox'><label for='data-77907a24-b59e-4583-86ba-5c895506b9cb' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;global&#x27;, &#x27;na&#x27;, &#x27;europe&#x27;], dtype=object)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>lead_time</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>timedelta64[ns]</div><div class='xr-var-preview xr-preview'>00:00:00 06:00:00 12:00:00</div><input id='attrs-6582a404-af4a-41e0-9a93-24f422bcc924' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-6582a404-af4a-41e0-9a93-24f422bcc924' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c08fd19c-137a-4e12-ba4c-b406aa2a4337' class='xr-var-data-in' type='checkbox'><label for='data-c08fd19c-137a-4e12-ba4c-b406aa2a4337' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>time since forecast_reference_time</dd><dt><span>standard_name :</span></dt><dd>forecast_period</dd></dl></div><div class='xr-var-data'><pre>array([             0, 21600000000000, 43200000000000], dtype=&#x27;timedelta64[ns]&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-4deeea0d-5660-4355-b043-1b8f2d670f76' class='xr-section-summary-in' type='checkbox'  checked><label for='section-4deeea0d-5660-4355-b043-1b8f2d670f76' class='xr-section-summary' >Data variables: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>rmse.geopotential</span></div><div class='xr-var-dims'>(region, lead_time, level)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>82.88 42.05 34.08 ... 27.88 29.48</div><input id='attrs-b2fc9db4-eb83-4252-ab6f-5df0838aaf01' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-b2fc9db4-eb83-4252-ab6f-5df0838aaf01' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-175aea63-e03c-4eaf-997e-aae52c3b1db9' class='xr-var-data-in' type='checkbox'><label for='data-175aea63-e03c-4eaf-997e-aae52c3b1db9' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[[ 82.88179454,  42.05411148,  34.08363907,  32.69531512,\n", "          32.18438109,  31.84820341,  29.40005908,  25.74533367,\n", "          23.14407541,  21.76128685,          nan,  22.46311481,\n", "          23.79077902],\n", "        [ 82.39673471,  40.16795019,  33.26927757,  30.85833562,\n", "          29.95194844,  29.41594468,  27.07237909,  24.28428697,\n", "          23.00415821,  23.18149938,  24.64048829,  25.40189446,\n", "          26.79994295],\n", "        [106.05772269,  59.87314634,  50.43837683,  46.39651495,\n", "          44.69507454,  43.44252074,  39.13714343,  35.26727277,\n", "          32.46972079,  30.56589697,  31.28850306,  32.54692715,\n", "          34.40918098]],\n", "\n", "       [[ 48.58571243,  25.42920788,  21.93517707,  22.11591787,\n", "          23.41418045,  24.66979055,  24.77383453,  23.94598813,\n", "          21.75008885,  21.0113857 ,          nan,  23.32949551,\n", "          24.75071867],\n", "        [ 69.03773152,  30.78616662,  20.65021949,  21.26255639,\n", "          22.58541006,  24.90734892,  25.4604965 ,  24.10547135,\n", "          22.84694374,  21.77207666,  22.11907865,  23.18333248,\n", "          24.81809649],\n", "        [ 92.48765245,  37.31107981,  31.46168504,  32.70859295,\n", "          32.87529904,  37.52809712,  37.62835753,  35.00904679,\n", "          32.10330812,  29.67939676,  34.83335325,  40.80563875,\n", "          47.22614107]],\n", "\n", "       [[ 80.99808308,  34.37548086,  23.43428364,  22.42821185,\n", "          22.12645978,  21.82512212,  18.87415539,  16.87845311,\n", "          17.38073301,  16.20739486,          nan,  11.54155125,\n", "          11.37984172],\n", "        [ 79.67682898,  35.67235462,  21.2133855 ,  21.47461637,\n", "          22.50897816,  23.55418534,  24.57315426,  21.50925651,\n", "          19.92116806,  18.52246874,  17.35817969,  17.52029348,\n", "          17.84691886],\n", "        [108.8669553 ,  50.26639712,  35.24689297,  34.74874688,\n", "          38.56624263,  40.81124431,  40.6103475 ,  40.25335106,\n", "          37.57226262,  33.26443399,  28.87632958,  27.8806141 ,\n", "          29.48443917]]])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>mae.geopotential</span></div><div class='xr-var-dims'>(region, lead_time, level)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>74.44 34.2 27.47 ... 20.62 22.35</div><input id='attrs-0a0678e6-c65b-4dee-b9b9-a1979943b310' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0a0678e6-c65b-4dee-b9b9-a1979943b310' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-3621b97b-6048-4aaf-b5cc-5d051564e8be' class='xr-var-data-in' type='checkbox'><label for='data-3621b97b-6048-4aaf-b5cc-5d051564e8be' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[[ 74.44321544,  34.20359871,  27.46774603,  26.55230557,\n", "          25.91214441,  25.53226927,  23.33220989,  20.32079402,\n", "          18.20573345,  17.10651598,          nan,  17.08799279,\n", "          17.97043168],\n", "        [ 72.01200946,  31.75349516,  26.275971  ,  24.5255533 ,\n", "          23.69634717,  23.30328157,  21.30932794,  18.95635278,\n", "          17.77887416,  17.6496448 ,  18.11632517,  18.22840991,\n", "          18.97286741],\n", "        [ 93.97262956,  47.00870894,  39.30988868,  36.79967105,\n", "          35.30514958,  34.22869829,  30.79997077,  27.48817155,\n", "          25.1242927 ,  23.4461824 ,  23.53727313,  24.24663785,\n", "          25.33028358]],\n", "\n", "       [[ 40.9987461 ,  19.96234699,  17.59880706,  17.78895907,\n", "          18.96307431,  20.01239657,  20.00565825,  19.49668532,\n", "          17.9246575 ,  16.85359572,          nan,  18.09414112,\n", "          18.76514199],\n", "        [ 64.80321674,  25.93896325,  16.74472313,  17.4755407 ,\n", "          17.98296476,  19.72243833,  21.21174384,  20.14692926,\n", "          19.03995476,  18.23805054,  17.63827296,  18.41781951,\n", "          19.43510519],\n", "        [ 84.40469355,  29.91643439,  25.63462794,  25.42785112,\n", "          24.61266902,  27.37595536,  28.10385088,  27.02460369,\n", "          25.52977007,  24.03417611,  26.78943491,  30.79705622,\n", "          34.96624549]],\n", "\n", "       [[ 74.23897408,  26.44480331,  18.94140921,  17.78433128,\n", "          17.54469819,  16.9930371 ,  15.13970013,  13.35784822,\n", "          13.36021111,  12.42635003,          nan,   9.49305372,\n", "           9.10373198],\n", "        [ 73.84896966,  28.94483848,  17.36096069,  17.4942765 ,\n", "          18.41180698,  19.47494277,  19.97141599,  17.35896903,\n", "          15.71324164,  14.5259948 ,  13.04386825,  13.25351224,\n", "          13.565832  ],\n", "        [102.67293565,  42.67217461,  28.05397071,  27.08503617,\n", "          28.87842063,  29.92294033,  29.39007658,  28.47592393,\n", "          26.68279033,  23.25066088,  21.06774514,  20.62012923,\n", "          22.34631725]]])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>rmse.2m_temperature</span></div><div class='xr-var-dims'>(region, lead_time)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.6832 0.6496 ... 0.5633 0.6497</div><input id='attrs-c6571eae-bf7e-4308-930f-ec238686b791' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c6571eae-bf7e-4308-930f-ec238686b791' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-919a2813-b2ea-48d5-9e37-3597cb0779d6' class='xr-var-data-in' type='checkbox'><label for='data-919a2813-b2ea-48d5-9e37-3597cb0779d6' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[0.68317926, 0.64959164, 0.73117071],\n", "       [0.84058149, 0.63523978, 0.89264501],\n", "       [0.53729653, 0.56330103, 0.64966404]])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>mae.2m_temperature</span></div><div class='xr-var-dims'>(region, lead_time)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.3887 0.3738 ... 0.4251 0.4813</div><input id='attrs-32e77ea7-c8db-4e95-b6cb-6baf2fecd17e' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-32e77ea7-c8db-4e95-b6cb-6baf2fecd17e' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-f2767e42-f90e-4a5e-bf9b-d6718c212c65' class='xr-var-data-in' type='checkbox'><label for='data-f2767e42-f90e-4a5e-bf9b-d6718c212c65' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[0.38872794, 0.37382877, 0.40253479],\n", "       [0.6038081 , 0.46058367, 0.59761541],\n", "       [0.42473947, 0.42510172, 0.48133112]])</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-56131044-d52a-4a90-b14e-ea3aee1134f7' class='xr-section-summary-in' type='checkbox'  ><label for='section-56131044-d52a-4a90-b14e-ea3aee1134f7' class='xr-section-summary' >Indexes: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>level</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-284c48d5-ceb6-42ad-a87f-71272f29af06' class='xr-index-data-in' type='checkbox'/><label for='index-284c48d5-ceb6-42ad-a87f-71272f29af06' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([50, 100, 150, 200, 250, 300, 400, 500, 600, 700, 850, 925, 1000], dtype=&#x27;int32&#x27;, name=&#x27;level&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>region</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-4251a970-3233-4d72-917f-88bae4d14d08' class='xr-index-data-in' type='checkbox'/><label for='index-4251a970-3233-4d72-917f-88bae4d14d08' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([&#x27;global&#x27;, &#x27;na&#x27;, &#x27;europe&#x27;], dtype=&#x27;object&#x27;, name=&#x27;region&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>lead_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-5e79442a-c6b7-49c6-a18d-24be6400cfdb' class='xr-index-data-in' type='checkbox'/><label for='index-5e79442a-c6b7-49c6-a18d-24be6400cfdb' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(TimedeltaIndex([&#x27;0 days 00:00:00&#x27;, &#x27;0 days 06:00:00&#x27;, &#x27;0 days 12:00:00&#x27;], dtype=&#x27;timedelta64[ns]&#x27;, name=&#x27;lead_time&#x27;, freq=None))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-805a593a-b6b0-43bc-80ee-8f296d74d7a6' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-805a593a-b6b0-43bc-80ee-8f296d74d7a6' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 2kB\n", "Dimensions:              (level: 13, region: 3, lead_time: 3)\n", "Coordinates:\n", "  * level                (level) int32 52B 50 100 150 200 ... 700 ************\n", "  * region               (region) object 24B 'global' 'na' 'europe'\n", "  * lead_time            (lead_time) timedelta64[ns] 24B 00:00:00 ... 12:00:00\n", "Data variables:\n", "    rmse.geopotential    (region, lead_time, level) float64 936B 82.88 ... 29.48\n", "    mae.geopotential     (region, lead_time, level) float64 936B 74.44 ... 22.35\n", "    rmse.2m_temperature  (region, lead_time) float64 72B 0.6832 ... 0.6497\n", "    mae.2m_temperature   (region, lead_time) float64 72B 0.3887 ... 0.4813"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["xr.open_dataset('./out.nc').compute()"]}, {"cell_type": "markdown", "id": "610cbc5a-0612-4e64-ab95-22441e171e08", "metadata": {}, "source": ["Voila! To see an example of a full pipeline, see [run_example_evaluation.py](https://github.com/google-research/weatherbenchX/tree/main/evaluation_scripts/run_example_evaluation.py)\n", "\n", "This was it for the simple example. For more advanced topics see the HOW TO guides."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}