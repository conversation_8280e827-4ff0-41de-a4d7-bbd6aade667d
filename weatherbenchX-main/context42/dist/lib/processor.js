import { mkdir, rename, unlink } from "node:fs/promises";
import { join } from "node:path";
import { sum } from "es-toolkit";
import PQueue from "p-queue";
import { cleanupRegistry, createFileCleanupHandler } from "./cleanup-registry.js";
import { generateStyleGuide } from "./generator.js";
const createStyleGuideProcessor = (options) => {
  const workers = Array.from({ length: options.concurrency }, (_, index) => ({
    id: index + 1,
    status: "idle"
  }));
  let total;
  let completed;
  let queuedTasks = [];
  let abortController;
  const updateWorker = (id, updates) => {
    const worker = workers.find((w) => w.id === id);
    Object.assign(worker, updates);
    options.onWorkerUpdate?.(worker);
  };
  const run = async ({ fileGroups, outputDir }) => {
    const createdStyleGuides = /* @__PURE__ */ new Map();
    const createdFiles = /* @__PURE__ */ new Set();
    const groups = Array.from(fileGroups.values()).flat();
    abortController = new AbortController();
    const unregisterCleanup = cleanupRegistry.register(createFileCleanupHandler(createdFiles));
    try {
      completed = 0;
      total = sum(groups.map((g) => g.files.length));
      await mkdir(outputDir, { recursive: true });
      const tasks = {};
      const edges = [];
      const isChildOf = (child, parent) => {
        return child.startsWith(`${parent}/`) && child !== parent;
      };
      const getTaskId = (group) => {
        return `${group.language}:${group.directory}`;
      };
      for (const group of groups) {
        const taskId = getTaskId(group);
        for (const potentialParent of groups) {
          if (group.language === potentialParent.language && isChildOf(group.directory, potentialParent.directory)) {
            const isDirectParent = !groups.some(
              (g) => g.language === group.language && g.directory !== group.directory && g.directory !== potentialParent.directory && isChildOf(group.directory, g.directory) && isChildOf(g.directory, potentialParent.directory)
            );
            if (isDirectParent) {
              edges.push([taskId, getTaskId(potentialParent)]);
            }
          }
        }
      }
      const workerQueue = [];
      const workerMutex = { locked: false };
      const getWorker = async () => {
        while (true) {
          if (!workerMutex.locked && workerQueue.length > 0) {
            workerMutex.locked = true;
            const workerId = workerQueue.shift();
            workerMutex.locked = false;
            if (workerId !== void 0) return workerId;
          }
          await new Promise((resolve) => setTimeout(resolve, 10));
        }
      };
      const releaseWorker = (workerId) => {
        workerQueue.push(workerId);
      };
      workers.forEach((w) => workerQueue.push(w.id));
      for (const group of groups) {
        const taskId = getTaskId(group);
        tasks[taskId] = async () => {
          const assignedWorkerId = await getWorker();
          try {
            updateWorker(assignedWorkerId, {
              status: "working",
              language: group.language,
              directory: group.directory
            });
            const childGuides = options.database.getChildStyleGuides(group.directory, group.language);
            const childStyleGuides = childGuides.length > 0 ? Object.fromEntries(childGuides.map((g) => [g.directory, g.content])) : void 0;
            await generateStyleGuide({
              model: options.model,
              files: [...group.files],
              language: group.language,
              childStyleGuides,
              signal: abortController?.signal,
              onProgress: (message) => {
                updateWorker(assignedWorkerId, {
                  progress: message
                });
              }
            });
            const langName = group.language.toUpperCase();
            options.database.saveStyleGuide(group.language, `# ${langName} Style Guide`, group.directory);
            const styleFileName = `style.${group.language}.md`;
            const styleFilePath = join(group.directory, styleFileName);
            if (!createdStyleGuides.has(group.language)) {
              createdStyleGuides.set(group.language, []);
            }
            createdStyleGuides.get(group.language).push(styleFilePath);
            createdFiles.add(styleFilePath);
            updateWorker(assignedWorkerId, {
              status: "success",
              language: group.language,
              directory: group.directory
            });
          } catch (error) {
            updateWorker(assignedWorkerId, {
              status: "error",
              language: group.language,
              directory: group.directory,
              error: error instanceof Error ? error.message : "Unknown error"
            });
            throw error;
          } finally {
            releaseWorker(assignedWorkerId);
            updateWorker(assignedWorkerId, {
              status: "idle",
              language: void 0,
              directory: void 0,
              error: void 0,
              progress: void 0
            });
            completed = (completed ?? 0) + group.files.length;
            options.onProgress?.(completed);
          }
        };
      }
      const taskMetadata = /* @__PURE__ */ new Map();
      for (const group of groups) {
        const taskId = getTaskId(group);
        taskMetadata.set(taskId, {
          language: group.language,
          directory: group.directory
        });
      }
      await runTasks(tasks, edges, options.concurrency, (queueState) => {
        const readyTasks = queueState.ready.map((id) => {
          const metadata = taskMetadata.get(id);
          return {
            id,
            language: metadata.language,
            directory: metadata.directory,
            status: "ready"
          };
        });
        const waitingTasks = Array.from(queueState.waiting.entries()).map(([id, deps]) => {
          const metadata = taskMetadata.get(id);
          return {
            id,
            language: metadata.language,
            directory: metadata.directory,
            status: "waiting",
            pendingDeps: deps
          };
        });
        queuedTasks = [...readyTasks, ...waitingTasks];
        options.onQueueUpdate?.(queuedTasks);
      });
      await Promise.all(
        Array.from(createdStyleGuides.entries()).map(async ([language, sourcePaths]) => {
          const sourcePath = sourcePaths[sourcePaths.length - 1];
          if (sourcePath) {
            try {
              await rename(sourcePath, join(outputDir, `${language}.md`));
              createdFiles.delete(sourcePath);
            } catch (err) {
              console.error(`Failed to move ${language} style guide:`, err instanceof Error ? err.message : String(err));
            }
          }
        })
      );
      queuedTasks = [];
      options.onQueueUpdate?.(queuedTasks);
      const results = /* @__PURE__ */ new Map();
      for (const [language] of createdStyleGuides) {
        results.set(language, `${language}.md`);
      }
      return results;
    } finally {
      abortController = void 0;
      unregisterCleanup();
      if (createdFiles.size > 0) {
        await Promise.all(
          Array.from(createdFiles).map(async (filePath) => {
            try {
              await unlink(filePath);
            } catch (_err) {
            }
          })
        );
      }
    }
  };
  const reset = () => {
    abortController?.abort();
    abortController = void 0;
    total = void 0;
    completed = void 0;
    queuedTasks = [];
    for (const worker of workers) {
      worker.status = "idle";
      worker.directory = void 0;
      worker.language = void 0;
      worker.error = void 0;
      worker.progress = void 0;
      options.onWorkerUpdate?.(worker);
    }
  };
  return {
    run,
    reset,
    workers,
    completed: completed ?? 0,
    total: total ?? 0,
    queuedTasks
  };
};
async function runTasks(tasks, edges, concurrency = 4, onQueueUpdate) {
  const inDeg = /* @__PURE__ */ new Map();
  const outAdj = /* @__PURE__ */ new Map();
  for (const id in tasks) {
    inDeg.set(id, 0);
    outAdj.set(id, []);
  }
  for (const [child, parent] of edges) {
    inDeg.set(parent, (inDeg.get(parent) ?? 0) + 1);
    outAdj.get(child).push(parent);
  }
  const q = new PQueue({ concurrency });
  const readyQueue = [];
  const completedTasks = /* @__PURE__ */ new Set();
  const updateQueue = () => {
    if (onQueueUpdate) {
      const waiting = /* @__PURE__ */ new Map();
      for (const [id, deps] of inDeg) {
        if (deps > 0 && !completedTasks.has(id)) {
          waiting.set(id, deps);
        }
      }
      onQueueUpdate({
        ready: [...readyQueue],
        waiting
      });
    }
  };
  const enqueue = (id) => {
    readyQueue.push(id);
    updateQueue();
    q.add(async () => {
      const index = readyQueue.indexOf(id);
      if (index > -1) {
        readyQueue.splice(index, 1);
        updateQueue();
      }
      try {
        await tasks[id]();
      } catch (_error) {
      }
      completedTasks.add(id);
      for (const nxt of outAdj.get(id)) {
        inDeg.set(nxt, inDeg.get(nxt) - 1);
        if (inDeg.get(nxt) === 0) enqueue(nxt);
      }
      updateQueue();
    });
  };
  [...inDeg.entries()].filter(([, c]) => c === 0).forEach(([id]) => enqueue(id));
  await q.onIdle();
}
export {
  createStyleGuideProcessor
};
//# sourceMappingURL=processor.js.map
