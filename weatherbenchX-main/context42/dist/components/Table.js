import { Box, Text } from "ink";
import { sha1 } from "object-hash";
import { useCallback, useMemo } from "react";
import { jsx, jsxs } from "react/jsx-runtime";
const Table = (props) => {
  const getDataKeys = useCallback(() => {
    const keys = /* @__PURE__ */ new Set();
    for (const data2 of props.data) {
      for (const key in data2) {
        keys.add(key);
      }
    }
    return Array.from(keys);
  }, [props.data]);
  const getConfig = useMemo(() => {
    return {
      data: props.data,
      columns: props.columns || getDataKeys(),
      padding: props.padding || 1,
      columnWidths: props.columnWidths,
      header: props.header || Header,
      cell: props.cell || Cell,
      skeleton: props.skeleton || Skeleton
    };
  }, [
    props.data,
    props.columns,
    props.padding,
    props.columnWidths,
    props.header,
    props.cell,
    props.skeleton,
    getDataKeys
  ]);
  const getColumns = useMemo(() => {
    const { columns: columns2, padding, columnWidths: widths } = getConfig;
    const widthsMap = columns2.map((key) => {
      if (widths && widths[key] !== void 0) {
        return {
          column: key,
          width: widths[key],
          key: String(key)
        };
      }
      const header2 = String(key).length;
      const data2 = props.data.map((data3) => {
        const value = data3[key];
        if (value === void 0 || value === null) return 0;
        return String(value).length;
      });
      const width = Math.max(...data2, header2) + padding * 2;
      return {
        column: key,
        width,
        key: String(key)
      };
    });
    return widthsMap;
  }, [getConfig, props.data]);
  const getHeadings = useMemo(() => {
    const { columns: columns2 } = getConfig;
    const headings2 = columns2.reduce((acc, column) => ({ ...acc, [column]: column }), {});
    return headings2;
  }, [getConfig]);
  const header = useMemo(
    () => row({
      cell: getConfig.skeleton,
      padding: getConfig.padding,
      skeleton: {
        component: getConfig.skeleton,
        // chars
        line: "\u2500",
        left: "\u250C",
        right: "\u2510",
        cross: "\u252C"
      }
    }),
    [getConfig]
  );
  const heading = useMemo(
    () => row({
      cell: getConfig.header,
      padding: getConfig.padding,
      skeleton: {
        component: getConfig.skeleton,
        // chars
        line: " ",
        left: "\u2502",
        right: "\u2502",
        cross: "\u2502"
      }
    }),
    [getConfig]
  );
  const separator = useMemo(
    () => row({
      cell: getConfig.skeleton,
      padding: getConfig.padding,
      skeleton: {
        component: getConfig.skeleton,
        // chars
        line: "\u2500",
        left: "\u251C",
        right: "\u2524",
        cross: "\u253C"
      }
    }),
    [getConfig]
  );
  const data = useMemo(
    () => row({
      cell: getConfig.cell,
      padding: getConfig.padding,
      skeleton: {
        component: getConfig.skeleton,
        // chars
        line: " ",
        left: "\u2502",
        right: "\u2502",
        cross: "\u2502"
      }
    }),
    [getConfig]
  );
  const footer = useMemo(
    () => row({
      cell: getConfig.skeleton,
      padding: getConfig.padding,
      skeleton: {
        component: getConfig.skeleton,
        // chars
        line: "\u2500",
        left: "\u2514",
        right: "\u2518",
        cross: "\u2534"
      }
    }),
    [getConfig]
  );
  const columns = getColumns;
  const headings = getHeadings;
  return /* @__PURE__ */ jsxs(Box, { flexDirection: "column", children: [
    header({ key: "header", columns, data: {} }),
    heading({ key: "heading", columns, data: headings }),
    props.data.map((row2, index) => {
      const key = `row-${sha1(row2)}-${index}`;
      return /* @__PURE__ */ jsxs(Box, { flexDirection: "column", children: [
        separator({ key: `separator-${key}`, columns, data: {} }),
        data({ key: `data-${key}`, columns, data: row2 })
      ] }, key);
    }),
    footer({ key: "footer", columns, data: {} })
  ] });
};
var Table_default = Table;
const row = (config) => {
  const skeleton = config.skeleton;
  return (props) => /* @__PURE__ */ jsxs(Box, { flexDirection: "row", children: [
    /* @__PURE__ */ jsx(skeleton.component, { children: skeleton.left }),
    ...intersperse(
      (i) => {
        const key = `${props.key}-hseparator-${i}`;
        return /* @__PURE__ */ jsx(skeleton.component, { children: skeleton.cross }, key);
      },
      // Values.
      props.columns.map((column, colI) => {
        const value = props.data[column.column];
        if (value === void 0 || value === null) {
          const key2 = `${props.key}-empty-${column.key}`;
          return /* @__PURE__ */ jsx(config.cell, { column: colI, children: skeleton.line.repeat(column.width) }, key2);
        }
        const key = `${props.key}-cell-${column.key}`;
        const originalText = String(value);
        const truncatedText = truncateText(originalText, column.width, config.padding);
        const ml = config.padding;
        const mr = column.width - truncatedText.length - config.padding;
        return (
          /* prettier-ignore */
          /* @__PURE__ */ jsx(config.cell, { column: colI, children: `${skeleton.line.repeat(ml)}${truncatedText}${skeleton.line.repeat(mr)}` }, key)
        );
      })
    ),
    /* @__PURE__ */ jsx(skeleton.component, { children: skeleton.right })
  ] });
};
const Header = (props) => /* @__PURE__ */ jsx(Text, { bold: true, color: "blue", children: props.children });
const Cell = (props) => /* @__PURE__ */ jsx(Text, { children: props.children });
const Skeleton = (props) => /* @__PURE__ */ jsx(Text, { bold: true, children: props.children });
const truncateText = (text, maxWidth, padding) => {
  const availableWidth = maxWidth - padding * 2;
  if (text.length <= availableWidth) return text;
  if (availableWidth <= 3) return "...".slice(0, availableWidth);
  return `${text.slice(0, availableWidth - 3)}...`;
};
const intersperse = (intersperser, elements) => (
  // Intersperse by reducing from left.
  elements.reduce(
    (acc, element, index) => {
      if (acc.length === 0) return [element];
      acc.push(intersperser(index), element);
      return acc;
    },
    []
  )
);
export {
  Cell,
  Header,
  Skeleton,
  Table_default as default
};
//# sourceMappingURL=Table.js.map
