{"version": 3, "file": "reconciler.js", "sourceRoot": "", "sources": ["../src/reconciler.ts"], "names": [], "mappings": "AAAA,OAAO,OAAO,MAAM,cAAc,CAAC;AACnC,OAAO,gBAAqC,MAAM,kBAAkB,CAAC;AACrE,OAAO,EACN,oBAAoB,EACpB,eAAe,GACf,MAAM,+BAA+B,CAAC;AACvC,OAAO,IAA6B,MAAM,aAAa,CAAC;AACxD,OAAO,EAAC,aAAa,EAAC,MAAM,OAAO,CAAC;AACpC,OAAO,EACN,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,QAAQ,EACR,gBAAgB,EAChB,UAAU,EACV,YAAY,GAKZ,MAAM,UAAU,CAAC;AAClB,OAAO,WAA0B,MAAM,aAAa,CAAC;AAGrD,gEAAgE;AAChE,gDAAgD;AAChD,qDAAqD;AACrD,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM,EAAE,CAAC;IACnC,IAAI,CAAC;QACJ,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;IAC/B,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACrB,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAsB,EAAE,CAAC;YAC3C,OAAO,CAAC,IAAI,CACX;;;;;;;KAOC,CAAC,IAAI,EAAE,GAAG,IAAI,CACf,CAAC;QACH,CAAC;aAAM,CAAC;YACP,+DAA+D;YAC/D,MAAM,KAAK,CAAC;QACb,CAAC;IACF,CAAC;AACF,CAAC;AAID,MAAM,IAAI,GAAG,CAAC,MAAiB,EAAE,KAAgB,EAAyB,EAAE;IAC3E,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;QACtB,OAAO;IACR,CAAC;IAED,IAAI,CAAC,MAAM,EAAE,CAAC;QACb,OAAO,KAAK,CAAC;IACd,CAAC;IAED,MAAM,OAAO,GAAc,EAAE,CAAC;IAC9B,IAAI,SAAS,GAAG,KAAK,CAAC;IAEtB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;QACvC,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAE5D,IAAI,SAAS,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;YACzB,SAAS,GAAG,IAAI,CAAC;QAClB,CAAC;IACF,CAAC;IAED,IAAI,KAAK,EAAE,CAAC;QACX,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACtC,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC1B,SAAS,GAAG,IAAI,CAAC;YAClB,CAAC;QACF,CAAC;IACF,CAAC;IAED,OAAO,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;AACxC,CAAC,CAAC;AAEF,MAAM,eAAe,GAAG,CAAC,IAAe,EAAQ,EAAE;IACjD,IAAI,EAAE,gBAAgB,EAAE,CAAC;IACzB,IAAI,EAAE,aAAa,EAAE,CAAC;AACvB,CAAC,CAAC;AAQF,IAAI,qBAAqB,GAAG,eAAe,CAAC;AAE5C,IAAI,eAAuC,CAAC;AAE5C,eAAe,gBAAgB,CAe7B;IACD,kBAAkB,EAAE,GAAG,EAAE,CAAC,CAAC;QAC1B,YAAY,EAAE,KAAK;KACnB,CAAC;IACF,gBAAgB,EAAE,GAAG,EAAE,CAAC,IAAI;IAC5B,kBAAkB,EAAE,GAAG,EAAE,CAAC,IAAI;IAC9B,cAAc,EAAE,GAAG,EAAE,CAAC,KAAK;IAC3B,gBAAgB,CAAC,QAAQ;QACxB,IAAI,OAAO,QAAQ,CAAC,eAAe,KAAK,UAAU,EAAE,CAAC;YACpD,QAAQ,CAAC,eAAe,EAAE,CAAC;QAC5B,CAAC;QAED,oFAAoF;QACpF,0EAA0E;QAC1E,uGAAuG;QACvG,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC5B,QAAQ,CAAC,aAAa,GAAG,KAAK,CAAC;YAC/B,IAAI,OAAO,QAAQ,CAAC,iBAAiB,KAAK,UAAU,EAAE,CAAC;gBACtD,QAAQ,CAAC,iBAAiB,EAAE,CAAC;YAC9B,CAAC;YAED,OAAO;QACR,CAAC;QAED,IAAI,OAAO,QAAQ,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YAC7C,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACrB,CAAC;IACF,CAAC;IACD,mBAAmB,CAAC,iBAAiB,EAAE,IAAI;QAC1C,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,YAAY,CAAC;QAC5D,MAAM,YAAY,GAAG,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,kBAAkB,CAAC;QAExE,IAAI,oBAAoB,KAAK,YAAY,EAAE,CAAC;YAC3C,OAAO,iBAAiB,CAAC;QAC1B,CAAC;QAED,OAAO,EAAC,YAAY,EAAC,CAAC;IACvB,CAAC;IACD,oBAAoB,EAAE,GAAG,EAAE,CAAC,KAAK;IACjC,cAAc,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW;QAC3D,IAAI,WAAW,CAAC,YAAY,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,IAAI,GACT,YAAY,KAAK,UAAU,IAAI,WAAW,CAAC,YAAY;YACtD,CAAC,CAAC,kBAAkB;YACpB,CAAC,CAAC,YAAY,CAAC;QAEjB,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;QAE9B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrD,IAAI,GAAG,KAAK,UAAU,EAAE,CAAC;gBACxB,SAAS;YACV,CAAC;YAED,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;gBACrB,QAAQ,CAAC,IAAI,EAAE,KAAe,CAAC,CAAC;gBAEhC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACnB,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAe,CAAC,CAAC;gBAC7C,CAAC;gBAED,SAAS;YACV,CAAC;YAED,IAAI,GAAG,KAAK,oBAAoB,EAAE,CAAC;gBAClC,IAAI,CAAC,kBAAkB,GAAG,KAA0B,CAAC;gBACrD,SAAS;YACV,CAAC;YAED,IAAI,GAAG,KAAK,iBAAiB,EAAE,CAAC;gBAC/B,eAAe,GAAG,QAAQ,CAAC;gBAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;gBAE9B,8DAA8D;gBAC9D,uBAAuB;gBACvB,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;gBAC3B,SAAS;YACV,CAAC;YAED,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,KAAyB,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IACD,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,WAAW;QAC1C,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CACd,gBAAgB,IAAI,4CAA4C,CAChE,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IACD,gBAAgB,KAAI,CAAC;IACrB,gBAAgB,CAAC,IAAI;QACpB,gBAAgB,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC5B,CAAC;IACD,kBAAkB,CAAC,IAAI,EAAE,IAAI;QAC5B,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9B,CAAC;IACD,iBAAiB,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ;IACvC,YAAY,CAAC,IAAI;QAChB,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC9C,CAAC;IACD,cAAc,CAAC,IAAI;QAClB,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC9C,CAAC;IACD,kBAAkB,EAAE,eAAe;IACnC,WAAW,EAAE,eAAe;IAC5B,YAAY,EAAE,gBAAgB;IAC9B,uBAAuB;QACtB,OAAO,KAAK,CAAC;IACd,CAAC;IACD,iBAAiB,EAAE,IAAI;IACvB,gBAAgB,EAAE,IAAI;IACtB,mBAAmB,EAAE,KAAK;IAC1B,iBAAiB,EAAE,KAAK;IACxB,eAAe,EAAE,UAAU;IAC3B,aAAa,EAAE,YAAY;IAC3B,SAAS,EAAE,CAAC,CAAC;IACb,wBAAwB,KAAI,CAAC;IAC7B,uBAAuB,KAAI,CAAC;IAC5B,qBAAqB,KAAI,CAAC;IAC1B,mBAAmB,EAAE,GAAG,EAAE,CAAC,IAAI;IAC/B,kBAAkB,KAAI,CAAC;IACvB,oBAAoB,EAAE,GAAG,EAAE,CAAC,IAAI;IAChC,sBAAsB,EAAE,eAAe;IACvC,uBAAuB,EAAE,gBAAgB;IACzC,wBAAwB,CAAC,IAAI,EAAE,UAAU;QACxC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAClC,eAAe,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IACD,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;QAC3C,IAAI,eAAe,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC7C,eAAe,CAAC,aAAa,GAAG,IAAI,CAAC;QACtC,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAEvC,MAAM,KAAK,GAAG,IAAI,CACjB,QAAQ,CAAC,OAAO,CAAW,EAC3B,QAAQ,CAAC,OAAO,CAAW,CAC3B,CAAC;QAEF,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;YACtB,OAAO;QACR,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACX,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClD,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;oBACrB,QAAQ,CAAC,IAAI,EAAE,KAAe,CAAC,CAAC;oBAChC,SAAS;gBACV,CAAC;gBAED,IAAI,GAAG,KAAK,oBAAoB,EAAE,CAAC;oBAClC,IAAI,CAAC,kBAAkB,GAAG,KAA0B,CAAC;oBACrD,SAAS;gBACV,CAAC;gBAED,IAAI,GAAG,KAAK,iBAAiB,EAAE,CAAC;oBAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC5B,SAAS;gBACV,CAAC;gBAED,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,KAAyB,CAAC,CAAC;YACpD,CAAC;QACF,CAAC;QAED,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QACnC,CAAC;IACF,CAAC;IACD,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO;QACvC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACjC,CAAC;IACD,WAAW,CAAC,IAAI,EAAE,UAAU;QAC3B,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAClC,eAAe,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IACD,wBAAwB,CAAC,WAAmB;QAC3C,qBAAqB,GAAG,WAAW,CAAC;IACrC,CAAC;IACD,wBAAwB,EAAE,GAAG,EAAE,CAAC,qBAAqB;IACrD,qBAAqB;QACpB,IAAI,qBAAqB,KAAK,eAAe,EAAE,CAAC;YAC/C,OAAO,qBAAqB,CAAC;QAC9B,CAAC;QAED,OAAO,oBAAoB,CAAC;IAC7B,CAAC;IACD,gBAAgB;QACf,OAAO,KAAK,CAAC;IACd,CAAC;IACD,gEAAgE;IAChE,oBAAoB,EAAE,SAAS;IAC/B,gEAAgE;IAChE,qBAAqB,EAAE,aAAa,CACnC,IAAI,CACgC;IACrC,iBAAiB,KAAI,CAAC;IACtB,wBAAwB,KAAI,CAAC;IAC7B,4BAA4B;QAC3B,OAAO,KAAK,CAAC;IACd,CAAC;IACD,mBAAmB,KAAI,CAAC;IACxB,gBAAgB;QACf,OAAO,IAAI,CAAC;IACb,CAAC;IACD,qBAAqB;QACpB,OAAO,CAAC,GAAG,CAAC;IACb,CAAC;IACD,eAAe;QACd,OAAO,IAAI,CAAC;IACb,CAAC;IACD,qBAAqB,KAAI,CAAC;IAC1B,eAAe,KAAI,CAAC;IACpB,sBAAsB;QACrB,OAAO,IAAI,CAAC;IACb,CAAC;CACD,CAAC,CAAC"}