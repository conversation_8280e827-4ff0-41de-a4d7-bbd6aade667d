{"version": 3, "sources": ["../../src/components/WorkerStatus.tsx"], "sourcesContent": ["import path from \"node:path\"\nimport { Box, Text } from \"ink\"\nimport type React from \"react\"\nimport { useMemo } from \"react\"\nimport type { QueuedTask, Worker } from \"../lib/types.js\"\nimport Table from \"./Table.js\"\n\nexport type WorkersStatusProps = {\n  inputDir: string\n  readonly workers: readonly Worker[]\n  readonly queuedTasks?: readonly QueuedTask[]\n}\n\nconst globpath = (inputDir: string, directory: string, language: string) =>\n  path.join(path.relative(inputDir, directory) || \"./\", `*.${language}`)\n\nexport const WorkersStatus: React.FC<WorkersStatusProps> = ({ workers, inputDir, queuedTasks = [] }) => {\n  const waitingTasks = queuedTasks.filter(t => t.status === \"waiting\").slice(0, workers.length)\n  const workersViewModel = useMemo(\n    () =>\n      workers\n        .map(agent => ({\n          agent: agent.id,\n          directory: agent.directory && agent.language ? globpath(inputDir, agent.directory, agent.language) : \"\",\n          status:\n            agent.status === \"idle\"\n              ? \"Waiting...\"\n              : agent.status === \"working\"\n                ? agent.progress || \"Working...\"\n                : agent.status === \"success\"\n                  ? \"Success\"\n                  : agent.error,\n        }))\n        .filter(agent => agent.directory),\n    [workers, inputDir],\n  )\n  const columnWidths = useMemo(() => ({ agent: 8, directory: 22, status: 90 }), [])\n\n  if (workersViewModel.length === 0) return null\n\n  return (\n    <Box flexDirection=\"column\" marginTop={1}>\n      <Table data={workersViewModel} columnWidths={columnWidths} />\n\n      {/* Waiting tasks */}\n      {waitingTasks.length !== 0 && (\n        <Box flexDirection=\"column\" marginBottom={1}>\n          <Text dimColor>Queue: {waitingTasks.length}</Text>\n          {waitingTasks.map((task, index) => (\n            <Box key={`waiting-${task.id}-${index}`} marginLeft={2}>\n              <Text dimColor>\n                {globpath(inputDir, task.directory, task.language)}\n                {task.pendingDeps && ` (${task.pendingDeps} dep${task.pendingDeps > 1 ? \"s\" : \"\"})`}\n              </Text>\n            </Box>\n          ))}\n        </Box>\n      )}\n    </Box>\n  )\n}\n"], "mappings": "AAAA,OAAO,UAAU;AACjB,SAAS,KAAK,YAAY;AAE1B,SAAS,eAAe;AAExB,OAAO,WAAW;AAqCZ,cAKI,YALJ;AA7BN,MAAM,WAAW,CAAC,UAAkB,WAAmB,aACrD,KAAK,KAAK,KAAK,SAAS,UAAU,SAAS,KAAK,MAAM,KAAK,QAAQ,EAAE;AAEhE,MAAM,gBAA8C,CAAC,EAAE,SAAS,UAAU,cAAc,CAAC,EAAE,MAAM;AACtG,QAAM,eAAe,YAAY,OAAO,OAAK,EAAE,WAAW,SAAS,EAAE,MAAM,GAAG,QAAQ,MAAM;AAC5F,QAAM,mBAAmB;AAAA,IACvB,MACE,QACG,IAAI,YAAU;AAAA,MACb,OAAO,MAAM;AAAA,MACb,WAAW,MAAM,aAAa,MAAM,WAAW,SAAS,UAAU,MAAM,WAAW,MAAM,QAAQ,IAAI;AAAA,MACrG,QACE,MAAM,WAAW,SACb,eACA,MAAM,WAAW,YACf,MAAM,YAAY,eAClB,MAAM,WAAW,YACf,YACA,MAAM;AAAA,IAClB,EAAE,EACD,OAAO,WAAS,MAAM,SAAS;AAAA,IACpC,CAAC,SAAS,QAAQ;AAAA,EACpB;AACA,QAAM,eAAe,QAAQ,OAAO,EAAE,OAAO,GAAG,WAAW,IAAI,QAAQ,GAAG,IAAI,CAAC,CAAC;AAEhF,MAAI,iBAAiB,WAAW,EAAG,QAAO;AAE1C,SACE,qBAAC,OAAI,eAAc,UAAS,WAAW,GACrC;AAAA,wBAAC,SAAM,MAAM,kBAAkB,cAA4B;AAAA,IAG1D,aAAa,WAAW,KACvB,qBAAC,OAAI,eAAc,UAAS,cAAc,GACxC;AAAA,2BAAC,QAAK,UAAQ,MAAC;AAAA;AAAA,QAAQ,aAAa;AAAA,SAAO;AAAA,MAC1C,aAAa,IAAI,CAAC,MAAM,UACvB,oBAAC,OAAwC,YAAY,GACnD,+BAAC,QAAK,UAAQ,MACX;AAAA,iBAAS,UAAU,KAAK,WAAW,KAAK,QAAQ;AAAA,QAChD,KAAK,eAAe,KAAK,KAAK,WAAW,OAAO,KAAK,cAAc,IAAI,MAAM,EAAE;AAAA,SAClF,KAJQ,WAAW,KAAK,EAAE,IAAI,KAAK,EAKrC,CACD;AAAA,OACH;AAAA,KAEJ;AAEJ;", "names": []}