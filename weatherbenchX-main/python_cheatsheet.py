#!/usr/bin/env python3
"""
Python Basic to Advanced Cheatsheet for Gridmatic Interview
Comprehensive syntax reference with practical examples

Categories:
1. Basic Syntax & Data Types
2. Control Flow & Functions
3. Data Structures & Comprehensions
4. Object-Oriented Programming
5. Error Handling & Context Managers
6. Advanced Features (Decorators, Generators, etc.)
7. Standard Library Essentials
8. Scientific Computing (NumPy, Pandas, XArray)
9. Common Patterns & Best Practices
"""

import numpy as np
import pandas as pd
import xarray as xr
from typing import List, Dict, Optional, Union, Tuple, Any
from collections import defaultdict, Counter, namedtuple
from functools import wraps, reduce
from itertools import chain, combinations, groupby
import datetime
import json
import re

# =============================================================================
# 1. BASIC SYNTAX & DATA TYPES
# =============================================================================

# Variables and basic types
name = "Python"                    # str
age = 30                          # int
height = 5.9                      # float
is_active = True                  # bool
nothing = None                    # NoneType

# String operations
text = "Hello World"
print(f"Length: {len(text)}")                    # f-strings (Python 3.6+)
print(f"Upper: {text.upper()}")                  # Method chaining
print(f"Split: {text.split()}")                  # Returns list
print(f"Replace: {text.replace('World', 'Python')}")
print(f"Contains: {'Hello' in text}")            # Membership test

# String formatting options
name, score = "Alice", 95.5
print("Name: %s, Score: %.1f" % (name, score))   # Old style
print("Name: {}, Score: {:.1f}".format(name, score))  # .format()
print(f"Name: {name}, Score: {score:.1f}")       # f-strings (preferred)

# Multiple assignment
x, y, z = 1, 2, 3
a = b = c = 0                     # Same value
first, *middle, last = [1, 2, 3, 4, 5]  # Unpacking with *

# =============================================================================
# 2. CONTROL FLOW & FUNCTIONS
# =============================================================================

# Conditional statements
def check_grade(score):
    if score >= 90:
        return "A"
    elif score >= 80:
        return "B"
    elif score >= 70:
        return "C"
    else:
        return "F"

# Ternary operator
grade = "Pass" if score >= 60 else "Fail"

# Loops
# For loop with range
for i in range(5):                # 0, 1, 2, 3, 4
    print(i)

for i in range(2, 10, 2):         # 2, 4, 6, 8 (start, stop, step)
    print(i)

# For loop with enumerate
items = ['a', 'b', 'c']
for index, value in enumerate(items):
    print(f"{index}: {value}")

# For loop with zip
names = ['Alice', 'Bob', 'Charlie']
scores = [85, 92, 78]
for name, score in zip(names, scores):
    print(f"{name}: {score}")

# While loop
count = 0
while count < 5:
    print(count)
    count += 1

# Functions
def greet(name, greeting="Hello"):
    """Function with default parameter"""
    return f"{greeting}, {name}!"

def calculate_stats(*args, **kwargs):
    """Function with *args and **kwargs"""
    print(f"Args: {args}")
    print(f"Kwargs: {kwargs}")
    return sum(args) if args else 0

# Lambda functions
square = lambda x: x ** 2
add = lambda x, y: x + y
numbers = [1, 2, 3, 4, 5]
squared = list(map(square, numbers))              # [1, 4, 9, 16, 25]
evens = list(filter(lambda x: x % 2 == 0, numbers))  # [2, 4]

# =============================================================================
# 3. DATA STRUCTURES & COMPREHENSIONS
# =============================================================================

# Lists
fruits = ['apple', 'banana', 'cherry']
fruits.append('date')                             # Add to end
fruits.insert(1, 'blueberry')                    # Insert at index
fruits.remove('banana')                          # Remove first occurrence
popped = fruits.pop()                            # Remove and return last
fruits.extend(['elderberry', 'fig'])             # Add multiple items

# List slicing
numbers = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
print(numbers[2:5])                              # [2, 3, 4]
print(numbers[:3])                               # [0, 1, 2]
print(numbers[7:])                               # [7, 8, 9]
print(numbers[::2])                              # [0, 2, 4, 6, 8] (every 2nd)
print(numbers[::-1])                             # Reverse

# List comprehensions
squares = [x**2 for x in range(10)]              # [0, 1, 4, 9, 16, ...]
evens = [x for x in range(20) if x % 2 == 0]    # [0, 2, 4, 6, ...]
matrix = [[i*j for j in range(3)] for i in range(3)]  # Nested comprehension

# Dictionaries
person = {'name': 'Alice', 'age': 30, 'city': 'NYC'}
person['job'] = 'Engineer'                       # Add key
person.update({'salary': 75000, 'married': True})  # Add multiple
keys = person.keys()                             # dict_keys object
values = person.values()                         # dict_values object
items = person.items()                           # dict_items object

# Dictionary comprehensions
word_lengths = {word: len(word) for word in ['hello', 'world', 'python']}
filtered_dict = {k: v for k, v in person.items() if isinstance(v, str)}

# Sets
unique_numbers = {1, 2, 3, 4, 5}
unique_numbers.add(6)                            # Add element
unique_numbers.update([7, 8, 9])                 # Add multiple
set1 = {1, 2, 3, 4}
set2 = {3, 4, 5, 6}
intersection = set1 & set2                       # {3, 4}
union = set1 | set2                              # {1, 2, 3, 4, 5, 6}
difference = set1 - set2                         # {1, 2}

# Tuples (immutable)
coordinates = (10, 20)
x, y = coordinates                               # Unpacking
point = namedtuple('Point', ['x', 'y'])
p = point(10, 20)
print(f"Point: {p.x}, {p.y}")

# =============================================================================
# 4. OBJECT-ORIENTED PROGRAMMING
# =============================================================================

class Animal:
    """Base class demonstrating OOP concepts"""
    
    species_count = 0                            # Class variable
    
    def __init__(self, name, species):
        self.name = name                         # Instance variable
        self.species = species
        Animal.species_count += 1
    
    def __str__(self):                           # String representation
        return f"{self.name} ({self.species})"
    
    def __repr__(self):                          # Developer representation
        return f"Animal('{self.name}', '{self.species}')"
    
    def make_sound(self):                        # Method to be overridden
        return "Some generic animal sound"
    
    @classmethod
    def get_species_count(cls):                  # Class method
        return cls.species_count
    
    @staticmethod
    def is_mammal(species):                      # Static method
        mammals = ['dog', 'cat', 'elephant', 'human']
        return species.lower() in mammals

class Dog(Animal):
    """Inheritance example"""
    
    def __init__(self, name, breed):
        super().__init__(name, 'dog')            # Call parent constructor
        self.breed = breed
    
    def make_sound(self):                        # Method overriding
        return "Woof!"
    
    def fetch(self):                             # Dog-specific method
        return f"{self.name} is fetching!"

# Usage
dog = Dog("Buddy", "Golden Retriever")
print(dog)                                       # Uses __str__
print(repr(dog))                                 # Uses __repr__
print(dog.make_sound())                          # Overridden method
print(Animal.is_mammal('dog'))                   # Static method

# Property decorators
class Circle:
    def __init__(self, radius):
        self._radius = radius
    
    @property
    def radius(self):                            # Getter
        return self._radius
    
    @radius.setter
    def radius(self, value):                     # Setter
        if value < 0:
            raise ValueError("Radius cannot be negative")
        self._radius = value
    
    @property
    def area(self):                              # Computed property
        return 3.14159 * self._radius ** 2

# =============================================================================
# 5. ERROR HANDLING & CONTEXT MANAGERS
# =============================================================================

# Exception handling
def safe_divide(a, b):
    try:
        result = a / b
        return result
    except ZeroDivisionError:
        print("Cannot divide by zero!")
        return None
    except TypeError:
        print("Invalid types for division!")
        return None
    except Exception as e:                       # Catch all other exceptions
        print(f"Unexpected error: {e}")
        return None
    else:                                        # Runs if no exception
        print("Division successful!")
    finally:                                     # Always runs
        print("Division attempt completed")

# Custom exceptions
class ValidationError(Exception):
    """Custom exception for validation errors"""
    pass

def validate_age(age):
    if not isinstance(age, int):
        raise ValidationError("Age must be an integer")
    if age < 0:
        raise ValidationError("Age cannot be negative")
    if age > 150:
        raise ValidationError("Age seems unrealistic")
    return True

# Context managers
# File handling
with open('data.txt', 'w') as file:
    file.write("Hello, World!")
# File automatically closed after with block

# Custom context manager
class Timer:
    def __enter__(self):
        import time
        self.start = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        import time
        self.end = time.time()
        print(f"Elapsed time: {self.end - self.start:.2f} seconds")

# Usage
with Timer():
    # Some time-consuming operation
    sum(range(1000000))

# =============================================================================
# 6. ADVANCED FEATURES
# =============================================================================

# Decorators
def timing_decorator(func):
    """Decorator to measure function execution time"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        import time
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} took {end - start:.4f} seconds")
        return result
    return wrapper

@timing_decorator
def slow_function():
    import time
    time.sleep(0.1)
    return "Done!"

# Generators
def fibonacci_generator(n):
    """Generator for Fibonacci sequence"""
    a, b = 0, 1
    for _ in range(n):
        yield a
        a, b = b, a + b

# Generator expression
squares_gen = (x**2 for x in range(10))          # Memory efficient

# Iterator protocol
class CountDown:
    def __init__(self, start):
        self.start = start
    
    def __iter__(self):
        return self
    
    def __next__(self):
        if self.start <= 0:
            raise StopIteration
        self.start -= 1
        return self.start + 1

# Usage
for num in CountDown(5):
    print(num)                                   # 5, 4, 3, 2, 1

# =============================================================================
# 7. STANDARD LIBRARY ESSENTIALS
# =============================================================================

# Collections module
from collections import defaultdict, Counter, deque, OrderedDict

# defaultdict - never raises KeyError
word_count = defaultdict(int)
for word in ['apple', 'banana', 'apple', 'cherry']:
    word_count[word] += 1

# Counter - counting made easy
text = "hello world"
char_count = Counter(text)                       # Counter({'l': 3, 'o': 2, ...})
most_common = char_count.most_common(3)          # Top 3 most common

# deque - double-ended queue
queue = deque([1, 2, 3])
queue.appendleft(0)                              # Add to left
queue.append(4)                                  # Add to right
left_item = queue.popleft()                      # Remove from left

# datetime module
from datetime import datetime, timedelta, date

now = datetime.now()
today = date.today()
tomorrow = today + timedelta(days=1)
formatted = now.strftime("%Y-%m-%d %H:%M:%S")
parsed = datetime.strptime("2024-01-01", "%Y-%m-%d")

# itertools - iterator functions
from itertools import chain, combinations, permutations, product

# Chain multiple iterables
chained = list(chain([1, 2], [3, 4], [5, 6]))   # [1, 2, 3, 4, 5, 6]

# Combinations and permutations
combos = list(combinations([1, 2, 3, 4], 2))     # [(1,2), (1,3), (1,4), ...]
perms = list(permutations([1, 2, 3], 2))         # [(1,2), (1,3), (2,1), ...]
products = list(product([1, 2], ['a', 'b']))     # [(1,'a'), (1,'b'), (2,'a'), (2,'b')]

# functools - functional programming tools
from functools import reduce, partial, lru_cache

# reduce - apply function cumulatively
numbers = [1, 2, 3, 4, 5]
sum_all = reduce(lambda x, y: x + y, numbers)    # 15
product_all = reduce(lambda x, y: x * y, numbers)  # 120

# partial - partial function application
def multiply(x, y):
    return x * y

double = partial(multiply, 2)                    # Fix first argument
print(double(5))                                 # 10

# lru_cache - memoization decorator
@lru_cache(maxsize=128)
def expensive_function(n):
    # Simulate expensive computation
    return sum(range(n))

# Regular expressions
import re

text = "Contact: <EMAIL> or call ************"
emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
phones = re.findall(r'\d{3}-\d{3}-\d{4}', text)

# JSON handling
import json

data = {'name': 'Alice', 'age': 30, 'skills': ['Python', 'SQL']}
json_string = json.dumps(data, indent=2)         # Serialize to JSON
parsed_data = json.loads(json_string)            # Parse from JSON

# =============================================================================
# 8. SCIENTIFIC COMPUTING (NumPy, Pandas, XArray)
# =============================================================================

# NumPy essentials
import numpy as np

# Array creation
arr1d = np.array([1, 2, 3, 4, 5])
arr2d = np.array([[1, 2, 3], [4, 5, 6]])
zeros = np.zeros((3, 4))                         # 3x4 array of zeros
ones = np.ones((2, 3))                           # 2x3 array of ones
identity = np.eye(3)                             # 3x3 identity matrix
random_arr = np.random.random((2, 3))            # Random values [0, 1)
normal_arr = np.random.normal(0, 1, (100,))     # Normal distribution

# Array operations
arr = np.array([1, 2, 3, 4, 5])
print(f"Shape: {arr.shape}")                    # (5,)
print(f"Data type: {arr.dtype}")                # int64
print(f"Sum: {arr.sum()}")                      # 15
print(f"Mean: {arr.mean()}")                    # 3.0
print(f"Standard deviation: {arr.std()}")       # 1.58...

# Boolean indexing
mask = arr > 3                                   # [False, False, False, True, True]
filtered = arr[mask]                             # [4, 5]

# Broadcasting
arr2d = np.array([[1, 2, 3], [4, 5, 6]])
arr1d = np.array([10, 20, 30])
result = arr2d + arr1d                           # Broadcasts arr1d to each row

# Pandas essentials
import pandas as pd

# DataFrame creation
data = {
    'name': ['Alice', 'Bob', 'Charlie', 'Diana'],
    'age': [25, 30, 35, 28],
    'salary': [50000, 60000, 70000, 55000],
    'department': ['IT', 'HR', 'IT', 'Finance']
}
df = pd.DataFrame(data)

# Basic operations
print(df.head())                                 # First 5 rows
print(df.info())                                 # Data types and info
print(df.describe())                             # Statistical summary
print(df.shape)                                  # (rows, columns)

# Selection and filtering
it_employees = df[df['department'] == 'IT']      # Filter rows
high_earners = df[df['salary'] > 55000]         # Numeric filter
selected_cols = df[['name', 'salary']]          # Select columns

# GroupBy operations
dept_stats = df.groupby('department')['salary'].agg(['mean', 'min', 'max'])
dept_counts = df['department'].value_counts()

# Data manipulation
df['salary_k'] = df['salary'] / 1000             # New column
df['age_group'] = pd.cut(df['age'], bins=[0, 30, 40, 100],
                        labels=['Young', 'Middle', 'Senior'])

# Missing data handling
df_with_na = df.copy()
df_with_na.loc[1, 'salary'] = np.nan
filled_df = df_with_na.fillna(df_with_na['salary'].mean())
dropped_df = df_with_na.dropna()

# XArray essentials (for weather data)
import xarray as xr

# Create sample weather data
time = pd.date_range('2024-01-01', periods=365, freq='D')
lat = np.linspace(-90, 90, 37)
lon = np.linspace(0, 360, 72)

temperature = xr.DataArray(
    np.random.normal(288, 15, (365, 37, 72)),
    dims=['time', 'lat', 'lon'],
    coords={'time': time, 'lat': lat, 'lon': lon},
    name='temperature'
)

# XArray operations
global_mean = temperature.mean(dim=['lat', 'lon'])  # Time series
spatial_mean = temperature.mean(dim='time')         # Climatology
winter_data = temperature.sel(time=temperature.time.dt.season == 'DJF')
tropics = temperature.sel(lat=slice(-30, 30))

# Groupby operations
monthly_mean = temperature.groupby('time.month').mean()
seasonal_cycle = temperature.groupby('time.season').mean()

# =============================================================================
# 9. COMMON PATTERNS & BEST PRACTICES
# =============================================================================

# File I/O patterns
def read_file_safely(filename):
    """Safe file reading with error handling"""
    try:
        with open(filename, 'r') as file:
            return file.read()
    except FileNotFoundError:
        print(f"File {filename} not found")
        return None
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

# Data validation patterns
def validate_data(data, required_fields):
    """Validate data dictionary has required fields"""
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        raise ValueError(f"Missing required fields: {missing_fields}")
    return True

# Caching pattern
_cache = {}
def cached_expensive_function(n):
    """Manual caching implementation"""
    if n in _cache:
        return _cache[n]

    # Expensive computation
    result = sum(range(n))
    _cache[n] = result
    return result

# Factory pattern
def create_data_processor(processor_type):
    """Factory function for creating data processors"""
    processors = {
        'csv': lambda: "CSV Processor",
        'json': lambda: "JSON Processor",
        'xml': lambda: "XML Processor"
    }

    if processor_type not in processors:
        raise ValueError(f"Unknown processor type: {processor_type}")

    return processors[processor_type]()

# Configuration pattern
class Config:
    """Configuration class using class variables"""
    DEBUG = True
    DATABASE_URL = "sqlite:///app.db"
    API_KEY = "your-api-key-here"

    @classmethod
    def from_dict(cls, config_dict):
        """Create config from dictionary"""
        for key, value in config_dict.items():
            setattr(cls, key.upper(), value)
        return cls

# Singleton pattern
class DatabaseConnection:
    """Singleton pattern for database connection"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.connection = "Database connection established"
            self.initialized = True

# Context manager for timing
class TimeIt:
    """Context manager for timing code execution"""
    def __init__(self, description="Operation"):
        self.description = description

    def __enter__(self):
        import time
        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        import time
        elapsed = time.time() - self.start_time
        print(f"{self.description} took {elapsed:.4f} seconds")

# Usage examples
with TimeIt("Data processing"):
    # Some data processing code
    result = sum(range(100000))

# =============================================================================
# QUICK REFERENCE SUMMARY
# =============================================================================

print("""
🐍 PYTHON CHEATSHEET QUICK REFERENCE

📚 BASIC SYNTAX:
   • f"Hello {name}"           # f-strings
   • x, y = 1, 2              # Multiple assignment
   • first, *rest, last = lst  # Unpacking

🔄 CONTROL FLOW:
   • for i, val in enumerate(lst)  # Index and value
   • for a, b in zip(lst1, lst2)   # Parallel iteration
   • result = val if condition else other  # Ternary

📦 DATA STRUCTURES:
   • [x**2 for x in range(10)]     # List comprehension
   • {k: v for k, v in items}      # Dict comprehension
   • set1 & set2                   # Set intersection

🏗️ OOP:
   • @property                     # Property decorator
   • super().__init__()           # Call parent constructor
   • @classmethod / @staticmethod  # Class/static methods

⚠️ ERROR HANDLING:
   • try/except/else/finally      # Exception handling
   • with open() as f:            # Context managers
   • raise CustomError("msg")     # Custom exceptions

🚀 ADVANCED:
   • @functools.lru_cache         # Memoization
   • yield value                  # Generators
   • *args, **kwargs             # Variable arguments

📊 SCIENTIFIC:
   • np.array([1, 2, 3])         # NumPy arrays
   • df.groupby('col').mean()    # Pandas groupby
   • data.sel(lat=slice(0, 30))  # XArray selection

💡 BEST PRACTICES:
   • Use f-strings for formatting
   • Prefer list/dict comprehensions
   • Use context managers for resources
   • Handle exceptions gracefully
   • Write docstrings for functions
   • Use type hints for clarity
""")

# =============================================================================
# INTERVIEW-SPECIFIC PATTERNS
# =============================================================================

# Weather data processing patterns
def process_weather_data(data):
    """Common weather data processing pattern"""
    # 1. Validate input
    if data is None or len(data) == 0:
        return None

    # 2. Clean data
    cleaned = data.dropna()

    # 3. Filter outliers
    q1, q3 = cleaned.quantile([0.25, 0.75])
    iqr = q3 - q1
    filtered = cleaned[(cleaned >= q1 - 1.5*iqr) & (cleaned <= q3 + 1.5*iqr)]

    # 4. Return processed data
    return filtered

# Aggregation pattern (WeatherBench-X style)
def weighted_average(values, weights):
    """Compute weighted average - key pattern for interviews"""
    if len(values) != len(weights):
        raise ValueError("Values and weights must have same length")

    weighted_sum = sum(v * w for v, w in zip(values, weights))
    weight_sum = sum(weights)

    return weighted_sum / weight_sum if weight_sum != 0 else 0

# Error handling pattern for data processing
def safe_data_operation(func, data, default=None):
    """Safe wrapper for data operations"""
    try:
        return func(data)
    except (ValueError, TypeError, KeyError) as e:
        print(f"Data operation failed: {e}")
        return default
    except Exception as e:
        print(f"Unexpected error: {e}")
        return default

print("\n✅ Python cheatsheet complete! Ready for your interview! 🚀")
