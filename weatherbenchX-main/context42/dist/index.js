import path from "node:path";
import { Box, Text, useApp } from "ink";
import <PERSON>Text from "ink-big-text";
import Gradient from "ink-gradient";
import { useEffect, useMemo } from "react";
import { ExplorerStatus } from "./components/ExplorerStatus.js";
import { ProgressBar } from "./components/ProgressBar.js";
import Table from "./components/Table.js";
import { WorkersStatus } from "./components/WorkerStatus.js";
import { useProcessor } from "./hooks/use-processor.js";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
const outputPath = (inputDir, outputDir, lang) => path.relative(inputDir, path.join(outputDir, `${lang}.md`));
const Index = ({
  fileGroups,
  inputDir,
  outputDir,
  model,
  concurrency,
  total,
  database,
  debug
}) => {
  const { exit } = useApp();
  const { run, workers, queuedTasks, progress, results, error, reset } = useProcessor({
    model,
    concurrency,
    fileGroups,
    inputDir,
    outputDir,
    database
  });
  useEffect(() => {
    run();
  }, [run]);
  useEffect(() => {
    if (results != null || error != null) {
      database.close();
      reset();
      exit();
    }
  }, [results, error, exit, reset, database]);
  const resultsViewModel = useMemo(
    () => results == null ? [] : Array.from(results.keys()).map((language) => ({ language, path: outputPath(inputDir, outputDir, language) })).toSorted((a, b) => a.language.localeCompare(b.language)),
    [results, inputDir, outputDir]
  );
  return /* @__PURE__ */ jsxs(Box, { flexDirection: "column", paddingY: 1, children: [
    /* @__PURE__ */ jsx(Gradient, { name: "retro", children: /* @__PURE__ */ jsx(BigText, { text: "context42" }) }),
    results != null ? /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx(Box, { marginTop: 1, children: /* @__PURE__ */ jsx(Text, { color: "green", children: "\u2713 Style guides generated successfully!" }) }),
      /* @__PURE__ */ jsx(Table, { data: resultsViewModel, columnWidths: { language: 16, path: 32 } })
    ] }) : error != null ? /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx(ExplorerStatus, { fileGroups, isLoading: false }),
      /* @__PURE__ */ jsxs(Box, { marginTop: 1, children: [
        /* @__PURE__ */ jsxs(Text, { color: "red", children: [
          "\u2717 Error: ",
          error
        ] }),
        debug && /* @__PURE__ */ jsxs(Text, { dimColor: true, children: [
          "Run ID: ",
          database.runId
        ] })
      ] })
    ] }) : /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx(ProgressBar, { value: progress, max: total, label: "files" }),
      /* @__PURE__ */ jsx(WorkersStatus, { workers, inputDir, queuedTasks })
    ] })
  ] });
};
export {
  Index
};
//# sourceMappingURL=index.js.map
