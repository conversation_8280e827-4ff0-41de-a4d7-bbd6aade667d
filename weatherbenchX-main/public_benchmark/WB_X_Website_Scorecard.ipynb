{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "IlNStBFJ7SaS"}, "outputs": [], "source": ["import xarray as xr\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "import seaborn as sns\n", "import numpy as np\n", "import matplotlib as mpl\n", "import fsspec"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "sUkps2nMZAlI"}, "outputs": [], "source": ["# To save figures\n", "SAVE_PATH = './'\n", "# Results paths\n", "RESULTS_PATH = 'gs://wb2-app-data/v4'  # No trailing slash"]}, {"cell_type": "markdown", "metadata": {"id": "Xhj6FEVKxG6C"}, "source": ["## Plotting functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iMPCesPs8k75"}, "outputs": [], "source": ["# define helper function\n", "def set_y_labels(ax, dataset, levels=True):\n", "    ax.set_xticks([])\n", "    if levels:\n", "      ax.set_yticks(np.arange(len(dataset.level)))\n", "      ax.set_yticklabels(dataset.level.values)\n", "      # ax.tick_params(axis='y', which='major', pad=10)\n", "    else:\n", "      ax.set_yticks([0])\n", "      ax.tick_params(axis='y', color='None')\n", "      ax.set_yticklabels(['000'], color='None')\n", "\n", "    ax.spines['top'].set_color('0.7')\n", "    ax.spines['right'].set_color('0.7')\n", "    ax.spines['bottom'].set_color('0.7')\n", "    ax.spines['left'].set_color('0.7')\n", "\n", "def add_white_lines(ax, img, color='white'):\n", "  for i in range(img.shape[0]):\n", "    for j in range(img.shape[1]):\n", "      rect = mpl.patches.Rectangle(\n", "          (j - 0.5, i - 0.5),\n", "          1,\n", "          1,\n", "          linewidth=2,\n", "          edgecolor=color,\n", "          facecolor='None',\n", "      )\n", "      ax.add_patch(rect)\n", "\n", "def set_x_labels(ax, dataset, labels=None):\n", "  lead_time_h = int(dataset.lead_time[0] / np.timedelta64(1, 'h'))\n", "  factor_24h = 24 // lead_time_h\n", "  xticks = np.arange(0, len(dataset.lead_time), factor_24h)\n", "  ax.set_xticks(xticks)\n", "  ax.set_xticklabels(xticks + 1 // factor_24h if labels is None else labels)\n", "  ax.spines['top'].set_color('0.7')\n", "  ax.spines['right'].set_color('0.7')\n", "  ax.spines['bottom'].set_color('0.7')\n", "  ax.spines['left'].set_color('0.7')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cjCSP0zu8n1R"}, "outputs": [], "source": ["plt.rcParams['figure.facecolor'] = '0.9'\n", "# reds = sns.color_palette('Reds', 7)\n", "# blues = sns.color_palette('Blues_r', 7)\n", "# cmap = matplotlib.colors.ListedColormap(blues + [(0.95, 0.95, 0.95)] + reds)\n", "# cb_levels = [-50, -25, -15, -10, -5, -2, -1, 1, 2, 5, 10, 15, 25, 50]\n", "reds = sns.color_palette('Reds', 6)\n", "blues = sns.color_palette('Blues_r', 6)\n", "cmap = matplotlib.colors.ListedColormap(blues + [(0.95, 0.95, 0.95)] + reds)\n", "cb_levels = [-50, -20,-10, -5, -2, -1, 1, 2, 5, 10, 20, 50]\n", "norm = matplotlib.colors.BoundaryNorm(cb_levels, cmap.N, extend='both')\n", "cmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "UrvQ0fpsA-fw"}, "outputs": [], "source": ["def plot_main_scorecard(relative, absolute, variables, save_fn=None,\n", "                        subselect_models=None, nphysical=None, nml=None,\n", "                        custom_titles=None,\n", "                        custom_names=None, custom_subtitles=None):\n", "  matplotlib.rcParams.update(matplotlib.rcParamsDefault)\n", "\n", "  models = subselect_models or absolute.model.values\n", "\n", "  nmodels = len(models)\n", "  nvariables = len(variables)\n", "\n", "  panel_width = 2\n", "  label_width = 1 * panel_width\n", "  padding_right = 0.1\n", "\n", "  panel_height = panel_width / 5\n", "\n", "  title_height = panel_height * 1.25\n", "  cbar_height = panel_height * 3\n", "  spacing_height = panel_height * 0.1\n", "  spacing_width = panel_height * 0.3\n", "\n", "  total_width = label_width + nvariables * panel_width + (nvariables - 1) * spacing_width + padding_right\n", "  total_height = title_height + cbar_height + nmodels * panel_height + (nmodels - 1) * spacing_height\n", "\n", "  fig = plt.figure(figsize=(total_width, total_height))\n", "  gs = mpl.gridspec.GridSpec(\n", "      nmodels,\n", "      nvariables,\n", "      figure=fig,\n", "      left=label_width / total_width,\n", "      right=1 - padding_right / total_width,\n", "      top=1-(title_height / total_height),\n", "      bottom=cbar_height / total_height,\n", "      # w/hspace are relative to average panel size\n", "      hspace=spacing_height / panel_height,\n", "      wspace=spacing_width / panel_width\n", "  )\n", "  for row, m in enumerate(models):\n", "    for col, (var, l, metric) in enumerate(variables):\n", "      ax = fig.add_subplot(gs[row, col])\n", "\n", "      abs = absolute.sel(model=m, metric=metric)[var]\n", "      da = relative.sel(model=m, metric=metric)[var]\n", "      if l:\n", "        da = da.sel(level=l)\n", "        abs = abs.sel(level=l)\n", "      values = da.sel(lead_time=lead_times).values[None, :]\n", "      abs_values = abs.sel(lead_time=lead_times).values[None, :]\n", "      img = ax.imshow(values, aspect='auto', cmap=cmap, norm=norm)\n", "      set_y_labels(ax, da, levels=False)\n", "      add_white_lines(ax, values, color='0.9')\n", "      ax.grid(False)\n", "      if col == 0:\n", "        if custom_names is None:\n", "          label = m.split(' vs')[0]\n", "        else:\n", "          label = custom_names[row]\n", "        ax.set_ylabel(label, rotation='horizontal', horizontalalignment='right',\n", "                      verticalalignment='center', labelpad=-20, zorder=10, fontsize=9.5)\n", "        # Add physical model box\n", "        if (nphysical is not None) and (row == nphysical-1):\n", "          h=nphysical + (nphysical - 1) * spacing_height / panel_height\n", "          rect = mpl.patches.Rectangle(\n", "              (-0.98, 0), width=0.96, height=h, color=\"lightsteelblue\", transform=ax.transAxes,\n", "              clip_on=False, alpha=0.2, zorder=0.1\n", "          )\n", "          ax.add_patch(rect)\n", "          if nphysical >=3:\n", "            ax.text(-0.9, h/2, 'Physical models', ha='center',va='center',\n", "                    transform=ax.transAxes, fontsize=8, rotation='vertical')\n", "        # Add ML model box\n", "        elif (nml is not None) and (row == nphysical + nml - 1):\n", "          h=nml + (nml - 1) * spacing_height / panel_height\n", "          rect = mpl.patches.Rectangle(\n", "              (-0.98, 0), width=0.96, height=h, color=\"lightcoral\", transform=ax.transAxes,\n", "              clip_on=False, alpha=0.2, zorder=0.1\n", "          )\n", "          ax.add_patch(rect)\n", "          if nml >=3:\n", "            ax.text(-0.9, h/2, 'ML / hybrid models', ha='center',va='center',\n", "                    transform=ax.transAxes, fontsize=8, rotation='vertical')\n", "        else:\n", "          ax.set_zorder(10)\n", "      if row == 0:\n", "\n", "        ax.set_title(custom_titles[col], fontsize=10, pad=17)\n", "        ax.text(0.5, 1.25, custom_subtitles[col], ha='center',va='center', transform=ax.transAxes,\n", "                fontsize=6)\n", "      if np.isnan(values).all():\n", "        ax.remove()\n", "        continue\n", "\n", "\n", "      if row == nmodels - 1:\n", "        lead_time_in_days = absolute.lead_time.values.astype('timedelta64[h]').astype(int) // 24\n", "        set_x_labels(ax, da, labels=lead_time_in_days)\n", "        ax.set_xlabel('Lead time [days]')\n", "      # Add absolute numbers\n", "      for i, v in enumerate(abs_values[0]):\n", "        if m == 'Climatology vs ERA5':\n", "          v = np.mean(abs_values)\n", "        if np.isfinite(v):\n", "          if var == 'Specific Humidity': v *= 1000\n", "          if var == '24h Precipitation' and metric == 'CRPS': v *= 1000\n", "\n", "          if v > 10:\n", "            v = str(v)[:3].rstrip('.')\n", "          else:\n", "            v = str(v)[:4]\n", "          ax.text(i, 0, v, ha='center',va='center', fontsize=8)\n", "\n", "  rel_cbar_height = cbar_height / total_height\n", "  cax = fig.add_axes((0.35, rel_cbar_height * 0.4, 0.5, rel_cbar_height * 0.1))\n", "  cb = fig.colorbar(img, cax=cax, orientation='horizontal', fraction=0.01)\n", "  cb.ax.set_xticks(cb_levels)\n", "  if '24h Precipitation' in np.ravel(variables):\n", "    cb.ax.set_xlabel(r'Better $\\longleftarrow$ % difference in RMSE/SEEPS vs IFS HRES $\\longrightarrow$ Worse')\n", "  else:\n", "    cb.ax.set_xlabel(r'Better $\\longleftarrow$ % difference in RMSE vs IFS HRES $\\longrightarrow$ Worse')\n", "\n", "  if save_fn:\n", "    with fsspec.open(f'{SAVE_PATH}/{save_fn}', 'wb', auto_mkdir=True) as f:\n", "      # fig.patch.set_facecolor('gray')\n", "      plt.savefig(f, dpi=300)\n", "\n", "      plt.close()"]}, {"cell_type": "markdown", "metadata": {"id": "6yEOqCdjx7t0"}, "source": ["## Data preprocessing functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Rk4JLimzx9b8"}, "outputs": [], "source": ["def replace_analysis_with_era_precip(results):\n", "  # Since vs Analysis doesn't have precip scores, replace vs analysis precip scores with ERA precip scores\n", "  precip_variables = ['6h Precipitation', '24h Precipitation']\n", "  for model in results.model.values:\n", "    if model.endswith('vs Analysis'):\n", "      model_vs_era = model.replace('vs Analysis', 'vs ERA5')\n", "      if model_vs_era in results.model.values:\n", "        for v in precip_variables:\n", "          results[v].loc[dict(model=model)] = results[v].sel(dict(model=model_vs_era))\n", "  return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5MYZZTaLycuZ"}, "outputs": [], "source": ["def compute_relative_results(results, reference_model):\n", "  # Compute relative result\n", "  reference = results.sel(model=reference_model, drop=True)\n", "  relative = ((results - reference) / reference * 100)\n", "  return relative"]}, {"cell_type": "markdown", "metadata": {"id": "oMqvjiK8xT9K"}, "source": ["## Deterministic"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CpvRR4RN8e2X"}, "outputs": [], "source": ["lead_times = np.array([1, 3, 5, 7, 10], dtype='timedelta64[D]')\n", "models = [\n", "    'IFS HRES vs Analysis',\n", "    'IFS ENS (mean) vs Analysis',\n", "    'ERA5-Forecasts vs ERA5',\n", "    'Pangu-Weather (oper.) vs Analysis',\n", "    'GraphCast (oper.) vs Analysis',\n", "    'GenCast (oper.) (mean) vs Analysis',\n", "    # 'GenCast 100m U/V (oper.) (mean) vs Analysis',\n", "    '<PERSON><PERSON><PERSON> (2022) vs ERA<PERSON>',\n", "    'Pangu-Weather vs ERA5',\n", "    'GraphCast vs ERA5',\n", "    'FuXi vs ERA5',\n", "    'NeuralGCM 0.7 vs ERA5',\n", "    'NeuralGCM ENS (mean) vs ERA5',\n", "    'GenCast (mean) vs ERA5',\n", "    'Stormer <PERSON>NS (mean) vs ERA5',\n", "    'Excarta (HEAL-ViT) vs ERA5',\n", "    # '<PERSON>win vs ERA5',\n", "    'ArchesWeather-Mx4 vs ERA5',\n", "    'ArchesWeatherGen (mean) vs ERA5',\n", "    'Climatology vs ERA5'\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "B_LQZYH477SF"}, "outputs": [], "source": ["results = xr.open_zarr(f'{RESULTS_PATH}/deterministic.zarr').compute()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "07doZHhn-b<PERSON>y"}, "outputs": [], "source": ["# Make all coarse selections\n", "results = results.sel(lead_time=lead_times, region='Global', year='2020', resolution='240x121')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JzqbNldDajVZ"}, "outputs": [], "source": ["results = replace_analysis_with_era_precip(results)\n", "relative = compute_relative_results(results, 'IFS HRES vs Analysis')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "RnuY8JbFCvz-"}, "outputs": [], "source": ["# Pick models\n", "results = results.sel(model=models)\n", "relative = relative.sel(model=models)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3LGHaSNc8qre"}, "outputs": [], "source": ["nphysical = 3\n", "upper_variables = [\n", "    ('Geopotential', 500, 'RMSE'),\n", "    ('Temperature', 850, 'RMSE'),\n", "    ('Specific Humidity', 700, 'RMSE'),\n", "    ('Wind Vector', 850, 'RMSE'),\n", "]\n", "upper_titles = ['Geopotential', 'Temperature', 'Humi<PERSON>y', 'Wind Vector']\n", "upper_subtitles = [f'500hPa geopotential RMSE [kg$^2$/m$^2$]', '850hPa temperature RMSE [K]',\n", "                                     '700hPa specific humidity RMSE [g/kg]', '850hPa wind vector RMSE [m/s]']\n", "surface_variables = [\n", "    ('2m Temperature', None, 'RMSE'),\n", "    ('Sea Level Pressure', None, 'RMSE'),\n", "    ('10m Wind Speed', None, 'RMSE'),\n", "    ('24h Precipitation', None, 'SEEPS'),\n", "]\n", "surface_titles = ['2m Temperature', 'Surface Pressure', '10m Wind Speed', 'Precipitation']\n", "surface_subtitles = ['RMSE [K]', 'RMSE [Pa]',\n", "                                     'RMSE [m/s]', '24h precipitation SEEPS']\n", "surface_models = [\n", "    'IFS HRES vs Analysis',\n", "    'IFS ENS (mean) vs Analysis',\n", "    'ERA5-Forecasts vs ERA5',\n", "    'Pangu-Weather (oper.) vs Analysis',\n", "    'GraphCast (oper.) vs Analysis',\n", "    'GenCast (oper.) (mean) vs Analysis',\n", "    'Pangu-Weather vs ERA5',\n", "    'GraphCast vs ERA5',\n", "    'FuXi vs ERA5',\n", "    'GenCast (mean) vs ERA5',\n", "    'Stormer <PERSON>NS (mean) vs ERA5',\n", "    'Excarta (HEAL-ViT) vs ERA5',\n", "    # '<PERSON>win vs ERA5',\n", "    'ArchesWeather-Mx4 vs ERA5',\n", "    'ArchesWeatherGen (mean) vs ERA5',\n", "    'Climatology vs ERA5'\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "oziBprk-C3b8"}, "outputs": [], "source": ["plot_main_scorecard(\n", "    relative,\n", "    results,\n", "    variables=upper_variables,\n", "    nphysical=nphysical,\n", "    nml=len(models) - nphysical - 1,\n", "    custom_titles=upper_titles,\n", "    custom_subtitles=upper_subtitles,\n", "    save_fn='deterministic_upper.png'\n", ")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Oi0cLLaOh5V0"}, "outputs": [], "source": ["plot_main_scorecard(\n", "    relative,\n", "    results,\n", "    variables=surface_variables,\n", "    subselect_models=surface_models,\n", "    nphysical=nphysical,\n", "    nml=len(surface_models) - nphysical - 1,\n", "    custom_titles=surface_titles,\n", "    custom_subtitles=surface_subtitles,\n", "    save_fn='deterministic_surface.png'\n", ")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bAIIRFbeK2Bw"}, "outputs": [], "source": ["# from IPython.display import Image\n", "# Image('test.png')"]}, {"cell_type": "markdown", "metadata": {"id": "UpTwJXYZDAvL"}, "source": ["## Probabilistic"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3bJVX38o0ACP"}, "outputs": [], "source": ["lead_times = np.array([1, 3, 5, 10, 15], dtype='timedelta64[D]')\n", "models = [\n", "    'IFS ENS vs Analysis',\n", "    'GenCast (oper.) vs Analysis',\n", "    'NeuralGCM ENS vs ERA5',\n", "    'GenCast vs ERA5',\n", "    'ArchesWeatherGen vs ERA5',\n", "    'Probabilistic Climatology vs ERA5',\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eki1eUIW0ACQ"}, "outputs": [], "source": ["results = xr.open_zarr(f'{RESULTS_PATH}/probabilistic.zarr').compute()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "PX0M-d_K0J1R"}, "outputs": [], "source": ["results"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pp3GmXgA0ACQ"}, "outputs": [], "source": ["# Make all coarse selections\n", "results = results.sel(lead_time=lead_times, region='Global', year='2020', resolution='240x121')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-j1BxjkQ0ACQ"}, "outputs": [], "source": ["results = replace_analysis_with_era_precip(results)\n", "relative = compute_relative_results(results, 'IFS ENS vs Analysis')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "YbJvdCWN0ACQ"}, "outputs": [], "source": ["# Pick models\n", "results = results.sel(model=models)\n", "relative = relative.sel(model=models)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-Rw3UmnI0ACQ"}, "outputs": [], "source": ["nphysical = 1\n", "upper_variables = [\n", "    ('Geopotential', 500, 'CRPS'),\n", "    ('Temperature', 850, 'CRPS'),\n", "    ('Specific Humidity', 700, 'CRPS'),\n", "    ('Wind Speed', 850, 'CRPS'),\n", "]\n", "upper_titles = ['Geopotential', 'Temperature', 'Humi<PERSON>y', 'Wind Speed']\n", "upper_subtitles = [f'500hPa geopotential CRPS [kg$^2$/m$^2$]', '850hPa temperature CRPS [K]',\n", "                                     '700hPa specific humidity CRPS [g/kg]', '850hPa wind speed CRPS [m/s]']\n", "surface_variables = [\n", "    ('2m Temperature', None, 'CRPS'),\n", "    ('Sea Level Pressure', None, 'CRPS'),\n", "    ('10m Wind Speed', None, 'CRPS'),\n", "    ('24h Precipitation', None, 'CRPS'),\n", "]\n", "surface_titles = ['2m Temperature', 'Surface Pressure', '10m Wind Speed', 'Precipitation']\n", "surface_subtitles = ['CRPS [K]', 'CRPS [Pa]',\n", "                                     'CRPS [m/s]', '24h precipitation CRPS [mm/h]']\n", "surface_models = [\n", "    'IFS ENS vs Analysis',\n", "    'GenCast (oper.) vs Analysis',\n", "    'GenCast vs ERA5',\n", "    'ArchesWeatherGen vs ERA5',\n", "    'Probabilistic Climatology vs ERA5'\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CuMh2cY50ACR"}, "outputs": [], "source": ["plot_main_scorecard(\n", "    relative,\n", "    results,\n", "    variables=upper_variables,\n", "    nphysical=nphysical,\n", "    nml=len(models) - nphysical - 1,\n", "    custom_titles=upper_titles,\n", "    custom_subtitles=upper_subtitles,\n", "    save_fn='probabilistic_upper.png'\n", ")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "RYJUshkUDNaN"}, "outputs": [], "source": ["plot_main_scorecard(\n", "    relative,\n", "    results,\n", "    variables=surface_variables,\n", "    subselect_models=surface_models,\n", "    nphysical=nphysical,\n", "    nml=len(surface_models) - nphysical - 1,\n", "    custom_titles=surface_titles,\n", "    custom_subtitles=surface_subtitles,\n", "    save_fn='probabilistic_surface.png'\n", ")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_aeL6QM53bIC"}, "outputs": [], "source": []}], "metadata": {"colab": {"last_runtime": {"build_target": "//gdm/weather/colab_base:weather_notebook", "kind": "private"}, "private_outputs": true, "provenance": [{"file_id": "1Es6_7z74Dy0GEIwrgZ8CEJ5sNI1OYNtl", "timestamp": 1739197528174}]}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}