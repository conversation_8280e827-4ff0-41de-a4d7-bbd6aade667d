{"version": 3, "sources": ["../../src/components/ProgressBar.tsx"], "sourcesContent": ["import { Box, Text } from \"ink\"\nimport type React from \"react\"\n\nexport type ProgressBarProps = {\n  readonly value: number\n  readonly max: number\n  readonly label?: string\n  readonly terminalWidth?: number\n}\n\nexport const ProgressBar: React.FC<ProgressBarProps> = ({ value, max, label, terminalWidth = 80 }) => {\n  const width = 40\n  const percentage = max > 0 ? Math.min(100, Math.round((value / max) * 100)) : 0\n  const filled = Math.round((percentage / 100) * width)\n  const empty = width - filled\n\n  // Calculate component width: [progress bar] + value/max + label + percentage\n  const valueText = `${value}/${max}`\n  const percentageText = `(${percentage}%)`\n  const componentWidth = 2 + width + 1 + valueText.length + 1 + (label ? label.length + 1 : 0) + percentageText.length\n\n  const leftPadding = Math.max(0, Math.floor((terminalWidth - componentWidth) / 2))\n\n  return (\n    <Box>\n      <Text>{\" \".repeat(leftPadding)}</Text>\n      <Box flexDirection=\"row\" gap={1}>\n        <Text>[</Text>\n        <Text color=\"green\">{\"█\".repeat(filled)}</Text>\n        <Text dimColor>{\"░\".repeat(empty)}</Text>\n        <Text>]</Text>\n        <Text>\n          {value}/{max}\n        </Text>\n        {label && <Text>{label}</Text>}\n        <Text dimColor>({percentage}%)</Text>\n      </Box>\n    </Box>\n  )\n}\n"], "mappings": "AAAA,SAAS,KAAK,YAAY;AAyBpB,cAME,YANF;AAfC,MAAM,cAA0C,CAAC,EAAE,OAAO,KAAK,OAAO,gBAAgB,GAAG,MAAM;AACpG,QAAM,QAAQ;AACd,QAAM,aAAa,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,MAAO,QAAQ,MAAO,GAAG,CAAC,IAAI;AAC9E,QAAM,SAAS,KAAK,MAAO,aAAa,MAAO,KAAK;AACpD,QAAM,QAAQ,QAAQ;AAGtB,QAAM,YAAY,GAAG,KAAK,IAAI,GAAG;AACjC,QAAM,iBAAiB,IAAI,UAAU;AACrC,QAAM,iBAAiB,IAAI,QAAQ,IAAI,UAAU,SAAS,KAAK,QAAQ,MAAM,SAAS,IAAI,KAAK,eAAe;AAE9G,QAAM,cAAc,KAAK,IAAI,GAAG,KAAK,OAAO,gBAAgB,kBAAkB,CAAC,CAAC;AAEhF,SACE,qBAAC,OACC;AAAA,wBAAC,QAAM,cAAI,OAAO,WAAW,GAAE;AAAA,IAC/B,qBAAC,OAAI,eAAc,OAAM,KAAK,GAC5B;AAAA,0BAAC,QAAK,eAAC;AAAA,MACP,oBAAC,QAAK,OAAM,SAAS,mBAAI,OAAO,MAAM,GAAE;AAAA,MACxC,oBAAC,QAAK,UAAQ,MAAE,mBAAI,OAAO,KAAK,GAAE;AAAA,MAClC,oBAAC,QAAK,eAAC;AAAA,MACP,qBAAC,QACE;AAAA;AAAA,QAAM;AAAA,QAAE;AAAA,SACX;AAAA,MACC,SAAS,oBAAC,QAAM,iBAAM;AAAA,MACvB,qBAAC,QAAK,UAAQ,MAAC;AAAA;AAAA,QAAE;AAAA,QAAW;AAAA,SAAE;AAAA,OAChC;AAAA,KACF;AAEJ;", "names": []}