import React, { forwardRef } from 'react';
import { backgroundContext } from './BackgroundContext.js';
/**
 * `<Box>` is an essential Ink component to build your layout. It's like `<div style="display: flex">` in the browser.
 */
const Box = forwardRef(({ children, backgroundColor, ...style }, ref) => {
    const boxElement = (React.createElement("ink-box", { ref: ref, style: {
            flexWrap: 'nowrap',
            flexDirection: 'row',
            flexGrow: 0,
            flexShrink: 1,
            ...style,
            backgroundColor,
            overflowX: style.overflowX ?? style.overflow ?? 'visible',
            overflowY: style.overflowY ?? style.overflow ?? 'visible',
        } }, children));
    // If this Box has a background color, provide it to children via context
    if (backgroundColor) {
        return (React.createElement(backgroundContext.Provider, { value: backgroundColor }, boxElement));
    }
    return boxElement;
});
Box.displayName = 'Box';
export default Box;
//# sourceMappingURL=Box.js.map