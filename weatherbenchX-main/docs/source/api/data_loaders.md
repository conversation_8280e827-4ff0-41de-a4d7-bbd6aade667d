# Data Loaders



## Base
```{eval-rst}
.. currentmodule:: weatherbenchX.data_loaders.base

.. autoclass:: DataLoader
```

## Xarray Data Loaders
```{eval-rst}
.. currentmodule:: weatherbenchX.data_loaders.xarray_loaders

.. autoclass:: XarrayDataLoader
.. autoclass:: PredictionsFromXarray
.. autoclass:: TargetsFromXarray
.. autoclass:: ClimatologyFromXarray
.. autoclass:: PersistenceFromXarray
.. autoclass:: ProbabilisticClimatologyFromXarray
```

## Sparse Data Loaders
```{eval-rst}
.. currentmodule:: weatherbenchX.data_loaders.sparse_parquet

.. autoclass:: SparseObservationsFromParquet
```

## Latency Wrappers
```{eval-rst}
.. currentmodule:: weatherbenchX.data_loaders.latency_wrappers

.. autoclass:: ConstantLatencyWrapper
.. autoclass:: XarrayConstantLatencyWrapper
.. autoclass:: MultipleConstantLatencyWrapper
```

