# WeatherBench-X Python Style Guide

*Generated by analyzing the WeatherBench-X codebase patterns*

## Import Organization

### Standard Pattern
```python
# Standard library imports (alphabetical)
import abc
import dataclasses
import logging
from collections.abc import <PERSON><PERSON><PERSON>
from typing import Any, Collection, Mapping, Optional, Sequence, Union

# Third-party imports (alphabetical)
import apache_beam as beam
import numpy as np
import xarray as xr

# Local imports (relative imports preferred)
from weatherbenchX import aggregation
from weatherbenchX import binning
from weatherbenchX import weighting
from weatherbenchX.data_loaders import base as data_loaders_base
from weatherbenchX.metrics import base as metrics_base
```

### Import Conventions
- **Standard library first**, then **third-party**, then **local imports**
- Use **alphabetical ordering** within each group
- Prefer **relative imports** for local modules
- Use **aliases** for commonly used modules: `base as metrics_base`
- Import specific classes/functions when used frequently

## Class Definitions

### Dataclass Pattern
```python
@dataclasses.dataclass
class AggregationState:
  """An object that contains a sum of weighted statistics and a sum of weights.

  Allows for aggregation over multiple chunks before computing a final weighted
  mean.

  Attributes:
    sum_weighted_statistics: Structure containing summed/aggregated statistics,
      as a DataArray or nested dictionary of DataArrays, or None.
    sum_weights: Similar structure containing the corresponding summed weights.
  """

  sum_weighted_statistics: Any
  sum_weights: Any

  @classmethod
  def zero(cls) -> 'AggregationState':
    """An initial/'zero' aggregation state."""
    return cls(sum_weighted_statistics=None, sum_weights=None)
```

### Abstract Base Class Pattern
```python
class Binning(abc.ABC):
  """Binning base class."""

  def __init__(self, bin_dim_name: str):
    """Init.

    Args:
      bin_dim_name: Name of binning dimension.
    """
    self.bin_dim_name = bin_dim_name

  @abc.abstractmethod
  def create_bin_mask(
      self,
      statistic: xr.DataArray,
  ) -> xr.DataArray:
    """Creates a bin mask for a statistic.

    Args:
      statistic: Individual DataArray with statistic values.

    Returns:
      bin_mask: Boolean mask with shape that broadcasts against the statistic.
    """
```

## Function Definitions

### Type Hints
- **Always use type hints** for function parameters and return values
- Use **Union types** for multiple possible types: `Union[np.ndarray, slice]`
- Use **Optional** for nullable parameters: `Optional[Callable[[], None]]`
- Use **Collection/Sequence** for generic containers
- Use **forward references** with quotes for self-referencing types: `'AggregationState'`

### Function Signature Style
```python
def define_pipeline(
    root: beam.Pipeline,
    times: time_chunks.TimeChunks,
    predictions_loader: data_loaders_base.DataLoader,
    targets_loader: data_loaders_base.DataLoader,
    metrics: Mapping[str, metrics_base.Metric],
    aggregator: aggregation.Aggregator,
    out_path: str,
    setup_fn: Optional[Callable[[], None]] = None,
):
  """Defines a beam pipeline for calculating aggregated metrics.

  Args:
    root: Pipeline root.
    times: TimeChunks instance.
    predictions_loader: DataLoader instance.
    targets_loader: DataLoader instance.
    metrics: A dictionary of metrics to compute.
    aggregator: Aggregation instance.
    out_path: The full path to write the metrics to.
    setup_fn: (Optional) A function to call once per worker.
  """
```

## Docstring Style

### Google Style Docstrings
```python
def aggregation_fn(
    self,
    stat: xr.DataArray,
) -> xr.DataArray | None:
  """Returns the aggregation function.
  
  Recall that masked out values have already been set to zero in
  aggregate_statistics. The logic below has to respect this.

  Args:
    stat: Input DataArray with statistics.

  Returns:
    Aggregated DataArray or None if aggregation cannot be performed.

  Raises:
    ValueError: If bin dimension names are not unique.
  """
```

### Class Docstring Pattern
```python
class Aggregator:
  """Defines aggregation over set of dataset dimensions.

  Note on NaNs: By default, all reductions are performed with skipna=False,
  meaning that the aggregated statistics will be NaN if any of the input
  statistics are NaN.

  Attributes:
    reduce_dims: Dimensions to average over.
    bin_by: List of binning instances. All bins will be multiplied.
    weigh_by: List of weighting instance. All weights will be multiplied.
    masked: If True, aggregation will only be performed for non-masked values.
    skipna: If True, NaNs will be omitted in the aggregation.
  """
```

## Naming Conventions

### Variables and Functions
- **snake_case** for variables and functions: `reduce_dims`, `bin_by`
- **Descriptive names**: `sum_weighted_statistics` not `sum_stats`
- **Consistent prefixes**: `create_bin_mask`, `compute_metric_values`

### Classes
- **PascalCase** for classes: `AggregationState`, `LoadPredictionsAndTargets`
- **Descriptive compound names**: `ComputeAndFormatStatistics`

### Constants
- **UPPER_CASE** for module-level constants
- **Descriptive names**: `PREDICTION_PATH`, `REDUCE_DIMS`

## Code Organization

### File Structure
```python
#!/usr/bin/env python3
"""Module docstring describing the purpose."""

# Copyright header (Google style)
# Copyright 2025 Google LLC
# Licensed under the Apache License, Version 2.0

# Imports (organized as described above)

# Constants (if any)

# Classes and functions (logical grouping)

# Main execution block
if __name__ == '__main__':
  # Entry point code
```

### Section Separators
```python
# =============================================================================
# SECTION NAME
# =============================================================================
```

## Error Handling

### Exception Handling Pattern
```python
try:
  result = process_data(data)
  return result
except FileNotFoundError:
  print(f"File {filename} not found")
  return None
except Exception as e:
  print(f"Error processing {filename}: {e}")
  return None
```

### Validation Pattern
```python
if not data:
  return pd.DataFrame()

required_cols = ['station', 'temp', 'timestamp']
if not all(col in df.columns for col in required_cols):
  raise ValueError(f"Missing required columns: {required_cols}")
```

## Data Processing Patterns

### XArray Operations
```python
# Selection with method chaining
regional_data = ds.sel(
    lat=slice(region['lat_min'], region['lat_max']),
    lon=slice(region['lon_min'], region['lon_max'])
)

# Aggregation with explicit dimensions
temp_timeseries = regional_data['temperature'].mean(dim=['lat', 'lon'])
```

### Pandas Operations
```python
# Method chaining for clean code
result = (df
          .dropna(subset=['temp'])
          .query('temp >= -50 and temp <= 60')
          .groupby('station')
          .resample('D', on='timestamp')
          .agg({'temp': ['mean', 'min', 'max']})
          .round(2))
```

## Configuration and Setup

### Flag Definitions (absl style)
```python
from absl import flags

PREDICTION_PATH = flags.DEFINE_string(
    'prediction_path',
    None,
    'Path to prediction data.',
    required=True
)

VARIABLES = flags.DEFINE_list(
    'variables',
    ['temperature', 'geopotential'],
    'List of variables to evaluate.'
)
```

### Dictionary Configuration
```python
regions = {
    'global': ((-90, 90), (0, 360)),
    'northern-hemisphere': ((20, 90), (0, 360)),
}

all_metrics = {
    'rmse': deterministic.RMSE(),
    'mse': deterministic.MSE()
}
```

## Testing Patterns

### Test Class Structure
```python
class AggregationTest(absltest.TestCase):
  """Test aggregation functionality."""

  def setUp(self):
    """Set up test data."""
    self.test_data = self._create_test_data()

  def test_basic_aggregation(self):
    """Test basic aggregation functionality."""
    result = self._aggregate(metrics, predictions, targets)
    self.assertIsNotNone(result)

  def _create_test_data(self):
    """Helper method to create test data."""
    # Implementation details
```

## Key Principles

1. **Type Safety**: Always use type hints
2. **Documentation**: Comprehensive docstrings with Args/Returns
3. **Modularity**: Clear separation of concerns
4. **Consistency**: Follow established patterns throughout codebase
5. **Readability**: Descriptive names and clear structure
6. **Error Handling**: Graceful handling of edge cases
7. **Testing**: Comprehensive test coverage with clear test structure
