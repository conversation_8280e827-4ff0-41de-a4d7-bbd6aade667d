pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*

grayscale style (c) MY Sun <<EMAIL>>

*/
.hljs {
  color: #333;
  background: #fff
}
.hljs-comment,
.hljs-quote {
  color: #777;
  font-style: italic
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
  color: #333;
  font-weight: bold
}
.hljs-number,
.hljs-literal {
  color: #777
}
.hljs-string,
.hljs-doctag,
.hljs-formula {
  color: #333;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAAJ0lEQVQIW2O8e/fufwYGBgZBQUEQxcCIIfDu3Tuwivfv30NUoAsAALHpFMMLqZlPAAAAAElFTkSuQmCC) repeat
}
.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: #000;
  font-weight: bold
}
.hljs-subst {
  font-weight: normal
}
.hljs-title.class_,
.hljs-class .hljs-title,
.hljs-type,
.hljs-name {
  color: #333;
  font-weight: bold
}
.hljs-tag {
  color: #333
}
.hljs-regexp {
  color: #333;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAICAYAAADA+m62AAAAPUlEQVQYV2NkQAN37979r6yszIgujiIAU4RNMVwhuiQ6H6wQl3XI4oy4FMHcCJPHcDS6J2A2EqUQpJhohQDexSef15DBCwAAAABJRU5ErkJggg==) repeat
}
.hljs-symbol,
.hljs-bullet,
.hljs-link {
  color: #000;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAKElEQVQIW2NkQAO7d+/+z4gsBhJwdXVlhAvCBECKwIIwAbhKZBUwBQA6hBpm5efZsgAAAABJRU5ErkJggg==) repeat
}
.hljs-built_in {
  color: #000;
  text-decoration: underline
}
.hljs-meta {
  color: #999;
  font-weight: bold
}
.hljs-deletion {
  color: #fff;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAADCAYAAABS3WWCAAAAE0lEQVQIW2MMDQ39zzhz5kwIAQAyxweWgUHd1AAAAABJRU5ErkJggg==) repeat
}
.hljs-addition {
  color: #000;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAJCAYAAADgkQYQAAAALUlEQVQYV2N89+7dfwYk8P79ewZBQUFkIQZGOiu6e/cuiptQHAPl0NtNxAQBAM97Oejj3Dg7AAAAAElFTkSuQmCC) repeat
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}