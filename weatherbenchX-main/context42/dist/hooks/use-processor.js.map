{"version": 3, "sources": ["../../src/hooks/use-processor.ts"], "sourcesContent": ["import { useCallback, useMemo, useState } from \"react\"\nimport { createStyleGuideProcessor } from \"../lib/processor.js\"\nimport type { FileGroup, Language, Processor, ProcessorOptions, QueuedTask, Worker } from \"../lib/types.js\"\n\ntype UseProcessorOptions = ProcessorOptions & {\n  fileGroups?: Map<Language, FileGroup[]>\n  outputDir: string\n}\n\ntype UseProcessorResult = {\n  // State\n  processor: Processor\n  workers: Worker[]\n  queuedTasks: QueuedTask[]\n  progress: number\n  results: Map<Language, string> | null\n  error: string | null\n  isRunning: boolean\n\n  // Methods\n  run: () => void // Returns void for useEffect compatibility\n  reset: () => void\n}\n\nexport const useProcessor = (options: UseProcessorOptions): UseProcessorResult => {\n  const { model, concurrency, inputDir, onWorkerUpdate, fileGroups, outputDir } = options\n\n  // State\n  const [progress, setProgress] = useState(0)\n  const [results, setResults] = useState<Map<Language, string> | null>(null)\n  const [error, setError] = useState<string | null>(null)\n  const [isRunning, setIsRunning] = useState(false)\n  const [queuedTasks, setQueuedTasks] = useState<QueuedTask[]>([])\n\n  // Create processor instance\n  const processor = useMemo(\n    () =>\n      createStyleGuideProcessor({\n        model,\n        concurrency,\n        inputDir,\n        database: options.database,\n        onWorkerUpdate: (updatedWorker: Worker) => {\n          setWorkers(previousWorkers => previousWorkers.map(w => (w.id === updatedWorker.id ? updatedWorker : w)))\n          onWorkerUpdate?.(updatedWorker)\n        },\n        onProgress: setProgress,\n        onQueueUpdate: setQueuedTasks,\n      }),\n    [model, concurrency, inputDir, onWorkerUpdate, options.database],\n  )\n  const [workers, setWorkers] = useState<Worker[]>(processor.workers)\n\n  // Run method\n  const run = useCallback(async () => {\n    // Validate required options for running\n    if (!fileGroups || !inputDir || !outputDir) {\n      setError(\"Missing required options: fileGroups, inputDir, or outputDir\")\n      return\n    }\n\n    // Reset state\n    setError(null)\n    setResults(null)\n    setIsRunning(true)\n\n    try {\n      const styleGuides = await processor.run({ fileGroups, inputDir, outputDir })\n      setResults(styleGuides)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"An unknown error occurred\")\n    } finally {\n      setIsRunning(false)\n    }\n  }, [processor, fileGroups, inputDir, outputDir])\n\n  // Reset method\n  const reset = useCallback(() => {\n    processor.reset()\n    setProgress(0)\n    setResults(null)\n    setError(null)\n    setIsRunning(false)\n    setQueuedTasks([])\n  }, [processor])\n\n  return {\n    workers,\n    queuedTasks,\n    processor,\n    progress,\n    results,\n    error,\n    isRunning,\n    run,\n    reset,\n  }\n}\n"], "mappings": "AAAA,SAAS,aAAa,SAAS,gBAAgB;AAC/C,SAAS,iCAAiC;AAuBnC,MAAM,eAAe,CAAC,YAAqD;AAChF,QAAM,EAAE,OAAO,aAAa,UAAU,gBAAgB,YAAY,UAAU,IAAI;AAGhF,QAAM,CAAC,UAAU,WAAW,IAAI,SAAS,CAAC;AAC1C,QAAM,CAAC,SAAS,UAAU,IAAI,SAAuC,IAAI;AACzE,QAAM,CAAC,OAAO,QAAQ,IAAI,SAAwB,IAAI;AACtD,QAAM,CAAC,WAAW,YAAY,IAAI,SAAS,KAAK;AAChD,QAAM,CAAC,aAAa,cAAc,IAAI,SAAuB,CAAC,CAAC;AAG/D,QAAM,YAAY;AAAA,IAChB,MACE,0BAA0B;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,QAAQ;AAAA,MAClB,gBAAgB,CAAC,kBAA0B;AACzC,mBAAW,qBAAmB,gBAAgB,IAAI,OAAM,EAAE,OAAO,cAAc,KAAK,gBAAgB,CAAE,CAAC;AACvG,yBAAiB,aAAa;AAAA,MAChC;AAAA,MACA,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB,CAAC;AAAA,IACH,CAAC,OAAO,aAAa,UAAU,gBAAgB,QAAQ,QAAQ;AAAA,EACjE;AACA,QAAM,CAAC,SAAS,UAAU,IAAI,SAAmB,UAAU,OAAO;AAGlE,QAAM,MAAM,YAAY,YAAY;AAElC,QAAI,CAAC,cAAc,CAAC,YAAY,CAAC,WAAW;AAC1C,eAAS,8DAA8D;AACvE;AAAA,IACF;AAGA,aAAS,IAAI;AACb,eAAW,IAAI;AACf,iBAAa,IAAI;AAEjB,QAAI;AACF,YAAM,cAAc,MAAM,UAAU,IAAI,EAAE,YAAY,UAAU,UAAU,CAAC;AAC3E,iBAAW,WAAW;AAAA,IACxB,SAAS,KAAK;AACZ,eAAS,eAAe,QAAQ,IAAI,UAAU,2BAA2B;AAAA,IAC3E,UAAE;AACA,mBAAa,KAAK;AAAA,IACpB;AAAA,EACF,GAAG,CAAC,WAAW,YAAY,UAAU,SAAS,CAAC;AAG/C,QAAM,QAAQ,YAAY,MAAM;AAC9B,cAAU,MAAM;AAChB,gBAAY,CAAC;AACb,eAAW,IAAI;AACf,aAAS,IAAI;AACb,iBAAa,KAAK;AAClB,mBAAe,CAAC,CAAC;AAAA,EACnB,GAAG,CAAC,SAAS,CAAC;AAEd,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}