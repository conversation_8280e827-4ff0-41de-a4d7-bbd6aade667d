import { stat } from "node:fs/promises";
import { dirname, extname, join, relative } from "node:path";
import { globby } from "globby";
const DEFAULT_IGNORE = [
  "**/node_modules/**",
  "**/.git/**",
  "**/dist/**",
  "**/build/**",
  "**/coverage/**",
  "**/.next/**",
  "**/.nuxt/**",
  "**/venv/**",
  "**/__pycache__/**",
  "**/*.min.js",
  "**/*.min.css",
  "**/vendor/**",
  "**/.cache/**",
  "**/tmp/**",
  "**/temp/**"
];
const explorer = async (options) => {
  const ignore = [...DEFAULT_IGNORE, ...options.ignore ?? []];
  const pattern = join(options.directory, "**/*");
  const allFiles = await globby(pattern, {
    ignore,
    gitignore: true,
    absolute: true,
    onlyFiles: true
  });
  const filesByExtension = /* @__PURE__ */ new Map();
  for (const file of allFiles) {
    const { size } = await stat(file);
    if (size > 512 * 1024) continue;
    const ext = extname(file).slice(1).toLowerCase();
    if (!ext) continue;
    if (!filesByExtension.has(ext)) {
      filesByExtension.set(ext, []);
    }
    filesByExtension.get(ext).push(file);
  }
  const result = /* @__PURE__ */ new Map();
  for (const [extension, files] of filesByExtension) {
    const extensionFileGroups = [];
    const filesByDirectory = /* @__PURE__ */ new Map();
    for (const file of files) {
      const directory = dirname(file);
      if (!filesByDirectory.has(directory)) {
        filesByDirectory.set(directory, []);
      }
      filesByDirectory.get(directory)?.push(file);
    }
    for (const [directory, dirFiles] of filesByDirectory) {
      extensionFileGroups.push({
        directory,
        language: extension,
        // The extension IS the language
        files: Object.freeze(dirFiles)
      });
    }
    result.set(extension, extensionFileGroups);
  }
  return toposort(result);
};
const getDirectoriesForProcessing = (fileGroupsMap) => {
  const allDirectories = /* @__PURE__ */ new Set();
  for (const fileGroups of fileGroupsMap.values()) {
    for (const group of fileGroups) {
      allDirectories.add(group.directory);
    }
  }
  return Array.from(allDirectories).sort((a, b) => {
    const depthA = a.split("/").length;
    const depthB = b.split("/").length;
    if (depthA !== depthB) return depthA - depthB;
    return a.localeCompare(b);
  });
};
const getFilesForDirectory = (fileGroupsMap, directory) => {
  const result = /* @__PURE__ */ new Map();
  for (const [language, fileGroups] of fileGroupsMap) {
    for (const group of fileGroups) {
      const filesInDir = group.files.filter((file) => {
        const fileDir = dirname(file);
        const relativeDir = relative(process.cwd(), fileDir) || ".";
        return relativeDir === directory;
      });
      if (filesInDir.length > 0) {
        result.set(language, filesInDir);
      }
    }
  }
  return result;
};
const getSupportedLanguages = () => {
  return [];
};
const isChildDirectory = (child, parent) => {
  if (child === parent) return false;
  const childParts = child.split("/");
  const parentParts = parent.split("/");
  if (childParts.length <= parentParts.length) return false;
  for (let i = 0; i < parentParts.length; i++) {
    if (childParts[i] !== parentParts[i]) return false;
  }
  return true;
};
const toposort = (fileGroups) => {
  const result = /* @__PURE__ */ new Map();
  for (const [language, groups] of fileGroups) {
    if (groups.length === 0) {
      result.set(language, []);
      continue;
    }
    const adjacency = /* @__PURE__ */ new Map();
    const inDegree = /* @__PURE__ */ new Map();
    const directories = groups.map((g) => g.directory);
    for (const dir of directories) {
      adjacency.set(dir, /* @__PURE__ */ new Set());
      inDegree.set(dir, 0);
    }
    for (const parent of directories) {
      for (const child of directories) {
        if (isChildDirectory(child, parent)) {
          const isDirectChild = !directories.some(
            (d) => d !== child && d !== parent && isChildDirectory(child, d) && isChildDirectory(d, parent)
          );
          if (isDirectChild) {
            adjacency.get(parent)?.add(child);
            inDegree.set(child, inDegree.get(child) + 1);
          }
        }
      }
    }
    const visited = /* @__PURE__ */ new Set();
    const sorted = [];
    const dfs = (dir) => {
      if (visited.has(dir)) return;
      visited.add(dir);
      const children = Array.from(adjacency.get(dir)).sort();
      for (const child of children) {
        dfs(child);
      }
      sorted.push(dir);
    };
    const roots = directories.filter((d) => {
      return !directories.some((other) => other !== d && isChildDirectory(d, other));
    }).sort();
    for (const root of roots) {
      dfs(root);
    }
    const sortedGroups = sorted.map((dir) => groups.find((g) => g.directory === dir)).filter(Boolean);
    result.set(language, sortedGroups);
  }
  return result;
};
export {
  explorer,
  getDirectoriesForProcessing,
  getFilesForDirectory,
  getSupportedLanguages,
  toposort
};
//# sourceMappingURL=explorer.js.map
