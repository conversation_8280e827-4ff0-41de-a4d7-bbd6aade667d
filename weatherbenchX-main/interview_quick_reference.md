# WeatherBench-X Interview Quick Reference

*30-minute coding interview preparation - key patterns and concepts*

## 🎯 Most Likely Interview Topics

### 1. **Aggregation Pattern** (High Probability)
**Question**: "Implement a system to compute weighted regional averages of weather data"

**Key Pattern**:
```python
# AggregationState: The core abstraction
@dataclasses.dataclass
class AggregationState:
    sum_weighted_statistics: Dict  # Σ(weight × value)
    sum_weights: Dict              # Σ(weight)
    
    def __add__(self, other):
        # Combine states from different chunks
        return AggregationState(
            sum_weighted_statistics=combine_dicts(self.sum_weighted_statistics, other.sum_weighted_statistics),
            sum_weights=combine_dicts(self.sum_weights, other.sum_weights)
        )
    
    def mean_statistics(self):
        # Final step: weighted_sum / weight_sum
        return {k: self.sum_weighted_statistics[k] / self.sum_weights[k] 
                for k in self.sum_weighted_statistics}

# Usage pattern
aggregator = Aggregator(
    reduce_dims=['time', 'lat', 'lon'],
    bin_by=[Regions({'global': ((-90, 90), (0, 360))})],
    weigh_by=[GridAreaWeighting()]
)
```

**Why This Matters**: Distributed processing requires combinable intermediate states

### 2. **Binning System** (High Probability)
**Question**: "Create a system to group weather data by regions and time periods"

**Key Pattern**:
```python
class Binning(ABC):
    def create_bin_mask(self, statistic: xr.DataArray) -> xr.DataArray:
        """Return boolean mask with new bin dimension"""
        pass

# Regional binning
class Regions(Binning):
    def create_bin_mask(self, statistic):
        masks = []
        for region_name, ((lat_min, lat_max), (lon_min, lon_max)) in self.regions.items():
            lat_mask = (statistic.lat >= lat_min) & (statistic.lat <= lat_max)
            lon_mask = (statistic.lon >= lon_min) & (statistic.lon <= lon_max)
            masks.append(lat_mask & lon_mask)
        
        return xr.concat(masks, dim='region')

# Time binning
class ByTimeUnit(Binning):
    def create_bin_mask(self, statistic):
        if self.unit == 'season':
            seasons = statistic.time.dt.season
            masks = [seasons == s for s in ['DJF', 'MAM', 'JJA', 'SON']]
            return xr.concat(masks, dim='season')
```

**Key Insight**: Masks are multiplied together, so you can combine region × season × hour-of-day

### 3. **XArray Operations** (Very High Probability)
**Question**: "Process multi-dimensional weather data efficiently"

**Essential Patterns**:
```python
# 1. Coordinate-based selection (not positional indexing!)
europe = data.sel(lat=slice(35, 70), lon=slice(-10, 40))
winter = data.sel(time=data.time.dt.season == 'DJF')

# 2. Dimension-aware operations
global_mean = data.mean(dim=['lat', 'lon'])  # Time series
climatology = data.groupby('time.month').mean()  # Monthly climatology

# 3. Broadcasting and alignment
anomaly = data - climatology  # Automatically broadcasts

# 4. The magic: xr.dot for weighted reductions
weighted_mean = xr.dot(data, weights, dims=['lat', 'lon'])

# 5. Interpolation for grid-to-point alignment
interpolated = data.interp(lat=station_lats, lon=station_lons, method='linear')
```

### 4. **Grid Area Weighting** (Medium Probability)
**Question**: "Compute proper global averages accounting for grid cell area"

**Key Pattern**:
```python
class GridAreaWeighting:
    def weights(self, statistic: xr.DataArray) -> xr.DataArray:
        if 'lat' not in statistic.dims:
            return xr.DataArray(1.0)
        
        lat = statistic.lat
        # Area proportional to cos(latitude)
        weights = np.cos(np.deg2rad(lat))
        # Normalize to mean of 1
        weights = weights / weights.mean()
        return weights

# Usage in aggregation
global_mean = xr.dot(temperature, area_weights, dims=['lat', 'lon'])
```

**Why This Matters**: Equal-area grids need proper weighting for global statistics

### 5. **Interpolation** (Medium Probability)
**Question**: "Align gridded forecasts with point observations"

**Key Pattern**:
```python
class InterpolateToReferenceCoords:
    def interpolate(self, data, reference):
        # Extract coordinates from reference (observations)
        target_coords = {
            'lat': reference.lat,
            'lon': reference.lon
        }
        
        # Interpolate gridded data to observation points
        return data.interp(**target_coords, method='linear')

# Handle longitude wrapping
def pad_longitude(data):
    """Add ±360° copies for interpolation across dateline"""
    left = data.isel(lon=slice(-1, None)) + 360
    right = data.isel(lon=slice(0, 1)) - 360
    return xr.concat([left, data, right], dim='lon')
```

## 🚀 Interview Success Patterns

### **1. Always Think in Terms of Labeled Dimensions**
```python
# ❌ Bad: Positional indexing
data[0, :, 10:20]

# ✅ Good: Coordinate-based selection
data.sel(time='2024-01-01', lat=slice(30, 60))
```

### **2. Design for Distributed Processing**
```python
# ❌ Bad: Monolithic processing
def compute_global_mean(data):
    return data.mean()

# ✅ Good: Combinable intermediate states
def compute_weighted_sums(data, weights):
    return {
        'weighted_sum': (data * weights).sum(),
        'weight_sum': weights.sum()
    }

def combine_sums(sums_list):
    total_weighted = sum(s['weighted_sum'] for s in sums_list)
    total_weights = sum(s['weight_sum'] for s in sums_list)
    return total_weighted / total_weights
```

### **3. Handle Missing Data Gracefully**
```python
# Use skipna parameter consistently
aggregator = Aggregator(reduce_dims=['time'], skipna=True)

# Check for mask coordinates
if hasattr(statistic, 'mask'):
    statistic = statistic.where(statistic.mask)
```

### **4. Compose Complex Operations**
```python
# Build complex aggregations from simple components
aggregator = Aggregator(
    reduce_dims=['time', 'lat', 'lon'],
    bin_by=[
        Regions(regions),           # Spatial binning
        ByTimeUnit('time', 'season') # Temporal binning
    ],
    weigh_by=[GridAreaWeighting()]  # Proper weighting
)
```

## 🎯 Common Interview Questions

### **Q1**: "How would you compute seasonal averages by region?"
**A**: Use `ByTimeUnit('time', 'season')` + `Regions(region_dict)` binning

### **Q2**: "How do you handle the fact that grid cells near poles are smaller?"
**A**: Use `GridAreaWeighting` with `cos(latitude)` weighting

### **Q3**: "How would you align gridded forecasts with weather station data?"
**A**: Use `InterpolateToReferenceCoords` with station coordinates

### **Q4**: "How do you process data too large for memory?"
**A**: Use chunking + `AggregationState` pattern for combinable intermediate results

### **Q5**: "How would you add a new evaluation metric?"
**A**: Inherit from `Metric`, implement `wanted_statistics()` and `evaluate_from_statistics()`

## 🔑 Key Abstractions to Remember

1. **AggregationState**: Combinable weighted statistics
2. **Binning**: Boolean masks with new dimensions
3. **Weighting**: Proper statistical weighting (area, etc.)
4. **xr.dot**: The workhorse for weighted reductions
5. **Coordinate-based operations**: Always use labels, not indices

## 💡 Pro Tips

- **Start simple**: Get basic functionality working first
- **Think distributed**: Design for chunk-based processing
- **Use xarray idioms**: Leverage coordinate-based operations
- **Handle edge cases**: Missing data, coordinate wrapping, etc.
- **Compose solutions**: Build complex operations from simple parts

This is the level of sophistication Gridmatic expects - production-ready scientific computing patterns! 🚀
