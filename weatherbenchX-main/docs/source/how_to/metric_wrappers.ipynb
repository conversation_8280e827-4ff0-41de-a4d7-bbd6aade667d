{"cells": [{"cell_type": "markdown", "id": "eb126990-a8ed-4105-8cfc-ce1315b50a63", "metadata": {}, "source": ["# Wrap metrics/compute binary metrics\n", "\n", "If we want to compute binary metrics like the CSI (Critical Success Index) from real valued forecasts, these need to be thresholded first. For this, we wrap the metrics with input transforms since the metrics/statistics expect the data to be in binary format already.\n", "\n", "Let's take an example of an ensemble forecast that we want to compute the CSI for. Doing this requires several transforms on the prediction and target data.\n", "\n", "The continuous, real-valued forecasts need to be converted to binary forecasts based on a threshold value. (In this case for total_precipitation).\n", "Then the binary ensembles have to be averaged to produce a probability forecast for each of the thresholds.\n", "Finally, the probability forecasts have to be thresholded by probability values to produce a binary output that we can compute the CSI for.\n", "Let's load the data and apply all the wrappers around the CSI metric."]}, {"cell_type": "code", "execution_count": 1, "id": "93aa5796-4a7b-4d00-aa0b-8d622a519800", "metadata": {}, "outputs": [], "source": ["# IMPORTANT: If you are running this on Colab, uncomment the cell below to access the cloud datasets.\n", "# from google.colab import auth\n", "# auth.authenticate_user()"]}, {"cell_type": "code", "execution_count": 2, "id": "8d44db48-6aba-4e47-bc50-93ffda308999", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from weatherbenchX import aggregation\n", "from weatherbenchX.data_loaders import xarray_loaders\n", "from weatherbenchX.metrics import categorical\n", "from weatherbenchX.metrics import wrappers"]}, {"cell_type": "code", "execution_count": 3, "id": "34efab58-2aa4-4059-a42b-e1ece6a986a1", "metadata": {}, "outputs": [], "source": ["prediction_path = 'gs://weatherbench2/datasets/ifs_ens/2018-2022-64x32_equiangular_conservative.zarr'\n", "target_path = 'gs://weatherbench2/datasets/era5/1959-2022-6h-64x32_equiangular_conservative.zarr'"]}, {"cell_type": "code", "execution_count": 4, "id": "616d1774-da46-4af7-ac40-a4bc44389f0f", "metadata": {}, "outputs": [], "source": ["variables = ['total_precipitation_6hr']\n", "target_data_loader = xarray_loaders.TargetsFromXarray(\n", "    path=target_path,\n", "    variables=variables,\n", ")\n", "prediction_data_loader = xarray_loaders.PredictionsFromXarray(\n", "    path=prediction_path,\n", "    variables=variables,\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "71749d56-92ff-4fc8-a2d3-a98b0164615c", "metadata": {}, "outputs": [], "source": ["init_times = np.array(['2020-01-01T00'], dtype='datetime64[ns]')\n", "lead_times = np.array([6], dtype='timedelta64[h]').astype('timedelta64[ns]')   # To silence xr warnings."]}, {"cell_type": "code", "execution_count": 6, "id": "801c0f32-21b6-4483-85bd-f88b380f83de", "metadata": {}, "outputs": [], "source": ["target_chunk = target_data_loader.load_chunk(init_times, lead_times)\n", "prediction_chunk = prediction_data_loader.load_chunk(init_times, lead_times)"]}, {"cell_type": "code", "execution_count": 7, "id": "e2ab658a-5862-4e7a-a335-4047b484bdeb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 9kB\n", "Dimensions:                  (latitude: 32, longitude: 64, init_time: 1,\n", "                              lead_time: 1)\n", "Coordinates:\n", "  * latitude                 (latitude) float64 256B -87.19 -81.56 ... 87.19\n", "  * longitude                (longitude) float64 512B 0.0 5.625 ... 348.8 354.4\n", "    valid_time               (init_time, lead_time) datetime64[ns] 8B 2020-01...\n", "  * init_time                (init_time) datetime64[ns] 8B 2020-01-01\n", "  * lead_time                (lead_time) timedelta64[ns] 8B 06:00:00\n", "Data variables:\n", "    total_precipitation_6hr  (init_time, lead_time, longitude, latitude) float32 8kB ...\n", "Attributes:\n", "    long_name:   Total precipitation\n", "    short_name:  tp\n", "    units:       m</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-e6638f0a-dee1-4888-80d7-6542617f93c2' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-e6638f0a-dee1-4888-80d7-6542617f93c2' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>latitude</span>: 32</li><li><span class='xr-has-index'>longitude</span>: 64</li><li><span class='xr-has-index'>init_time</span>: 1</li><li><span class='xr-has-index'>lead_time</span>: 1</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-785e5583-afb3-43e7-9e6a-f10d42049dd5' class='xr-section-summary-in' type='checkbox'  checked><label for='section-785e5583-afb3-43e7-9e6a-f10d42049dd5' class='xr-section-summary' >Coordinates: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>latitude</span></div><div class='xr-var-dims'>(latitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>-87.19 -81.56 ... 81.56 87.19</div><input id='attrs-43d678f1-64d3-4a9d-8082-65818e8868ff' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-43d678f1-64d3-4a9d-8082-65818e8868ff' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-2018f404-8383-4c34-b980-695af1e29c26' class='xr-var-data-in' type='checkbox'><label for='data-2018f404-8383-4c34-b980-695af1e29c26' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([-87.1875, -81.5625, -75.9375, -70.3125, -64.6875, -59.0625, -53.4375,\n", "       -47.8125, -42.1875, -36.5625, -30.9375, -25.3125, -19.6875, -14.0625,\n", "        -8.4375,  -2.8125,   2.8125,   8.4375,  14.0625,  19.6875,  25.3125,\n", "        30.9375,  36.5625,  42.1875,  47.8125,  53.4375,  59.0625,  64.6875,\n", "        70.3125,  75.9375,  81.5625,  87.1875])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>longitude</span></div><div class='xr-var-dims'>(longitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.0 5.625 11.25 ... 348.8 354.4</div><input id='attrs-9eee8ce1-8d74-499e-b5f2-e4a293e06276' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-9eee8ce1-8d74-499e-b5f2-e4a293e06276' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ef9fd601-37f3-49d7-b85e-9792f6614de6' class='xr-var-data-in' type='checkbox'><label for='data-ef9fd601-37f3-49d7-b85e-9792f6614de6' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  0.   ,   5.625,  11.25 ,  16.875,  22.5  ,  28.125,  33.75 ,  39.375,\n", "        45.   ,  50.625,  56.25 ,  61.875,  67.5  ,  73.125,  78.75 ,  84.375,\n", "        90.   ,  95.625, 101.25 , 106.875, 112.5  , 118.125, 123.75 , 129.375,\n", "       135.   , 140.625, 146.25 , 151.875, 157.5  , 163.125, 168.75 , 174.375,\n", "       180.   , 185.625, 191.25 , 196.875, 202.5  , 208.125, 213.75 , 219.375,\n", "       225.   , 230.625, 236.25 , 241.875, 247.5  , 253.125, 258.75 , 264.375,\n", "       270.   , 275.625, 281.25 , 286.875, 292.5  , 298.125, 303.75 , 309.375,\n", "       315.   , 320.625, 326.25 , 331.875, 337.5  , 343.125, 348.75 , 354.375])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>valid_time</span></div><div class='xr-var-dims'>(init_time, lead_time)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2020-01-01T06:00:00</div><input id='attrs-7dc8f999-0d53-48c3-bf64-ab0891096c4d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-7dc8f999-0d53-48c3-bf64-ab0891096c4d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-76d4fc8b-16fe-4868-905a-a58aae7e8d9e' class='xr-var-data-in' type='checkbox'><label for='data-76d4fc8b-16fe-4868-905a-a58aae7e8d9e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[&#x27;2020-01-01T06:00:00.000000000&#x27;]], dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>init_time</span></div><div class='xr-var-dims'>(init_time)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2020-01-01</div><input id='attrs-1ab84424-4ad6-4a00-b35e-f9f8e6b02fce' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-1ab84424-4ad6-4a00-b35e-f9f8e6b02fce' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-4858b0b3-b800-49df-bd8f-547b0caf9519' class='xr-var-data-in' type='checkbox'><label for='data-4858b0b3-b800-49df-bd8f-547b0caf9519' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2020-01-01T00:00:00.000000000&#x27;], dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>lead_time</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>timedelta64[ns]</div><div class='xr-var-preview xr-preview'>06:00:00</div><input id='attrs-08fc8874-cd06-4f4a-9161-46ecbf51e17c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-08fc8874-cd06-4f4a-9161-46ecbf51e17c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c65ac858-319d-4f14-ad99-6cef614117b2' class='xr-var-data-in' type='checkbox'><label for='data-c65ac858-319d-4f14-ad99-6cef614117b2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([21600000000000], dtype=&#x27;timedelta64[ns]&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-33d75063-fd2f-4f25-ad05-2151438276b8' class='xr-section-summary-in' type='checkbox'  checked><label for='section-33d75063-fd2f-4f25-ad05-2151438276b8' class='xr-section-summary' >Data variables: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>total_precipitation_6hr</span></div><div class='xr-var-dims'>(init_time, lead_time, longitude, latitude)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>1.94e-05 4.548e-05 ... 3.991e-05</div><input id='attrs-6cb2d27f-d6ae-4766-9e50-ff16795f383f' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-6cb2d27f-d6ae-4766-9e50-ff16795f383f' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-582a3014-742b-4380-917f-ed00f7abcfe5' class='xr-var-data-in' type='checkbox'><label for='data-582a3014-742b-4380-917f-ed00f7abcfe5' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>Total precipitation</dd><dt><span>short_name :</span></dt><dd>tp</dd><dt><span>units :</span></dt><dd>m</dd></dl></div><div class='xr-var-data'><pre>array([[[[1.9404670e-05, 4.5482229e-05, 2.3316835e-07, ...,\n", "          2.0144197e-04, 3.6242406e-04, 3.3432458e-05],\n", "         [2.4854797e-05, 5.5991608e-05, 1.5337461e-06, ...,\n", "          2.7990891e-04, 4.8377854e-04, 1.8141285e-05],\n", "         [2.8967837e-05, 4.9802191e-05, 6.0337362e-07, ...,\n", "          5.3046335e-04, 6.2247615e-05, 1.4186974e-05],\n", "         ...,\n", "         [3.6241029e-06, 4.5901288e-05, 2.4773467e-06, ...,\n", "          2.0171332e-04, 2.2350882e-04, 3.0808969e-05],\n", "         [4.5650258e-06, 2.8388049e-05, 2.6042777e-08, ...,\n", "          8.1270112e-04, 3.5089831e-04, 3.8137332e-05],\n", "         [1.1517460e-05, 2.7077394e-05, 1.2770050e-07, ...,\n", "          9.2705846e-04, 5.2468083e-04, 3.9910377e-05]]]], dtype=float32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-36fe40c0-1c23-4987-b229-c72f2f119cce' class='xr-section-summary-in' type='checkbox'  ><label for='section-36fe40c0-1c23-4987-b229-c72f2f119cce' class='xr-section-summary' >Indexes: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>latitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-9294c6fd-79a1-4c9c-a4ae-bbfe08ccf537' class='xr-index-data-in' type='checkbox'/><label for='index-9294c6fd-79a1-4c9c-a4ae-bbfe08ccf537' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([ -87.18750000000003,  -81.56250000000001,            -75.9375,\n", "        -70.31249999999999,  -64.68750000000001,            -59.0625,\n", "                  -53.4375,            -47.8125,            -42.1875,\n", "                  -36.5625, -30.937499999999996, -25.312500000000004,\n", "       -19.687499999999996, -14.062499999999991,  -8.437499999999996,\n", "        -2.812500000000003,   2.812500000000003,   8.437500000000009,\n", "        14.062500000000004,  19.687499999999996,  25.312500000000004,\n", "         30.93750000000001,  36.562499999999986,             42.1875,\n", "                   47.8125,             53.4375,  59.062500000000014,\n", "         64.68750000000001,             70.3125,             75.9375,\n", "         81.56249999999997,   87.18750000000003],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;latitude&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>longitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-9bcf91e6-0bd1-470c-bd12-32ebc6f0fb80' class='xr-index-data-in' type='checkbox'/><label for='index-9bcf91e6-0bd1-470c-bd12-32ebc6f0fb80' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([               0.0,              5.625,              11.25,\n", "                   16.875,               22.5,             28.125,\n", "                    33.75,             39.375,               45.0,\n", "                   50.625,              56.25,  61.87499999999999,\n", "                     67.5,             73.125,              78.75,\n", "                   84.375,               90.0,             95.625,\n", "                   101.25,            106.875,              112.5,\n", "                  118.125, 123.74999999999999,            129.375,\n", "                    135.0,            140.625,             146.25,\n", "                  151.875,              157.5,            163.125,\n", "                   168.75,            174.375,              180.0,\n", "                  185.625,             191.25,            196.875,\n", "                    202.5,            208.125,             213.75,\n", "                  219.375,              225.0, 230.62499999999997,\n", "                   236.25,            241.875, 247.49999999999997,\n", "                  253.125,             258.75,            264.375,\n", "                    270.0,            275.625,             281.25,\n", "                  286.875,              292.5,            298.125,\n", "                   303.75,            309.375,              315.0,\n", "                  320.625,             326.25,            331.875,\n", "                    337.5,            343.125,             348.75,\n", "                  354.375],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;longitude&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>init_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-cb9e9650-f307-4c76-aa76-6f036fdce6dc' class='xr-index-data-in' type='checkbox'/><label for='index-cb9e9650-f307-4c76-aa76-6f036fdce6dc' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2020-01-01&#x27;], dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;init_time&#x27;, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>lead_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-1e8a4c2c-cb43-4052-81ce-42fa3c2c95e5' class='xr-index-data-in' type='checkbox'/><label for='index-1e8a4c2c-cb43-4052-81ce-42fa3c2c95e5' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(TimedeltaIndex([&#x27;0 days 06:00:00&#x27;], dtype=&#x27;timedelta64[ns]&#x27;, name=&#x27;lead_time&#x27;, freq=None))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-75dbd057-69d6-4dde-b434-9bf1cc049181' class='xr-section-summary-in' type='checkbox'  checked><label for='section-75dbd057-69d6-4dde-b434-9bf1cc049181' class='xr-section-summary' >Attributes: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>Total precipitation</dd><dt><span>short_name :</span></dt><dd>tp</dd><dt><span>units :</span></dt><dd>m</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 9kB\n", "Dimensions:                  (latitude: 32, longitude: 64, init_time: 1,\n", "                              lead_time: 1)\n", "Coordinates:\n", "  * latitude                 (latitude) float64 256B -87.19 -81.56 ... 87.19\n", "  * longitude                (longitude) float64 512B 0.0 5.625 ... 348.8 354.4\n", "    valid_time               (init_time, lead_time) datetime64[ns] 8B 2020-01...\n", "  * init_time                (init_time) datetime64[ns] 8B 2020-01-01\n", "  * lead_time                (lead_time) timedelta64[ns] 8B 06:00:00\n", "Data variables:\n", "    total_precipitation_6hr  (init_time, lead_time, longitude, latitude) float32 8kB ...\n", "Attributes:\n", "    long_name:   Total precipitation\n", "    short_name:  tp\n", "    units:       m"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["target_chunk"]}, {"cell_type": "code", "execution_count": 8, "id": "a0c0ce4f-16e6-499a-b668-d8c385a792c1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 411kB\n", "Dimensions:                  (latitude: 32, longitude: 64, number: 50,\n", "                              lead_time: 1, init_time: 1)\n", "Coordinates:\n", "  * latitude                 (latitude) float64 256B -87.19 -81.56 ... 87.19\n", "  * longitude                (longitude) float64 512B 0.0 5.625 ... 348.8 354.4\n", "  * number                   (number) int32 200B 1 2 3 4 5 6 ... 46 47 48 49 50\n", "  * lead_time                (lead_time) timedelta64[ns] 8B 06:00:00\n", "  * init_time                (init_time) datetime64[ns] 8B 2020-01-01\n", "Data variables:\n", "    total_precipitation_6hr  (init_time, number, lead_time, longitude, latitude) float32 410kB ...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-2e814bb0-e01f-4698-9eb7-ca5411dfe6aa' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-2e814bb0-e01f-4698-9eb7-ca5411dfe6aa' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>latitude</span>: 32</li><li><span class='xr-has-index'>longitude</span>: 64</li><li><span class='xr-has-index'>number</span>: 50</li><li><span class='xr-has-index'>lead_time</span>: 1</li><li><span class='xr-has-index'>init_time</span>: 1</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-e12e7de0-1e8b-4fc8-8f39-5f65e1b1cbfc' class='xr-section-summary-in' type='checkbox'  checked><label for='section-e12e7de0-1e8b-4fc8-8f39-5f65e1b1cbfc' class='xr-section-summary' >Coordinates: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>latitude</span></div><div class='xr-var-dims'>(latitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>-87.19 -81.56 ... 81.56 87.19</div><input id='attrs-4358b4e7-83f6-4ff8-a552-8515707075c3' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-4358b4e7-83f6-4ff8-a552-8515707075c3' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-54e68d3c-3080-4449-ac15-c379a3b9ebd0' class='xr-var-data-in' type='checkbox'><label for='data-54e68d3c-3080-4449-ac15-c379a3b9ebd0' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([-87.1875, -81.5625, -75.9375, -70.3125, -64.6875, -59.0625, -53.4375,\n", "       -47.8125, -42.1875, -36.5625, -30.9375, -25.3125, -19.6875, -14.0625,\n", "        -8.4375,  -2.8125,   2.8125,   8.4375,  14.0625,  19.6875,  25.3125,\n", "        30.9375,  36.5625,  42.1875,  47.8125,  53.4375,  59.0625,  64.6875,\n", "        70.3125,  75.9375,  81.5625,  87.1875])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>longitude</span></div><div class='xr-var-dims'>(longitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.0 5.625 11.25 ... 348.8 354.4</div><input id='attrs-ef723931-ef6f-447a-91ad-b6b9504bbcc7' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ef723931-ef6f-447a-91ad-b6b9504bbcc7' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-dbfb8354-1446-4fa1-942a-555a7a597cd8' class='xr-var-data-in' type='checkbox'><label for='data-dbfb8354-1446-4fa1-942a-555a7a597cd8' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  0.   ,   5.625,  11.25 ,  16.875,  22.5  ,  28.125,  33.75 ,  39.375,\n", "        45.   ,  50.625,  56.25 ,  61.875,  67.5  ,  73.125,  78.75 ,  84.375,\n", "        90.   ,  95.625, 101.25 , 106.875, 112.5  , 118.125, 123.75 , 129.375,\n", "       135.   , 140.625, 146.25 , 151.875, 157.5  , 163.125, 168.75 , 174.375,\n", "       180.   , 185.625, 191.25 , 196.875, 202.5  , 208.125, 213.75 , 219.375,\n", "       225.   , 230.625, 236.25 , 241.875, 247.5  , 253.125, 258.75 , 264.375,\n", "       270.   , 275.625, 281.25 , 286.875, 292.5  , 298.125, 303.75 , 309.375,\n", "       315.   , 320.625, 326.25 , 331.875, 337.5  , 343.125, 348.75 , 354.375])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>number</span></div><div class='xr-var-dims'>(number)</div><div class='xr-var-dtype'>int32</div><div class='xr-var-preview xr-preview'>1 2 3 4 5 6 7 ... 45 46 47 48 49 50</div><input id='attrs-ad9735fd-84fc-4032-add6-e4ca47d7bfe3' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ad9735fd-84fc-4032-add6-e4ca47d7bfe3' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-3a974f88-ad76-4847-a6d9-6789caf6a625' class='xr-var-data-in' type='checkbox'><label for='data-3a974f88-ad76-4847-a6d9-6789caf6a625' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18,\n", "       19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36,\n", "       37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50], dtype=int32)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>lead_time</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>timedelta64[ns]</div><div class='xr-var-preview xr-preview'>06:00:00</div><input id='attrs-d2d4041a-ce35-44c1-8cee-5853f5bb4a03' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-d2d4041a-ce35-44c1-8cee-5853f5bb4a03' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b5b94057-7a2f-4112-b258-d1deb52b838d' class='xr-var-data-in' type='checkbox'><label for='data-b5b94057-7a2f-4112-b258-d1deb52b838d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>time since forecast_reference_time</dd><dt><span>standard_name :</span></dt><dd>forecast_period</dd></dl></div><div class='xr-var-data'><pre>array([21600000000000], dtype=&#x27;timedelta64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>init_time</span></div><div class='xr-var-dims'>(init_time)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2020-01-01</div><input id='attrs-1b1a098b-8acc-409e-96c9-f11febf5c295' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-1b1a098b-8acc-409e-96c9-f11febf5c295' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-bc3de023-022d-43a0-8a43-4125200e7997' class='xr-var-data-in' type='checkbox'><label for='data-bc3de023-022d-43a0-8a43-4125200e7997' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>initial time of forecast</dd><dt><span>standard_name :</span></dt><dd>forecast_reference_time</dd></dl></div><div class='xr-var-data'><pre>array([&#x27;2020-01-01T00:00:00.000000000&#x27;], dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-d994aab8-9c47-4ebd-b20d-f212c3178289' class='xr-section-summary-in' type='checkbox'  checked><label for='section-d994aab8-9c47-4ebd-b20d-f212c3178289' class='xr-section-summary' >Data variables: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>total_precipitation_6hr</span></div><div class='xr-var-dims'>(init_time, number, lead_time, longitude, latitude)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>9.967e-06 6.555e-05 ... 1.811e-05</div><input id='attrs-9264d66a-ac2e-4610-9e7d-411eb3d2fb31' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-9264d66a-ac2e-4610-9e7d-411eb3d2fb31' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c7671bfb-3512-4999-8bdc-bdb4837076d1' class='xr-var-data-in' type='checkbox'><label for='data-c7671bfb-3512-4999-8bdc-bdb4837076d1' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[[[[9.96709150e-06, 6.55538606e-05, 4.47257776e-07, ...,\n", "           2.27000273e-04, 3.52770119e-04, 2.26976517e-05],\n", "          [9.53860126e-06, 7.07851650e-05, 1.35051744e-06, ...,\n", "           1.51374130e-04, 4.74148546e-04, 4.95153517e-06],\n", "          [2.33770170e-05, 7.35553476e-05, 1.13228475e-06, ...,\n", "           7.30747124e-04, 1.56360242e-04, 2.43417662e-06],\n", "          ...,\n", "          [1.35492814e-06, 4.20554788e-05, 2.36490359e-05, ...,\n", "           3.99051874e-04, 1.89910570e-04, 1.34265065e-05],\n", "          [1.01508340e-06, 3.18184102e-05, 8.45199182e-08, ...,\n", "           8.89981922e-04, 2.56143423e-04, 1.00150110e-05],\n", "          [9.57642987e-06, 5.82341636e-05, 5.30144568e-08, ...,\n", "           1.39360223e-03, 5.79540851e-04, 2.59249136e-05]]],\n", "\n", "\n", "        [[[1.33007861e-05, 5.12972838e-05, 1.52672328e-07, ...,\n", "           1.67506107e-04, 9.07307607e-04, 2.48582110e-05],\n", "          [1.08797731e-05, 6.77318822e-05, 9.39133031e-07, ...,\n", "           3.44350468e-04, 4.61967022e-04, 1.01469750e-05],\n", "          [1.89388920e-05, 8.93924589e-05, 8.28401710e-07, ...,\n", "...\n", "          [2.22253811e-06, 2.57642860e-05, 4.35225644e-07, ...,\n", "           1.16715988e-03, 2.55341729e-04, 2.17801808e-05],\n", "          [1.02742124e-05, 5.95605343e-05, 0.00000000e+00, ...,\n", "           1.28140615e-03, 5.31625934e-04, 2.24593678e-05]]],\n", "\n", "\n", "        [[[1.03631955e-05, 5.59558066e-05, 3.36878372e-07, ...,\n", "           1.60535506e-04, 7.59830233e-04, 1.76252634e-05],\n", "          [9.83294012e-06, 7.27320221e-05, 1.23354960e-06, ...,\n", "           2.22191971e-04, 3.59319733e-04, 6.10258485e-06],\n", "          [2.33785868e-05, 8.05652016e-05, 7.37057917e-07, ...,\n", "           8.43977788e-04, 9.21552928e-05, 5.02498960e-06],\n", "          ...,\n", "          [5.78411175e-07, 2.01204457e-05, 1.96093770e-05, ...,\n", "           2.38810739e-04, 1.49757514e-04, 1.59175033e-05],\n", "          [3.16670992e-07, 2.75888251e-05, 1.10387653e-07, ...,\n", "           5.02671697e-04, 1.53276866e-04, 1.03345710e-05],\n", "          [7.35140065e-06, 4.37220951e-05, 3.24024484e-07, ...,\n", "           1.18734338e-03, 3.24120017e-04, 1.81081668e-05]]]]],\n", "      dtype=float32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-a8965666-5208-4459-8229-cae8c53d9bd3' class='xr-section-summary-in' type='checkbox'  ><label for='section-a8965666-5208-4459-8229-cae8c53d9bd3' class='xr-section-summary' >Indexes: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>latitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-40cbe461-1297-40a1-9003-f921cc13046e' class='xr-index-data-in' type='checkbox'/><label for='index-40cbe461-1297-40a1-9003-f921cc13046e' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([ -87.18750000000003,  -81.56250000000001,            -75.9375,\n", "        -70.31249999999999,  -64.68750000000001,            -59.0625,\n", "                  -53.4375,            -47.8125,            -42.1875,\n", "                  -36.5625, -30.937499999999996, -25.312500000000004,\n", "       -19.687499999999996, -14.062499999999991,  -8.437499999999996,\n", "        -2.812500000000003,   2.812500000000003,   8.437500000000009,\n", "        14.062500000000004,  19.687499999999996,  25.312500000000004,\n", "         30.93750000000001,  36.562499999999986,             42.1875,\n", "                   47.8125,             53.4375,  59.062500000000014,\n", "         64.68750000000001,             70.3125,             75.9375,\n", "         81.56249999999997,   87.18750000000003],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;latitude&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>longitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-cf116f1b-6c78-4114-b285-f631a402136b' class='xr-index-data-in' type='checkbox'/><label for='index-cf116f1b-6c78-4114-b285-f631a402136b' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([               0.0,              5.625,              11.25,\n", "                   16.875,               22.5,             28.125,\n", "                    33.75,             39.375,               45.0,\n", "                   50.625,              56.25,  61.87499999999999,\n", "                     67.5,             73.125,              78.75,\n", "                   84.375,               90.0,             95.625,\n", "                   101.25,            106.875,              112.5,\n", "                  118.125, 123.74999999999999,            129.375,\n", "                    135.0,            140.625,             146.25,\n", "                  151.875,              157.5,            163.125,\n", "                   168.75,            174.375,              180.0,\n", "                  185.625,             191.25,            196.875,\n", "                    202.5,            208.125,             213.75,\n", "                  219.375,              225.0, 230.62499999999997,\n", "                   236.25,            241.875, 247.49999999999997,\n", "                  253.125,             258.75,            264.375,\n", "                    270.0,            275.625,             281.25,\n", "                  286.875,              292.5,            298.125,\n", "                   303.75,            309.375,              315.0,\n", "                  320.625,             326.25,            331.875,\n", "                    337.5,            343.125,             348.75,\n", "                  354.375],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;longitude&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>number</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-b78c5e74-9723-49ff-b629-5acafd8ea22f' class='xr-index-data-in' type='checkbox'/><label for='index-b78c5e74-9723-49ff-b629-5acafd8ea22f' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([ 1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18,\n", "       19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36,\n", "       37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50],\n", "      dtype=&#x27;int32&#x27;, name=&#x27;number&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>lead_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-82b7446f-4dc3-443b-bd82-12f60efb949d' class='xr-index-data-in' type='checkbox'/><label for='index-82b7446f-4dc3-443b-bd82-12f60efb949d' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(TimedeltaIndex([&#x27;0 days 06:00:00&#x27;], dtype=&#x27;timedelta64[ns]&#x27;, name=&#x27;lead_time&#x27;, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>init_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-842e9018-9287-446b-80ad-c7bfa5b89064' class='xr-index-data-in' type='checkbox'/><label for='index-842e9018-9287-446b-80ad-c7bfa5b89064' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2020-01-01&#x27;], dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;init_time&#x27;, freq=None))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-c809d79d-172c-49e7-9357-210c0c32aa27' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-c809d79d-172c-49e7-9357-210c0c32aa27' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 411kB\n", "Dimensions:                  (latitude: 32, longitude: 64, number: 50,\n", "                              lead_time: 1, init_time: 1)\n", "Coordinates:\n", "  * latitude                 (latitude) float64 256B -87.19 -81.56 ... 87.19\n", "  * longitude                (longitude) float64 512B 0.0 5.625 ... 348.8 354.4\n", "  * number                   (number) int32 200B 1 2 3 4 5 6 ... 46 47 48 49 50\n", "  * lead_time                (lead_time) timedelta64[ns] 8B 06:00:00\n", "  * init_time                (init_time) datetime64[ns] 8B 2020-01-01\n", "Data variables:\n", "    total_precipitation_6hr  (init_time, number, lead_time, longitude, latitude) float32 410kB ..."]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["prediction_chunk"]}, {"cell_type": "markdown", "id": "555dc2e6-10cb-4190-9f7d-bb7bbb5901e6", "metadata": {}, "source": ["Note that the wrappers are applied in the order of the given list, so in this case ContinuousToBinary is applied first."]}, {"cell_type": "code", "execution_count": 9, "id": "96b17ee0-b6cd-4e6a-a464-8448407270f7", "metadata": {}, "outputs": [], "source": ["wrapped_csi = wrappers.WrappedMetric(\n", "    metric=categorical.CSI(),\n", "    transforms=[\n", "        wrappers.ContinuousToBinary(\n", "            which='both',\n", "            threshold_value=[1/1000, 5/1000],   # Raw values are in m\n", "            threshold_dim='threshold_precipitation'\n", "        ),\n", "        wrappers.EnsembleMean(\n", "            which='predictions', ensemble_dim='number'\n", "        ),\n", "        wrappers.ContinuousToBinary(\n", "            which='predictions',\n", "            threshold_value=[0.25, 0.75],\n", "            threshold_dim='threshold_probability'\n", "        ),\n", "    ],\n", ")\n", "metrics = {'csi': wrapped_csi}"]}, {"cell_type": "code", "execution_count": 10, "id": "f7711078-15de-46f9-842e-45b4c910ab2f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 72B\n", "Dimensions:                      (lead_time: 1, threshold_precipitation: 2,\n", "                                  threshold_probability: 2)\n", "Coordinates:\n", "  * lead_time                    (lead_time) timedelta64[ns] 8B 06:00:00\n", "  * threshold_precipitation      (threshold_precipitation) float64 16B 0.001 ...\n", "  * threshold_probability        (threshold_probability) float64 16B 0.25 0.75\n", "Data variables:\n", "    csi.total_precipitation_6hr  (lead_time, threshold_precipitation, threshold_probability) float64 32B ...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-4cc4271d-9dc5-4548-86e2-6d911880f6bc' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-4cc4271d-9dc5-4548-86e2-6d911880f6bc' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>lead_time</span>: 1</li><li><span class='xr-has-index'>threshold_precipitation</span>: 2</li><li><span class='xr-has-index'>threshold_probability</span>: 2</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-12cbfa6b-6ec3-448e-bd8d-40a0657e0006' class='xr-section-summary-in' type='checkbox'  checked><label for='section-12cbfa6b-6ec3-448e-bd8d-40a0657e0006' class='xr-section-summary' >Coordinates: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>lead_time</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>timedelta64[ns]</div><div class='xr-var-preview xr-preview'>06:00:00</div><input id='attrs-a65b7980-234a-4b3f-8566-4e15dc21f61d' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-a65b7980-234a-4b3f-8566-4e15dc21f61d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-6394e857-4f0d-4a03-8009-1303f86abe2b' class='xr-var-data-in' type='checkbox'><label for='data-6394e857-4f0d-4a03-8009-1303f86abe2b' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>time since forecast_reference_time</dd><dt><span>standard_name :</span></dt><dd>forecast_period</dd></dl></div><div class='xr-var-data'><pre>array([21600000000000], dtype=&#x27;timedelta64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>threshold_precipitation</span></div><div class='xr-var-dims'>(threshold_precipitation)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.001 0.005</div><input id='attrs-d264517d-dd36-4186-b340-37f0a50ffa58' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-d264517d-dd36-4186-b340-37f0a50ffa58' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a9496614-4f90-4cdf-a60a-4e3a017df5d4' class='xr-var-data-in' type='checkbox'><label for='data-a9496614-4f90-4cdf-a60a-4e3a017df5d4' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([0.001, 0.005])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>threshold_probability</span></div><div class='xr-var-dims'>(threshold_probability)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.25 0.75</div><input id='attrs-289351ef-b997-4fe8-bd55-4035139cd81c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-289351ef-b997-4fe8-bd55-4035139cd81c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-562f0f73-9898-46e0-9f81-722e41807d2f' class='xr-var-data-in' type='checkbox'><label for='data-562f0f73-9898-46e0-9f81-722e41807d2f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([0.25, 0.75])</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-b19aa680-89b7-4342-8052-28d01235235d' class='xr-section-summary-in' type='checkbox'  checked><label for='section-b19aa680-89b7-4342-8052-28d01235235d' class='xr-section-summary' >Data variables: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>csi.total_precipitation_6hr</span></div><div class='xr-var-dims'>(lead_time, threshold_precipitation, threshold_probability)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.7165 0.8031 0.4839 0.45</div><input id='attrs-f1bbe488-a091-4889-a263-bc27ca95e822' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-f1bbe488-a091-4889-a263-bc27ca95e822' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-fa3d39aa-a657-44b2-a1ac-b0b93eb5a1e3' class='xr-var-data-in' type='checkbox'><label for='data-fa3d39aa-a657-44b2-a1ac-b0b93eb5a1e3' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[[0.7164557 , 0.80307692],\n", "        [0.48387097, 0.45      ]]])</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-9d673824-c302-4053-b4bb-4decb60fa2a4' class='xr-section-summary-in' type='checkbox'  ><label for='section-9d673824-c302-4053-b4bb-4decb60fa2a4' class='xr-section-summary' >Indexes: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>lead_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-b08ed43e-1c8b-4d4f-bc17-e58394bd6f70' class='xr-index-data-in' type='checkbox'/><label for='index-b08ed43e-1c8b-4d4f-bc17-e58394bd6f70' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(TimedeltaIndex([&#x27;0 days 06:00:00&#x27;], dtype=&#x27;timedelta64[ns]&#x27;, name=&#x27;lead_time&#x27;, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>threshold_precipitation</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-c096ad41-c107-4c9e-b6c6-78c735f0fedc' class='xr-index-data-in' type='checkbox'/><label for='index-c096ad41-c107-4c9e-b6c6-78c735f0fedc' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([0.001, 0.005], dtype=&#x27;float64&#x27;, name=&#x27;threshold_precipitation&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>threshold_probability</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-6bcce5e3-ba7b-4cfd-835a-f0cb1141e2d4' class='xr-index-data-in' type='checkbox'/><label for='index-6bcce5e3-ba7b-4cfd-835a-f0cb1141e2d4' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([0.25, 0.75], dtype=&#x27;float64&#x27;, name=&#x27;threshold_probability&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-af940f92-7538-49c2-8770-bed35ec53edc' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-af940f92-7538-49c2-8770-bed35ec53edc' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 72B\n", "Dimensions:                      (lead_time: 1, threshold_precipitation: 2,\n", "                                  threshold_probability: 2)\n", "Coordinates:\n", "  * lead_time                    (lead_time) timedelta64[ns] 8B 06:00:00\n", "  * threshold_precipitation      (threshold_precipitation) float64 16B 0.001 ...\n", "  * threshold_probability        (threshold_probability) float64 16B 0.25 0.75\n", "Data variables:\n", "    csi.total_precipitation_6hr  (lead_time, threshold_precipitation, threshold_probability) float64 32B ..."]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["aggregator = aggregation.Aggregator(\n", "  reduce_dims=['init_time', 'latitude', 'longitude'],\n", ")\n", "aggregation.compute_metric_values_for_single_chunk(\n", "    metrics,\n", "    aggregator,\n", "    prediction_chunk,\n", "    target_chunk\n", ")"]}, {"cell_type": "markdown", "id": "b71e4e01-553d-4b28-a031-36824e56e672", "metadata": {}, "source": ["As we can see the final result has two additional dimensions: `threshold_precipitation` and `threshold_probability`."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}