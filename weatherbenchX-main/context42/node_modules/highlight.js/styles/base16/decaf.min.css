/*!
  Theme: Decaf
  Author: <PERSON> (https://github.com/alex<PERSON><PERSON>)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#ccc;background:#2d2d2d}.hljs ::selection,.hljs::selection{background-color:#515151;color:#ccc}.hljs-comment{color:#777}.hljs-tag{color:#b4b7b4}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#ccc}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#ff7f7b}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#ffbf70}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#ffd67c}.hljs-strong{font-weight:700;color:#ffd67c}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#beda78}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#bed6ff}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#90bee1}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#efb3f7}.hljs-emphasis{color:#efb3f7;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#ff93b3}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}