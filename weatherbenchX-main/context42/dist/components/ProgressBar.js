import { Box, Text } from "ink";
import { jsx, jsxs } from "react/jsx-runtime";
const ProgressBar = ({ value, max, label, terminalWidth = 80 }) => {
  const width = 40;
  const percentage = max > 0 ? Math.min(100, Math.round(value / max * 100)) : 0;
  const filled = Math.round(percentage / 100 * width);
  const empty = width - filled;
  const valueText = `${value}/${max}`;
  const percentageText = `(${percentage}%)`;
  const componentWidth = 2 + width + 1 + valueText.length + 1 + (label ? label.length + 1 : 0) + percentageText.length;
  const leftPadding = Math.max(0, Math.floor((terminalWidth - componentWidth) / 2));
  return /* @__PURE__ */ jsxs(Box, { children: [
    /* @__PURE__ */ jsx(Text, { children: " ".repeat(leftPadding) }),
    /* @__PURE__ */ jsxs(Box, { flexDirection: "row", gap: 1, children: [
      /* @__PURE__ */ jsx(Text, { children: "[" }),
      /* @__PURE__ */ jsx(Text, { color: "green", children: "\u2588".repeat(filled) }),
      /* @__PURE__ */ jsx(Text, { dimColor: true, children: "\u2591".repeat(empty) }),
      /* @__PURE__ */ jsx(Text, { children: "]" }),
      /* @__PURE__ */ jsxs(Text, { children: [
        value,
        "/",
        max
      ] }),
      label && /* @__PURE__ */ jsx(Text, { children: label }),
      /* @__PURE__ */ jsxs(Text, { dimColor: true, children: [
        "(",
        percentage,
        "%)"
      ] })
    ] })
  ] });
};
export {
  ProgressBar
};
//# sourceMappingURL=ProgressBar.js.map
