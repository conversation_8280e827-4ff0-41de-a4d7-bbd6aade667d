{"version": 3, "sources": ["../src/main.tsx"], "sourcesContent": ["#!/usr/bin/env node\nimport { render } from \"ink\"\nimport { cli } from \"./cli.js\"\nimport { Index } from \"./index.js\"\n\nconst result = await cli()\n\nif (!result) {\n  process.exit(0)\n}\n\nrender(\n  <Index\n    fileGroups={result.fileGroups}\n    inputDir={result.inputDir}\n    outputDir={result.outputDir}\n    model={result.model}\n    concurrency={result.concurrency}\n    total={result.total}\n    database={result.database}\n    debug={result.debug}\n  />,\n)\n"], "mappings": ";AACA,SAAS,cAAc;AACvB,SAAS,WAAW;AACpB,SAAS,aAAa;AASpB;AAPF,MAAM,SAAS,MAAM,IAAI;AAEzB,IAAI,CAAC,QAAQ;AACX,UAAQ,KAAK,CAAC;AAChB;AAEA;AAAA,EACE;AAAA,IAAC;AAAA;AAAA,MACC,YAAY,OAAO;AAAA,MACnB,UAAU,OAAO;AAAA,MACjB,WAAW,OAAO;AAAA,MAClB,OAAO,OAAO;AAAA,MACd,aAAa,OAAO;AAAA,MACpB,OAAO,OAAO;AAAA,MACd,UAAU,OAAO;AAAA,MACjB,OAAO,OAAO;AAAA;AAAA,EAChB;AACF;", "names": []}