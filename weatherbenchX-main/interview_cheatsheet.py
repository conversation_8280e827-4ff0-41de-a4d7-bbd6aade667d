#!/usr/bin/env python3
"""
GRIDMATIC INTERVIEW CHEAT SHEET
Quick reference for common patterns and operations

Print this out or keep it handy during practice!
"""

import pandas as pd
import numpy as np
import xarray as xr
from datetime import datetime, timedelta

# =============================================================================
# PANDAS QUICK REFERENCE
# =============================================================================

def pandas_cheatsheet():
    """Essential pandas operations for weather data"""
    
    # CREATE DATAFRAME
    df = pd.DataFrame({
        'station': ['A', 'B', 'A', 'B'],
        'temp': [25.0, 30.0, 26.0, 31.0],
        'timestamp': pd.date_range('2024-01-01', periods=4, freq='H')
    })
    
    # DATA CLEANING
    df['temp'] = pd.to_numeric(df['temp'], errors='coerce')  # Convert to numeric
    df = df.dropna(subset=['temp'])                          # Remove missing
    df = df.drop_duplicates()                                # Remove duplicates
    df = df.query('temp >= -50 and temp <= 60')             # Filter outliers
    
    # TIME OPERATIONS
    df['timestamp'] = pd.to_datetime(df['timestamp'])        # Convert to datetime
    df['date'] = df['timestamp'].dt.date                     # Extract date
    df['hour'] = df['timestamp'].dt.hour                     # Extract hour
    df['month'] = df['timestamp'].dt.month                   # Extract month
    
    # GROUPBY OPERATIONS
    daily = df.groupby('date')['temp'].agg(['mean', 'min', 'max'])
    by_station = df.groupby('station')['temp'].mean()
    
    # RESAMPLING (time series)
    df_indexed = df.set_index('timestamp')
    hourly = df_indexed.resample('H')['temp'].mean()
    daily = df_indexed.resample('D')['temp'].agg(['mean', 'std'])
    
    # MERGING
    df1 = pd.DataFrame({'station': ['A', 'B'], 'lat': [40, 34]})
    df2 = pd.DataFrame({'station': ['A', 'B'], 'lon': [-74, -118]})
    merged = pd.merge(df1, df2, on='station')
    
    return df


# =============================================================================
# XARRAY QUICK REFERENCE
# =============================================================================

def xarray_cheatsheet():
    """Essential xarray operations for gridded weather data"""
    
    # CREATE DATASET
    lats = np.linspace(30, 50, 21)
    lons = np.linspace(-120, -80, 41)
    times = pd.date_range('2024-01-01', periods=365, freq='D')
    
    temp_data = np.random.normal(20, 10, (365, 21, 41))
    
    ds = xr.Dataset({
        'temperature': (['time', 'lat', 'lon'], temp_data)
    }, coords={'time': times, 'lat': lats, 'lon': lons})
    
    # SELECTION
    subset = ds.sel(lat=slice(35, 45), lon=slice(-110, -90))  # Geographic subset
    point = ds.sel(lat=40, lon=-100, method='nearest')        # Nearest point
    time_slice = ds.sel(time=slice('2024-06-01', '2024-08-31'))  # Time range
    
    # AGGREGATION
    spatial_mean = ds.mean(dim=['lat', 'lon'])               # Spatial average
    time_mean = ds.mean(dim='time')                          # Time average
    monthly = ds.resample(time='M').mean()                   # Monthly means
    
    # FILTERING
    hot_days = ds.where(ds['temperature'] > 30)             # Conditional selection
    
    # CALCULATIONS
    temp_range = ds['temperature'].max() - ds['temperature'].min()
    anomaly = ds['temperature'] - ds['temperature'].mean(dim='time')
    
    return ds


# =============================================================================
# NUMPY QUICK REFERENCE
# =============================================================================

def numpy_cheatsheet():
    """Essential numpy operations"""
    
    # ARRAY CREATION
    arr = np.array([1, 2, 3, 4, 5])
    zeros = np.zeros((3, 4))
    ones = np.ones((2, 3))
    random = np.random.normal(0, 1, (10, 10))
    
    # STATISTICS
    mean_val = np.mean(arr)
    std_val = np.std(arr)
    percentile = np.percentile(arr, 95)
    
    # BOOLEAN INDEXING
    mask = arr > 3
    filtered = arr[mask]
    
    # MATHEMATICAL OPERATIONS
    squared = arr ** 2
    sqrt_vals = np.sqrt(arr)
    sin_vals = np.sin(arr)
    
    return arr


# =============================================================================
# COMMON PATTERNS & TEMPLATES
# =============================================================================

def data_validation_template(df):
    """Template for data validation"""
    if df.empty:
        return pd.DataFrame()
    
    # Check required columns
    required_cols = ['station', 'temp', 'timestamp']
    if not all(col in df.columns for col in required_cols):
        raise ValueError(f"Missing required columns: {required_cols}")
    
    # Convert data types
    df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
    df['temp'] = pd.to_numeric(df['temp'], errors='coerce')
    
    # Remove invalid data
    df = df.dropna(subset=['timestamp', 'temp'])
    
    # Validate ranges
    df = df.query('temp >= -100 and temp <= 60')
    
    return df


def time_series_analysis_template(df):
    """Template for time series analysis"""
    # Ensure datetime index
    df = df.set_index('timestamp')
    
    # Resample to regular intervals
    daily = df.resample('D').agg({
        'temp': ['mean', 'min', 'max', 'count']
    })
    
    # Calculate moving averages
    df['temp_7day'] = df['temp'].rolling(window=7).mean()
    
    # Find extremes
    max_temp = df['temp'].max()
    min_temp = df['temp'].min()
    
    return daily


def geospatial_filter_template(data, bounds):
    """Template for geospatial filtering"""
    filtered = []
    
    for item in data:
        if ('lat' in item and 'lon' in item and
            item['lat'] is not None and item['lon'] is not None):
            
            lat, lon = item['lat'], item['lon']
            
            if (bounds['south'] <= lat <= bounds['north'] and
                bounds['west'] <= lon <= bounds['east']):
                filtered.append(item)
    
    return filtered


# =============================================================================
# ERROR HANDLING PATTERNS
# =============================================================================

def safe_file_read(filename):
    """Safe file reading with error handling"""
    try:
        df = pd.read_csv(filename)
        return df
    except FileNotFoundError:
        print(f"File {filename} not found")
        return pd.DataFrame()
    except Exception as e:
        print(f"Error reading {filename}: {e}")
        return pd.DataFrame()


def safe_calculation(func, *args, **kwargs):
    """Safe calculation with error handling"""
    try:
        return func(*args, **kwargs)
    except ZeroDivisionError:
        return np.nan
    except Exception as e:
        print(f"Calculation error: {e}")
        return None


# =============================================================================
# INTERVIEW SUCCESS CHECKLIST
# =============================================================================

INTERVIEW_CHECKLIST = """
BEFORE YOU START:
□ Understand the problem completely
□ Ask clarifying questions
□ Discuss your approach out loud

WHILE CODING:
□ Start with a simple working solution
□ Handle the happy path first
□ Add error handling for edge cases
□ Use descriptive variable names
□ Test with simple examples

COMMON EDGE CASES:
□ Empty input data
□ Missing values (None, NaN)
□ Single row/column data
□ Invalid data types
□ Out-of-range values

PANDAS GOTCHAS:
□ Convert strings to datetime/numeric
□ Handle missing data before operations
□ Use .copy() when modifying DataFrames
□ Check for empty DataFrames

XARRAY GOTCHAS:
□ Understand coordinate vs data variables
□ Use .sel() for label-based selection
□ Use .isel() for integer-based selection
□ Handle missing coordinates gracefully

TIME MANAGEMENT:
□ 2-3 minutes: Understand problem
□ 15-20 minutes: Code solution
□ 5-8 minutes: Test and debug
□ 2-3 minutes: Discuss improvements
"""

# =============================================================================
# QUICK SYNTAX REFERENCE
# =============================================================================

QUICK_SYNTAX = {
    # DataFrame operations
    'create_df': "pd.DataFrame({'col': [1, 2, 3]})",
    'read_csv': "pd.read_csv('file.csv')",
    'filter_rows': "df[df['col'] > 5]",
    'group_by': "df.groupby('col').mean()",
    'merge': "pd.merge(df1, df2, on='key')",
    'pivot': "df.pivot_table(values='val', index='row', columns='col')",
    
    # Time operations
    'to_datetime': "pd.to_datetime(df['date'])",
    'extract_date': "df['date'].dt.date",
    'resample': "df.resample('D').mean()",
    'rolling': "df['col'].rolling(7).mean()",
    
    # XArray operations
    'create_ds': "xr.Dataset({'var': (['x', 'y'], data)})",
    'select': "ds.sel(x=slice(0, 10))",
    'where': "ds.where(ds['var'] > 0)",
    'groupby': "ds.groupby('time.month').mean()",
    
    # NumPy operations
    'array': "np.array([1, 2, 3])",
    'stats': "np.mean(arr), np.std(arr), np.percentile(arr, 95)",
    'boolean': "arr[arr > 5]",
    'math': "np.sin(arr), np.sqrt(arr), arr ** 2"
}

if __name__ == "__main__":
    print("GRIDMATIC INTERVIEW CHEAT SHEET")
    print("=" * 50)
    print("\nQuick Syntax Reference:")
    for operation, syntax in QUICK_SYNTAX.items():
        print(f"{operation:15}: {syntax}")
    
    print(f"\n{INTERVIEW_CHECKLIST}")
    
    print("\nPractice with the exercises in interview_exercises.py!")
    print("Check your solutions against interview_solutions.py")
