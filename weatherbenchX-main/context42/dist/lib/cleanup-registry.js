import { unlink } from "node:fs/promises";
class CleanupRegistry {
  handlers = /* @__PURE__ */ new Set();
  isShuttingDown = false;
  register(handler) {
    this.handlers.add(handler);
    return () => this.handlers.delete(handler);
  }
  async cleanup() {
    if (this.isShuttingDown) return;
    this.isShuttingDown = true;
    const promises = [];
    for (const handler of this.handlers) {
      try {
        const result = handler();
        if (result instanceof Promise) {
          promises.push(result.catch(console.error));
        }
      } catch (error) {
        console.error("Cleanup handler error:", error);
      }
    }
    await Promise.all(promises);
  }
  reset() {
    this.handlers.clear();
    this.isShuttingDown = false;
  }
}
const cleanupRegistry = new CleanupRegistry();
const createFileCleanupHandler = (files) => {
  return async () => {
    const promises = [];
    for (const file of files) {
      promises.push(
        unlink(file).catch((error) => {
          if (error.code !== "ENOENT") {
            console.error(`Failed to clean up ${file}:`, error.message);
          }
        })
      );
    }
    await Promise.all(promises);
  };
};
export {
  cleanupRegistry,
  createFileCleanupHandler
};
//# sourceMappingURL=cleanup-registry.js.map
