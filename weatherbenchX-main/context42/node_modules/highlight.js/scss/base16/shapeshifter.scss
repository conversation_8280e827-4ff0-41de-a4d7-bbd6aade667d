pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Shapeshifter
  Author: <PERSON> (http://tybenz.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme shapeshifter
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #f9f9f9  Default Background
base01  #e0e0e0  Lighter Background (Used for status bars, line number and folding marks)
base02  #ababab  Selection Background
base03  #555555  Comments, Invisibles, Line Highlighting
base04  #343434  Dark Foreground (Used for status bars)
base05  #102015  Default Foreground, Caret, Delimiters, Operators
base06  #040404  Light Foreground (Not often used)
base07  #000000  Light Background (Not often used)
base08  #e92f2f  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #e09448  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #dddd13  Classes, Markup Bold, Search Text Background
base0B  #0ed839  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #23edda  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #3b48e3  Functions, Methods, Attribute IDs, Headings
base0E  #f996e2  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #69542d  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #102015;
  background: #f9f9f9
}
.hljs::selection,
.hljs ::selection {
  background-color: #ababab;
  color: #102015
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #555555 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #555555
}
/* base04 - #343434 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #343434
}
/* base05 - #102015 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #102015
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #e92f2f
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #e09448
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #dddd13
}
.hljs-strong {
  font-weight: bold;
  color: #dddd13
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #0ed839
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #23edda
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #3b48e3
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #f996e2
}
.hljs-emphasis {
  color: #f996e2;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #69542d
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}