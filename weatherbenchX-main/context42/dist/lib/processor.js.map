{"version": 3, "sources": ["../../src/lib/processor.ts"], "sourcesContent": ["import { mkdir, rename, unlink } from \"node:fs/promises\"\nimport { join } from \"node:path\"\nimport { sum } from \"es-toolkit\"\nimport PQueue from \"p-queue\"\nimport { cleanupRegistry, createFileCleanupHandler } from \"./cleanup-registry.js\"\nimport { generateStyleGuide } from \"./generator.js\"\nimport type {\n  FileGroup,\n  Language,\n  Processor,\n  ProcessorOptions,\n  ProcessorRunOptions,\n  QueuedTask,\n  Worker,\n  WorkerUpdate,\n} from \"./types.js\"\n\nexport const createStyleGuideProcessor = (options: ProcessorOptions): Processor => {\n  // State managed via closure\n  const workers: Worker[] = Array.from({ length: options.concurrency }, (_, index) => ({\n    id: index + 1,\n    status: \"idle\",\n  }))\n\n  let total: number | undefined\n  let completed: number | undefined\n  let queuedTasks: QueuedTask[] = []\n  let abortController: AbortController | undefined\n\n  const updateWorker = (id: number, updates: Partial<WorkerUpdate>): void => {\n    const worker = workers.find(w => w.id === id)!\n    Object.assign(worker, updates)\n    options.onWorkerUpdate?.(worker)\n  }\n\n  const run = async ({ fileGroups, outputDir }: ProcessorRunOptions) => {\n    const createdStyleGuides = new Map<Language, string[]>() // Track created files by language (multiple per language)\n    const createdFiles = new Set<string>() // Track all created files for cleanup\n    const groups = Array.from(fileGroups.values()).flat()\n\n    // Create new abort controller for this run\n    abortController = new AbortController()\n\n    // Register cleanup handler for temporary files\n    const unregisterCleanup = cleanupRegistry.register(createFileCleanupHandler(createdFiles))\n\n    try {\n      completed = 0\n      total = sum(groups.map(g => g.files.length))\n\n      // Create output directory\n      await mkdir(outputDir, { recursive: true })\n\n      // Build tasks and dependency edges\n      const tasks: Record<string, TaskFn> = {}\n      const edges: [string, string][] = []\n\n      // Helper to check if one directory is a child of another\n      const isChildOf = (child: string, parent: string): boolean => {\n        return child.startsWith(`${parent}/`) && child !== parent\n      }\n\n      // Create task ID from group\n      const getTaskId = (group: FileGroup): string => {\n        return `${group.language}:${group.directory}`\n      }\n\n      // Build dependency edges\n      for (const group of groups) {\n        const taskId = getTaskId(group)\n\n        // Find all parent directories\n        for (const potentialParent of groups) {\n          if (group.language === potentialParent.language && isChildOf(group.directory, potentialParent.directory)) {\n            // Check if it's a direct parent (no intermediate directories)\n            const isDirectParent = !groups.some(\n              g =>\n                g.language === group.language &&\n                g.directory !== group.directory &&\n                g.directory !== potentialParent.directory &&\n                isChildOf(group.directory, g.directory) &&\n                isChildOf(g.directory, potentialParent.directory),\n            )\n\n            if (isDirectParent) {\n              // Edge: [child, parent] - child must complete before parent\n              edges.push([taskId, getTaskId(potentialParent)])\n            }\n          }\n        }\n      }\n\n      // Build tasks with worker management\n      const workerQueue: number[] = []\n      const workerMutex = { locked: false }\n\n      const getWorker = async (): Promise<number> => {\n        while (true) {\n          // Atomic check and grab\n          if (!workerMutex.locked && workerQueue.length > 0) {\n            workerMutex.locked = true\n            const workerId = workerQueue.shift()\n            workerMutex.locked = false\n            if (workerId !== undefined) return workerId\n          }\n          await new Promise(resolve => setTimeout(resolve, 10))\n        }\n      }\n\n      const releaseWorker = (workerId: number) => {\n        workerQueue.push(workerId)\n      }\n\n      // Initialize worker queue\n      workers.forEach(w => workerQueue.push(w.id))\n\n      // Create tasks\n      for (const group of groups) {\n        const taskId = getTaskId(group)\n\n        tasks[taskId] = async () => {\n          const assignedWorkerId = await getWorker()\n\n          try {\n            // Update worker status to working\n            updateWorker(assignedWorkerId, {\n              status: \"working\",\n              language: group.language,\n              directory: group.directory,\n            })\n\n            // Query for child directory style guides\n            const childGuides = options.database.getChildStyleGuides(group.directory, group.language)\n            const childStyleGuides =\n              childGuides.length > 0 ? Object.fromEntries(childGuides.map(g => [g.directory, g.content])) : undefined\n\n            // Generate style guide\n            await generateStyleGuide({\n              model: options.model,\n              files: [...group.files],\n              language: group.language,\n              childStyleGuides,\n              signal: abortController?.signal,\n              onProgress: message => {\n                updateWorker(assignedWorkerId, {\n                  progress: message,\n                })\n              },\n            })\n\n            // For testing compatibility, save a placeholder to database\n            // In real usage, the content would be read from the created file\n            const langName = group.language.toUpperCase()\n            options.database.saveStyleGuide(group.language, `# ${langName} Style Guide`, group.directory)\n\n            // Track created file location with new naming pattern\n            // The language IS the extension now\n            const styleFileName = `style.${group.language}.md`\n            const styleFilePath = join(group.directory, styleFileName)\n\n            // Add to language-specific array\n            if (!createdStyleGuides.has(group.language)) {\n              createdStyleGuides.set(group.language, [])\n            }\n            createdStyleGuides.get(group.language)!.push(styleFilePath)\n            createdFiles.add(styleFilePath)\n\n            // Update worker status to success\n            updateWorker(assignedWorkerId, {\n              status: \"success\",\n              language: group.language,\n              directory: group.directory,\n            })\n          } catch (error) {\n            // Update worker status with error\n            updateWorker(assignedWorkerId, {\n              status: \"error\",\n              language: group.language,\n              directory: group.directory,\n              error: error instanceof Error ? error.message : \"Unknown error\",\n            })\n            throw error\n          } finally {\n            // Release worker and update progress\n            releaseWorker(assignedWorkerId)\n            updateWorker(assignedWorkerId, {\n              status: \"idle\",\n              language: undefined,\n              directory: undefined,\n              error: undefined,\n              progress: undefined,\n            })\n            completed = (completed ?? 0) + group.files.length\n            options.onProgress?.(completed)\n          }\n        }\n      }\n\n      // Map to store task metadata for queue display\n      const taskMetadata = new Map<string, { language: Language; directory: string }>()\n      for (const group of groups) {\n        const taskId = getTaskId(group)\n        taskMetadata.set(taskId, {\n          language: group.language,\n          directory: group.directory,\n        })\n      }\n\n      // Run tasks with dependencies\n      await runTasks(tasks, edges, options.concurrency, queueState => {\n        // Map ready tasks\n        const readyTasks = queueState.ready.map(id => {\n          const metadata = taskMetadata.get(id)!\n          return {\n            id,\n            language: metadata.language,\n            directory: metadata.directory,\n            status: \"ready\" as const,\n          }\n        })\n\n        // Map waiting tasks\n        const waitingTasks = Array.from(queueState.waiting.entries()).map(([id, deps]) => {\n          const metadata = taskMetadata.get(id)!\n          return {\n            id,\n            language: metadata.language,\n            directory: metadata.directory,\n            status: \"waiting\" as const,\n            pendingDeps: deps,\n          }\n        })\n\n        // Combine all tasks\n        queuedTasks = [...readyTasks, ...waitingTasks]\n        options.onQueueUpdate?.(queuedTasks)\n      })\n\n      // Move created style guide files to output directory (only the last one per language)\n      await Promise.all(\n        Array.from(createdStyleGuides.entries()).map(async ([language, sourcePaths]) => {\n          // Get the last file created for this language\n          const sourcePath = sourcePaths[sourcePaths.length - 1]\n          if (sourcePath) {\n            try {\n              await rename(sourcePath, join(outputDir, `${language}.md`))\n              // Remove from cleanup set on successful move\n              createdFiles.delete(sourcePath)\n            } catch (err) {\n              console.error(`Failed to move ${language} style guide:`, err instanceof Error ? err.message : String(err))\n            }\n          }\n        }),\n      )\n\n      // Clear queue when done\n      queuedTasks = []\n      options.onQueueUpdate?.(queuedTasks)\n\n      // Return map with languages for UI display\n      const results = new Map<Language, string>()\n      for (const [language] of createdStyleGuides) {\n        results.set(language, `${language}.md`)\n      }\n      return results\n    } finally {\n      // Clear abort controller\n      abortController = undefined\n\n      // Unregister cleanup handler\n      unregisterCleanup()\n\n      // Clean up any remaining style files\n      if (createdFiles.size > 0) {\n        await Promise.all(\n          Array.from(createdFiles).map(async filePath => {\n            try {\n              await unlink(filePath)\n            } catch (_err) {\n              // Ignore errors during cleanup - file might not exist\n            }\n          }),\n        )\n      }\n    }\n  }\n\n  const reset = (): void => {\n    // Abort any running operations\n    abortController?.abort()\n    abortController = undefined\n\n    total = undefined\n    completed = undefined\n    queuedTasks = []\n\n    for (const worker of workers) {\n      worker.status = \"idle\"\n      worker.directory = undefined\n      worker.language = undefined\n      worker.error = undefined\n      worker.progress = undefined\n      options.onWorkerUpdate?.(worker)\n    }\n  }\n\n  return {\n    run,\n    reset,\n    workers,\n    completed: completed ?? 0,\n    total: total ?? 0,\n    queuedTasks,\n  }\n}\n\ntype TaskFn = () => Promise<void>\n\ntype QueueState = {\n  ready: string[]\n  waiting: Map<string, number> // task id -> pending deps count\n}\n\n/** Dependency-aware task runner\n * edges: [child, parent] means child must finish before parent starts\n * onQueueUpdate: callback to notify when queue changes\n */\nasync function runTasks(\n  tasks: Record<string, TaskFn>,\n  edges: [string, string][],\n  concurrency = 4,\n  onQueueUpdate?: (state: QueueState) => void,\n) {\n  const inDeg = new Map<string, number>() // pending deps\n  const outAdj = new Map<string, string[]>() // who unlocks on finish\n\n  // initialise maps\n  for (const id in tasks) {\n    inDeg.set(id, 0)\n    outAdj.set(id, [])\n  }\n  for (const [child, parent] of edges) {\n    inDeg.set(parent, (inDeg.get(parent) ?? 0) + 1)\n    outAdj.get(child)!.push(parent)\n  }\n\n  const q = new PQueue({ concurrency })\n  const readyQueue: string[] = [] // Tasks ready to run but waiting for worker\n  const completedTasks = new Set<string>() // Track completed tasks\n\n  const updateQueue = () => {\n    if (onQueueUpdate) {\n      // Build waiting tasks map (tasks with pending dependencies)\n      const waiting = new Map<string, number>()\n      for (const [id, deps] of inDeg) {\n        if (deps > 0 && !completedTasks.has(id)) {\n          waiting.set(id, deps)\n        }\n      }\n\n      onQueueUpdate({\n        ready: [...readyQueue],\n        waiting,\n      })\n    }\n  }\n\n  const enqueue = (id: string) => {\n    readyQueue.push(id)\n    updateQueue()\n\n    q.add(async () => {\n      // Remove from ready queue when task starts\n      const index = readyQueue.indexOf(id)\n      if (index > -1) {\n        readyQueue.splice(index, 1)\n        updateQueue()\n      }\n\n      try {\n        await tasks[id]() // ← your async work\n      } catch (_error) {\n        // Task failed, but we still need to unlock dependents\n        // The error is already handled in the task itself\n      }\n\n      // Mark task as completed\n      completedTasks.add(id)\n\n      for (const nxt of outAdj.get(id)!) {\n        inDeg.set(nxt, inDeg.get(nxt)! - 1)\n        if (inDeg.get(nxt) === 0) enqueue(nxt) // unlock\n      }\n\n      // Update queue after unlocking dependents\n      updateQueue()\n    })\n  }\n\n  // seed queue with roots (no deps)\n  ;[...inDeg.entries()].filter(([, c]) => c === 0).forEach(([id]) => enqueue(id))\n  await q.onIdle() // wait for all waves\n}\n"], "mappings": "AAAA,SAAS,OAAO,QAAQ,cAAc;AACtC,SAAS,YAAY;AACrB,SAAS,WAAW;AACpB,OAAO,YAAY;AACnB,SAAS,iBAAiB,gCAAgC;AAC1D,SAAS,0BAA0B;AAY5B,MAAM,4BAA4B,CAAC,YAAyC;AAEjF,QAAM,UAAoB,MAAM,KAAK,EAAE,QAAQ,QAAQ,YAAY,GAAG,CAAC,GAAG,WAAW;AAAA,IACnF,IAAI,QAAQ;AAAA,IACZ,QAAQ;AAAA,EACV,EAAE;AAEF,MAAI;AACJ,MAAI;AACJ,MAAI,cAA4B,CAAC;AACjC,MAAI;AAEJ,QAAM,eAAe,CAAC,IAAY,YAAyC;AACzE,UAAM,SAAS,QAAQ,KAAK,OAAK,EAAE,OAAO,EAAE;AAC5C,WAAO,OAAO,QAAQ,OAAO;AAC7B,YAAQ,iBAAiB,MAAM;AAAA,EACjC;AAEA,QAAM,MAAM,OAAO,EAAE,YAAY,UAAU,MAA2B;AACpE,UAAM,qBAAqB,oBAAI,IAAwB;AACvD,UAAM,eAAe,oBAAI,IAAY;AACrC,UAAM,SAAS,MAAM,KAAK,WAAW,OAAO,CAAC,EAAE,KAAK;AAGpD,sBAAkB,IAAI,gBAAgB;AAGtC,UAAM,oBAAoB,gBAAgB,SAAS,yBAAyB,YAAY,CAAC;AAEzF,QAAI;AACF,kBAAY;AACZ,cAAQ,IAAI,OAAO,IAAI,OAAK,EAAE,MAAM,MAAM,CAAC;AAG3C,YAAM,MAAM,WAAW,EAAE,WAAW,KAAK,CAAC;AAG1C,YAAM,QAAgC,CAAC;AACvC,YAAM,QAA4B,CAAC;AAGnC,YAAM,YAAY,CAAC,OAAe,WAA4B;AAC5D,eAAO,MAAM,WAAW,GAAG,MAAM,GAAG,KAAK,UAAU;AAAA,MACrD;AAGA,YAAM,YAAY,CAAC,UAA6B;AAC9C,eAAO,GAAG,MAAM,QAAQ,IAAI,MAAM,SAAS;AAAA,MAC7C;AAGA,iBAAW,SAAS,QAAQ;AAC1B,cAAM,SAAS,UAAU,KAAK;AAG9B,mBAAW,mBAAmB,QAAQ;AACpC,cAAI,MAAM,aAAa,gBAAgB,YAAY,UAAU,MAAM,WAAW,gBAAgB,SAAS,GAAG;AAExG,kBAAM,iBAAiB,CAAC,OAAO;AAAA,cAC7B,OACE,EAAE,aAAa,MAAM,YACrB,EAAE,cAAc,MAAM,aACtB,EAAE,cAAc,gBAAgB,aAChC,UAAU,MAAM,WAAW,EAAE,SAAS,KACtC,UAAU,EAAE,WAAW,gBAAgB,SAAS;AAAA,YACpD;AAEA,gBAAI,gBAAgB;AAElB,oBAAM,KAAK,CAAC,QAAQ,UAAU,eAAe,CAAC,CAAC;AAAA,YACjD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,YAAM,cAAwB,CAAC;AAC/B,YAAM,cAAc,EAAE,QAAQ,MAAM;AAEpC,YAAM,YAAY,YAA6B;AAC7C,eAAO,MAAM;AAEX,cAAI,CAAC,YAAY,UAAU,YAAY,SAAS,GAAG;AACjD,wBAAY,SAAS;AACrB,kBAAM,WAAW,YAAY,MAAM;AACnC,wBAAY,SAAS;AACrB,gBAAI,aAAa,OAAW,QAAO;AAAA,UACrC;AACA,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,EAAE,CAAC;AAAA,QACtD;AAAA,MACF;AAEA,YAAM,gBAAgB,CAAC,aAAqB;AAC1C,oBAAY,KAAK,QAAQ;AAAA,MAC3B;AAGA,cAAQ,QAAQ,OAAK,YAAY,KAAK,EAAE,EAAE,CAAC;AAG3C,iBAAW,SAAS,QAAQ;AAC1B,cAAM,SAAS,UAAU,KAAK;AAE9B,cAAM,MAAM,IAAI,YAAY;AAC1B,gBAAM,mBAAmB,MAAM,UAAU;AAEzC,cAAI;AAEF,yBAAa,kBAAkB;AAAA,cAC7B,QAAQ;AAAA,cACR,UAAU,MAAM;AAAA,cAChB,WAAW,MAAM;AAAA,YACnB,CAAC;AAGD,kBAAM,cAAc,QAAQ,SAAS,oBAAoB,MAAM,WAAW,MAAM,QAAQ;AACxF,kBAAM,mBACJ,YAAY,SAAS,IAAI,OAAO,YAAY,YAAY,IAAI,OAAK,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC,IAAI;AAGhG,kBAAM,mBAAmB;AAAA,cACvB,OAAO,QAAQ;AAAA,cACf,OAAO,CAAC,GAAG,MAAM,KAAK;AAAA,cACtB,UAAU,MAAM;AAAA,cAChB;AAAA,cACA,QAAQ,iBAAiB;AAAA,cACzB,YAAY,aAAW;AACrB,6BAAa,kBAAkB;AAAA,kBAC7B,UAAU;AAAA,gBACZ,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAID,kBAAM,WAAW,MAAM,SAAS,YAAY;AAC5C,oBAAQ,SAAS,eAAe,MAAM,UAAU,KAAK,QAAQ,gBAAgB,MAAM,SAAS;AAI5F,kBAAM,gBAAgB,SAAS,MAAM,QAAQ;AAC7C,kBAAM,gBAAgB,KAAK,MAAM,WAAW,aAAa;AAGzD,gBAAI,CAAC,mBAAmB,IAAI,MAAM,QAAQ,GAAG;AAC3C,iCAAmB,IAAI,MAAM,UAAU,CAAC,CAAC;AAAA,YAC3C;AACA,+BAAmB,IAAI,MAAM,QAAQ,EAAG,KAAK,aAAa;AAC1D,yBAAa,IAAI,aAAa;AAG9B,yBAAa,kBAAkB;AAAA,cAC7B,QAAQ;AAAA,cACR,UAAU,MAAM;AAAA,cAChB,WAAW,MAAM;AAAA,YACnB,CAAC;AAAA,UACH,SAAS,OAAO;AAEd,yBAAa,kBAAkB;AAAA,cAC7B,QAAQ;AAAA,cACR,UAAU,MAAM;AAAA,cAChB,WAAW,MAAM;AAAA,cACjB,OAAO,iBAAiB,QAAQ,MAAM,UAAU;AAAA,YAClD,CAAC;AACD,kBAAM;AAAA,UACR,UAAE;AAEA,0BAAc,gBAAgB;AAC9B,yBAAa,kBAAkB;AAAA,cAC7B,QAAQ;AAAA,cACR,UAAU;AAAA,cACV,WAAW;AAAA,cACX,OAAO;AAAA,cACP,UAAU;AAAA,YACZ,CAAC;AACD,yBAAa,aAAa,KAAK,MAAM,MAAM;AAC3C,oBAAQ,aAAa,SAAS;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AAGA,YAAM,eAAe,oBAAI,IAAuD;AAChF,iBAAW,SAAS,QAAQ;AAC1B,cAAM,SAAS,UAAU,KAAK;AAC9B,qBAAa,IAAI,QAAQ;AAAA,UACvB,UAAU,MAAM;AAAA,UAChB,WAAW,MAAM;AAAA,QACnB,CAAC;AAAA,MACH;AAGA,YAAM,SAAS,OAAO,OAAO,QAAQ,aAAa,gBAAc;AAE9D,cAAM,aAAa,WAAW,MAAM,IAAI,QAAM;AAC5C,gBAAM,WAAW,aAAa,IAAI,EAAE;AACpC,iBAAO;AAAA,YACL;AAAA,YACA,UAAU,SAAS;AAAA,YACnB,WAAW,SAAS;AAAA,YACpB,QAAQ;AAAA,UACV;AAAA,QACF,CAAC;AAGD,cAAM,eAAe,MAAM,KAAK,WAAW,QAAQ,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,IAAI,MAAM;AAChF,gBAAM,WAAW,aAAa,IAAI,EAAE;AACpC,iBAAO;AAAA,YACL;AAAA,YACA,UAAU,SAAS;AAAA,YACnB,WAAW,SAAS;AAAA,YACpB,QAAQ;AAAA,YACR,aAAa;AAAA,UACf;AAAA,QACF,CAAC;AAGD,sBAAc,CAAC,GAAG,YAAY,GAAG,YAAY;AAC7C,gBAAQ,gBAAgB,WAAW;AAAA,MACrC,CAAC;AAGD,YAAM,QAAQ;AAAA,QACZ,MAAM,KAAK,mBAAmB,QAAQ,CAAC,EAAE,IAAI,OAAO,CAAC,UAAU,WAAW,MAAM;AAE9E,gBAAM,aAAa,YAAY,YAAY,SAAS,CAAC;AACrD,cAAI,YAAY;AACd,gBAAI;AACF,oBAAM,OAAO,YAAY,KAAK,WAAW,GAAG,QAAQ,KAAK,CAAC;AAE1D,2BAAa,OAAO,UAAU;AAAA,YAChC,SAAS,KAAK;AACZ,sBAAQ,MAAM,kBAAkB,QAAQ,iBAAiB,eAAe,QAAQ,IAAI,UAAU,OAAO,GAAG,CAAC;AAAA,YAC3G;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAGA,oBAAc,CAAC;AACf,cAAQ,gBAAgB,WAAW;AAGnC,YAAM,UAAU,oBAAI,IAAsB;AAC1C,iBAAW,CAAC,QAAQ,KAAK,oBAAoB;AAC3C,gBAAQ,IAAI,UAAU,GAAG,QAAQ,KAAK;AAAA,MACxC;AACA,aAAO;AAAA,IACT,UAAE;AAEA,wBAAkB;AAGlB,wBAAkB;AAGlB,UAAI,aAAa,OAAO,GAAG;AACzB,cAAM,QAAQ;AAAA,UACZ,MAAM,KAAK,YAAY,EAAE,IAAI,OAAM,aAAY;AAC7C,gBAAI;AACF,oBAAM,OAAO,QAAQ;AAAA,YACvB,SAAS,MAAM;AAAA,YAEf;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,QAAQ,MAAY;AAExB,qBAAiB,MAAM;AACvB,sBAAkB;AAElB,YAAQ;AACR,gBAAY;AACZ,kBAAc,CAAC;AAEf,eAAW,UAAU,SAAS;AAC5B,aAAO,SAAS;AAChB,aAAO,YAAY;AACnB,aAAO,WAAW;AAClB,aAAO,QAAQ;AACf,aAAO,WAAW;AAClB,cAAQ,iBAAiB,MAAM;AAAA,IACjC;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,aAAa;AAAA,IACxB,OAAO,SAAS;AAAA,IAChB;AAAA,EACF;AACF;AAaA,eAAe,SACb,OACA,OACA,cAAc,GACd,eACA;AACA,QAAM,QAAQ,oBAAI,IAAoB;AACtC,QAAM,SAAS,oBAAI,IAAsB;AAGzC,aAAW,MAAM,OAAO;AACtB,UAAM,IAAI,IAAI,CAAC;AACf,WAAO,IAAI,IAAI,CAAC,CAAC;AAAA,EACnB;AACA,aAAW,CAAC,OAAO,MAAM,KAAK,OAAO;AACnC,UAAM,IAAI,SAAS,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AAC9C,WAAO,IAAI,KAAK,EAAG,KAAK,MAAM;AAAA,EAChC;AAEA,QAAM,IAAI,IAAI,OAAO,EAAE,YAAY,CAAC;AACpC,QAAM,aAAuB,CAAC;AAC9B,QAAM,iBAAiB,oBAAI,IAAY;AAEvC,QAAM,cAAc,MAAM;AACxB,QAAI,eAAe;AAEjB,YAAM,UAAU,oBAAI,IAAoB;AACxC,iBAAW,CAAC,IAAI,IAAI,KAAK,OAAO;AAC9B,YAAI,OAAO,KAAK,CAAC,eAAe,IAAI,EAAE,GAAG;AACvC,kBAAQ,IAAI,IAAI,IAAI;AAAA,QACtB;AAAA,MACF;AAEA,oBAAc;AAAA,QACZ,OAAO,CAAC,GAAG,UAAU;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,UAAU,CAAC,OAAe;AAC9B,eAAW,KAAK,EAAE;AAClB,gBAAY;AAEZ,MAAE,IAAI,YAAY;AAEhB,YAAM,QAAQ,WAAW,QAAQ,EAAE;AACnC,UAAI,QAAQ,IAAI;AACd,mBAAW,OAAO,OAAO,CAAC;AAC1B,oBAAY;AAAA,MACd;AAEA,UAAI;AACF,cAAM,MAAM,EAAE,EAAE;AAAA,MAClB,SAAS,QAAQ;AAAA,MAGjB;AAGA,qBAAe,IAAI,EAAE;AAErB,iBAAW,OAAO,OAAO,IAAI,EAAE,GAAI;AACjC,cAAM,IAAI,KAAK,MAAM,IAAI,GAAG,IAAK,CAAC;AAClC,YAAI,MAAM,IAAI,GAAG,MAAM,EAAG,SAAQ,GAAG;AAAA,MACvC;AAGA,kBAAY;AAAA,IACd,CAAC;AAAA,EACH;AAGC,GAAC,GAAG,MAAM,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC;AAC9E,QAAM,EAAE,OAAO;AACjB;", "names": []}