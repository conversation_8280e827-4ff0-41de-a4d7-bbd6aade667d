import { useCallback, useMemo, useState } from "react";
import { createStyleGuideProcessor } from "../lib/processor.js";
const useProcessor = (options) => {
  const { model, concurrency, inputDir, onWorkerUpdate, fileGroups, outputDir } = options;
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [queuedTasks, setQueuedTasks] = useState([]);
  const processor = useMemo(
    () => createStyleGuideProcessor({
      model,
      concurrency,
      inputDir,
      database: options.database,
      onWorkerUpdate: (updatedWorker) => {
        setWorkers((previousWorkers) => previousWorkers.map((w) => w.id === updatedWorker.id ? updatedWorker : w));
        onWorkerUpdate?.(updatedWorker);
      },
      onProgress: setProgress,
      onQueueUpdate: setQueuedTasks
    }),
    [model, concurrency, inputDir, onWorkerUpdate, options.database]
  );
  const [workers, setWorkers] = useState(processor.workers);
  const run = useCallback(async () => {
    if (!fileGroups || !inputDir || !outputDir) {
      setError("Missing required options: fileGroups, inputDir, or outputDir");
      return;
    }
    setError(null);
    setResults(null);
    setIsRunning(true);
    try {
      const styleGuides = await processor.run({ fileGroups, inputDir, outputDir });
      setResults(styleGuides);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
    } finally {
      setIsRunning(false);
    }
  }, [processor, fileGroups, inputDir, outputDir]);
  const reset = useCallback(() => {
    processor.reset();
    setProgress(0);
    setResults(null);
    setError(null);
    setIsRunning(false);
    setQueuedTasks([]);
  }, [processor]);
  return {
    workers,
    queuedTasks,
    processor,
    progress,
    results,
    error,
    isRunning,
    run,
    reset
  };
};
export {
  useProcessor
};
//# sourceMappingURL=use-processor.js.map
