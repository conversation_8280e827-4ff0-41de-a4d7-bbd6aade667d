import path from "node:path";
import { Box, Text } from "ink";
import { useMemo } from "react";
import Table from "./Table.js";
import { jsx, jsxs } from "react/jsx-runtime";
const globpath = (inputDir, directory, language) => path.join(path.relative(inputDir, directory) || "./", `*.${language}`);
const WorkersStatus = ({ workers, inputDir, queuedTasks = [] }) => {
  const waitingTasks = queuedTasks.filter((t) => t.status === "waiting").slice(0, workers.length);
  const workersViewModel = useMemo(
    () => workers.map((agent) => ({
      agent: agent.id,
      directory: agent.directory && agent.language ? globpath(inputDir, agent.directory, agent.language) : "",
      status: agent.status === "idle" ? "Waiting..." : agent.status === "working" ? agent.progress || "Working..." : agent.status === "success" ? "Success" : agent.error
    })).filter((agent) => agent.directory),
    [workers, inputDir]
  );
  const columnWidths = useMemo(() => ({ agent: 8, directory: 22, status: 90 }), []);
  if (workersViewModel.length === 0) return null;
  return /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginTop: 1, children: [
    /* @__PURE__ */ jsx(Table, { data: workersViewModel, columnWidths }),
    waitingTasks.length !== 0 && /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [
      /* @__PURE__ */ jsxs(Text, { dimColor: true, children: [
        "Queue: ",
        waitingTasks.length
      ] }),
      waitingTasks.map((task, index) => /* @__PURE__ */ jsx(Box, { marginLeft: 2, children: /* @__PURE__ */ jsxs(Text, { dimColor: true, children: [
        globpath(inputDir, task.directory, task.language),
        task.pendingDeps && ` (${task.pendingDeps} dep${task.pendingDeps > 1 ? "s" : ""})`
      ] }) }, `waiting-${task.id}-${index}`))
    ] })
  ] });
};
export {
  WorkersStatus
};
//# sourceMappingURL=WorkerStatus.js.map
