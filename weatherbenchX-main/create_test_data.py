#!/usr/bin/env python3
"""
Create realistic test data for interview practice
Run this to generate sample CSV files for practicing
"""

import pandas as pd
import numpy as np
import json
from datetime import datetime, timed<PERSON>ta


def create_weather_stations_csv():
    """Create sample weather stations data"""
    
    # Define some realistic weather stations
    stations = [
        {'id': 'NYC001', 'name': 'New York Central Park', 'lat': 40.7829, 'lon': -73.9654},
        {'id': 'LAX001', 'name': 'Los Angeles Airport', 'lat': 33.9425, 'lon': -118.4081},
        {'id': 'CHI001', 'name': 'Chicago O\'Hare', 'lat': 41.9742, 'lon': -87.9073},
        {'id': 'MIA001', 'name': 'Miami Beach', 'lat': 25.7907, 'lon': -80.1300},
        {'id': 'SEA001', 'name': 'Seattle Downtown', 'lat': 47.6062, 'lon': -122.3321},
        {'id': 'DEN001', 'name': 'Denver Airport', 'lat': 39.8561, 'lon': -104.6737},
        {'id': 'PHX001', 'name': 'Phoenix Sky Harbor', 'lat': 33.4484, 'lon': -112.0740},
        {'id': 'ATL001', 'name': 'Atlanta Hartsfield', 'lat': 33.6407, 'lon': -84.4277},
    ]
    
    # Generate hourly data for 30 days
    start_date = datetime(2024, 7, 1)
    hours = 24 * 30  # 30 days
    
    all_data = []
    
    for station in stations:
        base_temp = {
            'NYC001': 25, 'LAX001': 22, 'CHI001': 23, 'MIA001': 28,
            'SEA001': 18, 'DEN001': 20, 'PHX001': 35, 'ATL001': 26
        }[station['id']]
        
        for hour in range(hours):
            timestamp = start_date + timedelta(hours=hour)
            
            # Create realistic temperature with daily and seasonal cycles
            daily_cycle = 8 * np.sin(2 * np.pi * hour / 24)  # Daily temperature cycle
            seasonal = 5 * np.sin(2 * np.pi * hour / (24 * 365))  # Seasonal (simplified)
            noise = np.random.normal(0, 2)  # Random variation
            
            temp = base_temp + daily_cycle + seasonal + noise
            
            # Add some missing data (5% chance)
            if np.random.random() < 0.05:
                temp = np.nan
            
            # Add some extreme outliers (1% chance)
            if np.random.random() < 0.01:
                temp = np.random.choice([-999, 999])  # Bad sensor readings
            
            all_data.append({
                'station_id': station['id'],
                'station_name': station['name'],
                'latitude': station['lat'],
                'longitude': station['lon'],
                'timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                'temperature': temp,
                'humidity': max(0, min(100, 60 + np.random.normal(0, 15))),
                'wind_speed': max(0, np.random.exponential(5)),
                'pressure': 1013 + np.random.normal(0, 10)
            })
    
    df = pd.DataFrame(all_data)
    df.to_csv('weather_stations_data.csv', index=False)
    print(f"Created weather_stations_data.csv with {len(df)} records")
    return df


def create_messy_data_csv():
    """Create intentionally messy data for cleaning exercises"""
    
    messy_data = [
        # Good data
        {'id': 'STATION_A', 'lat': 40.7, 'lon': -74.0, 'temp': 25.5, 'time': '2024-01-01 12:00:00'},
        {'id': 'STATION_B', 'lat': 34.1, 'lon': -118.2, 'temp': 28.2, 'time': '2024-01-01 12:00:00'},
        
        # Missing coordinates
        {'id': 'STATION_C', 'lat': None, 'lon': -95.4, 'temp': 30.1, 'time': '2024-01-01 12:00:00'},
        {'id': 'STATION_D', 'lat': 29.8, 'lon': '', 'temp': 32.5, 'time': '2024-01-01 12:00:00'},
        
        # Invalid temperatures
        {'id': 'STATION_A', 'lat': 40.7, 'lon': -74.0, 'temp': -999, 'time': '2024-01-01 13:00:00'},
        {'id': 'STATION_B', 'lat': 34.1, 'lon': -118.2, 'temp': 'invalid', 'time': '2024-01-01 13:00:00'},
        
        # Duplicate records
        {'id': 'STATION_A', 'lat': 40.7, 'lon': -74.0, 'temp': 26.0, 'time': '2024-01-01 14:00:00'},
        {'id': 'STATION_A', 'lat': 40.7, 'lon': -74.0, 'temp': 26.0, 'time': '2024-01-01 14:00:00'},
        
        # Invalid coordinates
        {'id': 'STATION_E', 'lat': 95.0, 'lon': -74.0, 'temp': 22.0, 'time': '2024-01-01 14:00:00'},
        {'id': 'STATION_F', 'lat': 40.7, 'lon': -200.0, 'temp': 24.0, 'time': '2024-01-01 14:00:00'},
        
        # Invalid timestamps
        {'id': 'STATION_A', 'lat': 40.7, 'lon': -74.0, 'temp': 27.0, 'time': 'invalid_date'},
        {'id': 'STATION_B', 'lat': 34.1, 'lon': -118.2, 'temp': 29.0, 'time': ''},
        
        # More good data
        {'id': 'STATION_A', 'lat': 40.7, 'lon': -74.0, 'temp': 24.8, 'time': '2024-01-01 15:00:00'},
        {'id': 'STATION_B', 'lat': 34.1, 'lon': -118.2, 'temp': 27.5, 'time': '2024-01-01 15:00:00'},
    ]
    
    df = pd.DataFrame(messy_data)
    df.to_csv('messy_weather_data.csv', index=False)
    print(f"Created messy_weather_data.csv with {len(df)} records")
    return df


def create_daily_temperature_data():
    """Create daily temperature data for aggregation exercises"""
    
    # Generate daily data for a full year
    dates = pd.date_range('2024-01-01', '2024-12-31', freq='D')
    
    data = []
    for date in dates:
        # Create seasonal temperature pattern
        day_of_year = date.timetuple().tm_yday
        seasonal_temp = 15 + 20 * np.sin(2 * np.pi * (day_of_year - 80) / 365)
        
        # Add random variation
        daily_temp = seasonal_temp + np.random.normal(0, 5)
        
        data.append({
            'date': date.strftime('%Y-%m-%d'),
            'temperature': daily_temp,
            'station': 'MAIN_STATION'
        })
    
    df = pd.DataFrame(data)
    df.to_csv('daily_temperatures.csv', index=False)
    print(f"Created daily_temperatures.csv with {len(df)} records")
    return df


def create_station_metadata():
    """Create station metadata for geospatial exercises"""
    
    stations = [
        # West Coast
        {'id': 'CA_SF', 'name': 'San Francisco', 'lat': 37.7749, 'lon': -122.4194, 'elevation': 16},
        {'id': 'CA_LA', 'name': 'Los Angeles', 'lat': 34.0522, 'lon': -118.2437, 'elevation': 71},
        {'id': 'OR_PDX', 'name': 'Portland', 'lat': 45.5152, 'lon': -122.6784, 'elevation': 15},
        {'id': 'WA_SEA', 'name': 'Seattle', 'lat': 47.6062, 'lon': -122.3321, 'elevation': 56},
        
        # East Coast
        {'id': 'NY_NYC', 'name': 'New York', 'lat': 40.7128, 'lon': -74.0060, 'elevation': 10},
        {'id': 'FL_MIA', 'name': 'Miami', 'lat': 25.7617, 'lon': -80.1918, 'elevation': 2},
        {'id': 'MA_BOS', 'name': 'Boston', 'lat': 42.3601, 'lon': -71.0589, 'elevation': 43},
        
        # Central
        {'id': 'TX_HOU', 'name': 'Houston', 'lat': 29.7604, 'lon': -95.3698, 'elevation': 13},
        {'id': 'IL_CHI', 'name': 'Chicago', 'lat': 41.8781, 'lon': -87.6298, 'elevation': 181},
        {'id': 'CO_DEN', 'name': 'Denver', 'lat': 39.7392, 'lon': -104.9903, 'elevation': 1609},
        
        # Some stations with missing data
        {'id': 'MISSING_1', 'name': 'Unknown Location', 'lat': None, 'lon': -100.0, 'elevation': 500},
        {'id': 'MISSING_2', 'name': 'Partial Data', 'lat': 35.0, 'lon': None, 'elevation': 200},
    ]
    
    df = pd.DataFrame(stations)
    df.to_csv('station_metadata.csv', index=False)
    
    # Also save as JSON for different format practice
    with open('station_metadata.json', 'w') as f:
        json.dump(stations, f, indent=2)
    
    print(f"Created station_metadata.csv and .json with {len(df)} stations")
    return df


def create_sample_config():
    """Create sample configuration for exercises"""
    
    config = {
        "regions": {
            "west_coast": {
                "name": "West Coast",
                "bounds": {
                    "north": 49.0,
                    "south": 32.0,
                    "west": -125.0,
                    "east": -115.0
                }
            },
            "east_coast": {
                "name": "East Coast",
                "bounds": {
                    "north": 45.0,
                    "south": 25.0,
                    "west": -85.0,
                    "east": -65.0
                }
            },
            "central": {
                "name": "Central US",
                "bounds": {
                    "north": 45.0,
                    "south": 25.0,
                    "west": -115.0,
                    "east": -85.0
                }
            }
        },
        "temperature_thresholds": {
            "extreme_hot": 35.0,
            "hot": 30.0,
            "warm": 25.0,
            "cool": 15.0,
            "cold": 5.0,
            "extreme_cold": -10.0
        },
        "data_quality": {
            "temp_min": -50.0,
            "temp_max": 60.0,
            "missing_data_threshold": 0.1
        }
    }
    
    with open('sample_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("Created sample_config.json")
    return config


if __name__ == "__main__":
    print("Creating test data files for interview practice...")
    print("=" * 50)
    
    # Create all test data files
    create_weather_stations_csv()
    create_messy_data_csv()
    create_daily_temperature_data()
    create_station_metadata()
    create_sample_config()
    
    print("\nTest data files created successfully!")
    print("\nFiles created:")
    print("- weather_stations_data.csv (realistic hourly weather data)")
    print("- messy_weather_data.csv (data with quality issues)")
    print("- daily_temperatures.csv (daily temperature time series)")
    print("- station_metadata.csv/.json (station information)")
    print("- sample_config.json (configuration file)")
    
    print("\nUse these files to practice the exercises in interview_exercises.py!")
