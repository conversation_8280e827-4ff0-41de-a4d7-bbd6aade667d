{"version": 3, "sources": ["../src/index.tsx"], "sourcesContent": ["import path from \"node:path\"\nimport { Box, Text, useApp } from \"ink\"\nimport <PERSON>Tex<PERSON> from \"ink-big-text\"\nimport Gradient from \"ink-gradient\"\nimport { useEffect, useMemo } from \"react\"\nimport { ExplorerStatus } from \"./components/ExplorerStatus.js\"\nimport { ProgressBar } from \"./components/ProgressBar.js\"\nimport Table from \"./components/Table.js\"\nimport { WorkersStatus } from \"./components/WorkerStatus.js\"\nimport { useProcessor } from \"./hooks/use-processor.js\"\nimport type { DB } from \"./lib/database.js\"\nimport type { FileGroup, Language } from \"./lib/types.js\"\n\nconst outputPath = (inputDir: string, outputDir: string, lang: Language) =>\n  path.relative(inputDir, path.join(outputDir, `${lang}.md`))\n\nexport type IndexProps = {\n  fileGroups: Map<Language, FileGroup[]>\n  inputDir: string\n  outputDir: string\n  model: string\n  concurrency: number\n  total: number\n  database: DB\n  debug?: boolean\n}\n\nexport const Index: React.FC<IndexProps> = ({\n  fileGroups,\n  inputDir,\n  outputDir,\n  model,\n  concurrency,\n  total,\n  database,\n  debug,\n}) => {\n  const { exit } = useApp()\n  const { run, workers, queuedTasks, progress, results, error, reset } = useProcessor({\n    model,\n    concurrency,\n    fileGroups,\n    inputDir,\n    outputDir,\n    database,\n  })\n\n  useEffect(() => {\n    run()\n  }, [run])\n\n  // Exit when complete or on error\n  useEffect(() => {\n    if (results != null || error != null) {\n      // Reset processor before exiting\n      database.close()\n      reset()\n      exit()\n    }\n  }, [results, error, exit, reset, database])\n\n  const resultsViewModel = useMemo(\n    () =>\n      results == null\n        ? []\n        : Array.from(results.keys())\n            .map(language => ({ language, path: outputPath(inputDir, outputDir, language) }))\n            .toSorted((a, b) => a.language.localeCompare(b.language)),\n    [results, inputDir, outputDir],\n  )\n\n  return (\n    <Box flexDirection=\"column\" paddingY={1}>\n      <Gradient name=\"retro\">\n        <BigText text=\"context42\" />\n      </Gradient>\n\n      {results != null ? (\n        <>\n          <Box marginTop={1}>\n            <Text color=\"green\">✓ Style guides generated successfully!</Text>\n          </Box>\n          <Table data={resultsViewModel} columnWidths={{ language: 16, path: 32 }} />\n        </>\n      ) : error != null ? (\n        <>\n          <ExplorerStatus fileGroups={fileGroups} isLoading={false} />\n          <Box marginTop={1}>\n            <Text color=\"red\">✗ Error: {error}</Text>\n            {debug && <Text dimColor>Run ID: {database.runId}</Text>}\n          </Box>\n        </>\n      ) : (\n        <>\n          <ProgressBar value={progress} max={total} label=\"files\" />\n          <WorkersStatus workers={workers} inputDir={inputDir} queuedTasks={queuedTasks} />\n        </>\n      )}\n    </Box>\n  )\n}\n"], "mappings": "AAAA,OAAO,UAAU;AACjB,SAAS,KAAK,MAAM,cAAc;AAClC,OAAO,aAAa;AACpB,OAAO,cAAc;AACrB,SAAS,WAAW,eAAe;AACnC,SAAS,sBAAsB;AAC/B,SAAS,mBAAmB;AAC5B,OAAO,WAAW;AAClB,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;AAiErB,SAIA,UAJA,KAIA,YAJA;AA7DR,MAAM,aAAa,CAAC,UAAkB,WAAmB,SACvD,KAAK,SAAS,UAAU,KAAK,KAAK,WAAW,GAAG,IAAI,KAAK,CAAC;AAarD,MAAM,QAA8B,CAAC;AAAA,EAC1C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,KAAK,IAAI,OAAO;AACxB,QAAM,EAAE,KAAK,SAAS,aAAa,UAAU,SAAS,OAAO,MAAM,IAAI,aAAa;AAAA,IAClF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,YAAU,MAAM;AACd,QAAI;AAAA,EACN,GAAG,CAAC,GAAG,CAAC;AAGR,YAAU,MAAM;AACd,QAAI,WAAW,QAAQ,SAAS,MAAM;AAEpC,eAAS,MAAM;AACf,YAAM;AACN,WAAK;AAAA,IACP;AAAA,EACF,GAAG,CAAC,SAAS,OAAO,MAAM,OAAO,QAAQ,CAAC;AAE1C,QAAM,mBAAmB;AAAA,IACvB,MACE,WAAW,OACP,CAAC,IACD,MAAM,KAAK,QAAQ,KAAK,CAAC,EACtB,IAAI,eAAa,EAAE,UAAU,MAAM,WAAW,UAAU,WAAW,QAAQ,EAAE,EAAE,EAC/E,SAAS,CAAC,GAAG,MAAM,EAAE,SAAS,cAAc,EAAE,QAAQ,CAAC;AAAA,IAChE,CAAC,SAAS,UAAU,SAAS;AAAA,EAC/B;AAEA,SACE,qBAAC,OAAI,eAAc,UAAS,UAAU,GACpC;AAAA,wBAAC,YAAS,MAAK,SACb,8BAAC,WAAQ,MAAK,aAAY,GAC5B;AAAA,IAEC,WAAW,OACV,iCACE;AAAA,0BAAC,OAAI,WAAW,GACd,8BAAC,QAAK,OAAM,SAAQ,yDAAsC,GAC5D;AAAA,MACA,oBAAC,SAAM,MAAM,kBAAkB,cAAc,EAAE,UAAU,IAAI,MAAM,GAAG,GAAG;AAAA,OAC3E,IACE,SAAS,OACX,iCACE;AAAA,0BAAC,kBAAe,YAAwB,WAAW,OAAO;AAAA,MAC1D,qBAAC,OAAI,WAAW,GACd;AAAA,6BAAC,QAAK,OAAM,OAAM;AAAA;AAAA,UAAU;AAAA,WAAM;AAAA,QACjC,SAAS,qBAAC,QAAK,UAAQ,MAAC;AAAA;AAAA,UAAS,SAAS;AAAA,WAAM;AAAA,SACnD;AAAA,OACF,IAEA,iCACE;AAAA,0BAAC,eAAY,OAAO,UAAU,KAAK,OAAO,OAAM,SAAQ;AAAA,MACxD,oBAAC,iBAAc,SAAkB,UAAoB,aAA0B;AAAA,OACjF;AAAA,KAEJ;AAEJ;", "names": []}