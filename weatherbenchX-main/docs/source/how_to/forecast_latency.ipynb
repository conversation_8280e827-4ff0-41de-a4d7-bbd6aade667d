{"cells": [{"cell_type": "markdown", "id": "4b18acad-4389-45b1-8fe5-b5f960eb82a4", "metadata": {}, "source": ["# Handle forecast latency"]}, {"cell_type": "markdown", "id": "5c9e846f-0439-4151-ba08-990acc524bb8", "metadata": {}, "source": ["Some predictions have a certain latency. For example, HRES forecasts are typically only available just under 6h after the nominal init_time, so the 6UTC run will be released just before 12UTC. This means that lead_time <6h on the prediction files are actually never available in real time, in other words the corresponding valid times would always be in the past. (Valid time is the time for which the forecast is made, so a forecast initialized at 00UTC with a lead time of 6h would have a valid time at 06UTC.)\n", "\n", "To adjust the evaluation to the \"operational\" setting where only actually available forecasts are evaluated, we can use latency wrappers.\n", "\n", "These latency wrappers, look up the most recently available forecast for a given \"query\" init_time and adjust the lead_time on file to the \"query\" lead time.\n", "\n", "Example: For a query init_time of 21UTC and a query lead_time of 3h (with a valid_time of 00UTC), the most recently available HRES forecast would be the 12UTC run with a lead_time of 12h."]}, {"cell_type": "code", "execution_count": 1, "id": "76d692aa-5b00-4a78-8ba1-9882d673cc6e", "metadata": {}, "outputs": [], "source": ["# IMPORTANT: If you are running this on Colab, uncomment the cell below to access the cloud datasets.\n", "# from google.colab import auth\n", "# auth.authenticate_user()"]}, {"cell_type": "code", "execution_count": 2, "id": "2a6af8f6-efde-4cf4-bcd3-c4e422ad07b7", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from weatherbenchX.data_loaders import xarray_loaders\n", "from weatherbenchX.data_loaders import latency_wrappers"]}, {"cell_type": "code", "execution_count": 3, "id": "59e67c85-bd76-45fe-bf38-633868fca02a", "metadata": {}, "outputs": [], "source": ["prediction_path = 'gs://weatherbench2/datasets/hres/2016-2022-0012-64x32_equiangular_conservative.zarr'"]}, {"cell_type": "code", "execution_count": 4, "id": "3b74eb15-d622-469a-81cf-971854449743", "metadata": {}, "outputs": [], "source": ["variables = ['2m_temperature', 'geopotential']\n", "prediction_data_loader = xarray_loaders.PredictionsFromXarray(\n", "    path=prediction_path,\n", "    variables=variables,\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "b9f06752-084d-441f-bcbd-bd2d14c76b21", "metadata": {}, "outputs": [], "source": ["init_times = np.array(['2020-01-01T21'], dtype='datetime64[ns]')\n", "lead_times = np.array([3], dtype='timedelta64[h]').astype('timedelta64[ns]')"]}, {"cell_type": "code", "execution_count": 6, "id": "2bcac9c0-349d-4d85-8b65-c1c26c9a9146", "metadata": {}, "outputs": [], "source": ["prediction_data_loader = latency_wrappers.XarrayConstantLatencyWrapper(\n", "    prediction_data_loader, latency=np.timedelta64(6, 'h')\n", ")"]}, {"cell_type": "code", "execution_count": 7, "id": "26312dcd-825e-4320-872f-7b9ad8299aef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2020-01-01T12:00:00.000000000 [12]\n"]}], "source": ["prediction_chunk = prediction_data_loader.load_chunk(init_times, lead_times)"]}, {"cell_type": "code", "execution_count": 8, "id": "bf8f57e3-d4b9-4158-bc8d-1d957b84571e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 116kB\n", "Dimensions:         (latitude: 32, longitude: 64, init_time: 1, lead_time: 1,\n", "                     level: 13)\n", "Coordinates:\n", "  * latitude        (latitude) float64 256B -87.19 -81.56 -75.94 ... 81.56 87.19\n", "  * longitude       (longitude) float64 512B 0.0 5.625 11.25 ... 348.8 354.4\n", "  * init_time       (init_time) datetime64[ns] 8B 2020-01-01T21:00:00\n", "  * lead_time       (lead_time) timedelta64[ns] 8B 03:00:00\n", "  * level           (level) int32 52B 50 100 150 200 250 ... 700 ************\n", "Data variables:\n", "    2m_temperature  (init_time, lead_time, longitude, latitude) float32 8kB 2...\n", "    geopotential    (init_time, lead_time, level, longitude, latitude) float32 106kB ...\n", "Attributes:\n", "    long_name:      2 metre temperature\n", "    short_name:     t2m\n", "    standard_name:  unknown\n", "    units:          K</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-d9f5e8ab-3187-4e7d-947d-ebe4ac0d76f6' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-d9f5e8ab-3187-4e7d-947d-ebe4ac0d76f6' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>latitude</span>: 32</li><li><span class='xr-has-index'>longitude</span>: 64</li><li><span class='xr-has-index'>init_time</span>: 1</li><li><span class='xr-has-index'>lead_time</span>: 1</li><li><span class='xr-has-index'>level</span>: 13</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-9588b21c-24bc-4a7f-a1cc-6e9b54a3c17f' class='xr-section-summary-in' type='checkbox'  checked><label for='section-9588b21c-24bc-4a7f-a1cc-6e9b54a3c17f' class='xr-section-summary' >Coordinates: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>latitude</span></div><div class='xr-var-dims'>(latitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>-87.19 -81.56 ... 81.56 87.19</div><input id='attrs-eb3bc3f9-afd8-4a91-8b48-010421c2b3ba' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-eb3bc3f9-afd8-4a91-8b48-010421c2b3ba' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a72a8c36-622e-43ce-94d1-f9fbb6f5b3ba' class='xr-var-data-in' type='checkbox'><label for='data-a72a8c36-622e-43ce-94d1-f9fbb6f5b3ba' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([-87.1875, -81.5625, -75.9375, -70.3125, -64.6875, -59.0625, -53.4375,\n", "       -47.8125, -42.1875, -36.5625, -30.9375, -25.3125, -19.6875, -14.0625,\n", "        -8.4375,  -2.8125,   2.8125,   8.4375,  14.0625,  19.6875,  25.3125,\n", "        30.9375,  36.5625,  42.1875,  47.8125,  53.4375,  59.0625,  64.6875,\n", "        70.3125,  75.9375,  81.5625,  87.1875])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>longitude</span></div><div class='xr-var-dims'>(longitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>0.0 5.625 11.25 ... 348.8 354.4</div><input id='attrs-0e0351cd-bbd9-4a40-a484-ed9b3bafce1d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-0e0351cd-bbd9-4a40-a484-ed9b3bafce1d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-15254a88-bc56-420c-a37a-d61aa2de3e44' class='xr-var-data-in' type='checkbox'><label for='data-15254a88-bc56-420c-a37a-d61aa2de3e44' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  0.   ,   5.625,  11.25 ,  16.875,  22.5  ,  28.125,  33.75 ,  39.375,\n", "        45.   ,  50.625,  56.25 ,  61.875,  67.5  ,  73.125,  78.75 ,  84.375,\n", "        90.   ,  95.625, 101.25 , 106.875, 112.5  , 118.125, 123.75 , 129.375,\n", "       135.   , 140.625, 146.25 , 151.875, 157.5  , 163.125, 168.75 , 174.375,\n", "       180.   , 185.625, 191.25 , 196.875, 202.5  , 208.125, 213.75 , 219.375,\n", "       225.   , 230.625, 236.25 , 241.875, 247.5  , 253.125, 258.75 , 264.375,\n", "       270.   , 275.625, 281.25 , 286.875, 292.5  , 298.125, 303.75 , 309.375,\n", "       315.   , 320.625, 326.25 , 331.875, 337.5  , 343.125, 348.75 , 354.375])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>init_time</span></div><div class='xr-var-dims'>(init_time)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2020-01-01T21:00:00</div><input id='attrs-cc3f1aec-1a06-4212-afce-8beb8658fa35' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-cc3f1aec-1a06-4212-afce-8beb8658fa35' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-4940b470-3229-4ae2-984a-6084ba8244bb' class='xr-var-data-in' type='checkbox'><label for='data-4940b470-3229-4ae2-984a-6084ba8244bb' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2020-01-01T21:00:00.000000000&#x27;], dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>lead_time</span></div><div class='xr-var-dims'>(lead_time)</div><div class='xr-var-dtype'>timedelta64[ns]</div><div class='xr-var-preview xr-preview'>03:00:00</div><input id='attrs-7a6067e6-dd2f-4c28-aefc-65fd0d6ebb82' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-7a6067e6-dd2f-4c28-aefc-65fd0d6ebb82' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-4cb8322f-879b-4c41-a3ff-202b688fb196' class='xr-var-data-in' type='checkbox'><label for='data-4cb8322f-879b-4c41-a3ff-202b688fb196' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([10800000000000], dtype=&#x27;timedelta64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>level</span></div><div class='xr-var-dims'>(level)</div><div class='xr-var-dtype'>int32</div><div class='xr-var-preview xr-preview'>50 100 150 200 ... 700 ************</div><input id='attrs-c76d690a-feeb-49a3-80c0-7000c08f83c1' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c76d690a-feeb-49a3-80c0-7000c08f83c1' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-92b3262d-8a48-4c8c-a21a-2c8ac43bb939' class='xr-var-data-in' type='checkbox'><label for='data-92b3262d-8a48-4c8c-a21a-2c8ac43bb939' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  50,  100,  150,  200,  250,  300,  400,  500,  600,  700,  850,  925,\n", "       1000], dtype=int32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-e88e4405-dac5-481c-9025-d051913b7bd4' class='xr-section-summary-in' type='checkbox'  checked><label for='section-e88e4405-dac5-481c-9025-d051913b7bd4' class='xr-section-summary' >Data variables: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>2m_temperature</span></div><div class='xr-var-dims'>(init_time, lead_time, longitude, latitude)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>248.0 249.5 244.8 ... 254.1 245.9</div><input id='attrs-94f19692-eaff-4dc0-bd96-1ea5bd8de719' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-94f19692-eaff-4dc0-bd96-1ea5bd8de719' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-59839a7b-f72a-4e4d-bf52-0542de1bb58d' class='xr-var-data-in' type='checkbox'><label for='data-59839a7b-f72a-4e4d-bf52-0542de1bb58d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>2 metre temperature</dd><dt><span>short_name :</span></dt><dd>t2m</dd><dt><span>standard_name :</span></dt><dd>unknown</dd><dt><span>units :</span></dt><dd>K</dd></dl></div><div class='xr-var-data'><pre>array([[[[247.99323, 249.50836, 244.81491, ..., 272.80756, 255.0918 ,\n", "          245.52582],\n", "         [247.81555, 248.20906, 242.49106, ..., 273.18204, 255.89404,\n", "          245.04214],\n", "         [247.8318 , 246.8538 , 240.08238, ..., 270.55347, 254.0717 ,\n", "          244.43997],\n", "         ...,\n", "         [248.91452, 254.68246, 259.32538, ..., 253.1698 , 248.35475,\n", "          246.54271],\n", "         [248.52327, 251.92426, 252.99542, ..., 262.19604, 252.18025,\n", "          246.21764],\n", "         [248.09544, 250.4874 , 247.31923, ..., 269.40958, 254.05913,\n", "          245.89488]]]], dtype=float32)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>geopotential</span></div><div class='xr-var-dims'>(init_time, lead_time, level, longitude, latitude)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>2.016e+05 2.012e+05 ... 473.8</div><input id='attrs-9c0b69a7-06b5-4c2d-bb8d-eb38cf117bd9' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-9c0b69a7-06b5-4c2d-bb8d-eb38cf117bd9' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-c8e1b5d6-5f7e-405e-8bb7-6e6369fb7409' class='xr-var-data-in' type='checkbox'><label for='data-c8e1b5d6-5f7e-405e-8bb7-6e6369fb7409' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>Geopotential</dd><dt><span>short_name :</span></dt><dd>z</dd><dt><span>standard_name :</span></dt><dd>geopotential</dd><dt><span>units :</span></dt><dd>m**2 s**-2</dd></dl></div><div class='xr-var-data'><pre>array([[[[[ 2.01602750e+05,  2.01209344e+05,  2.00841781e+05, ...,\n", "            1.86233250e+05,  1.85196375e+05,  1.85214250e+05],\n", "          [ 2.01600484e+05,  2.01196891e+05,  2.00839328e+05, ...,\n", "            1.86192641e+05,  1.85166484e+05,  1.85193578e+05],\n", "          [ 2.01602578e+05,  2.01199219e+05,  2.00856219e+05, ...,\n", "            1.86129781e+05,  1.85136266e+05,  1.85177766e+05],\n", "          ...,\n", "          [ 2.01626344e+05,  2.01287984e+05,  2.01008453e+05, ...,\n", "            1.86292594e+05,  1.85304094e+05,  1.85298484e+05],\n", "          [ 2.01615562e+05,  2.01252250e+05,  2.00928734e+05, ...,\n", "            1.86277625e+05,  1.85258297e+05,  1.85267078e+05],\n", "          [ 2.01608234e+05,  2.01228219e+05,  2.00868844e+05, ...,\n", "            1.86258234e+05,  1.85225203e+05,  1.85239562e+05]],\n", "\n", "         [[ 1.55005500e+05,  1.54755500e+05,  1.54510719e+05, ...,\n", "            1.47136453e+05,  1.46068031e+05,  1.46036312e+05],\n", "          [ 1.55002031e+05,  1.54731422e+05,  1.54482609e+05, ...,\n", "            1.47177297e+05,  1.46088594e+05,  1.46047703e+05],\n", "          [ 1.55002594e+05,  1.54721812e+05,  1.54495422e+05, ...,\n", "            1.47173047e+05,  1.46096750e+05,  1.46062859e+05],\n", "...\n", "            4.61496484e+03,  5.85730518e+03,  6.11057812e+03],\n", "          [ 5.50676904e+03,  5.34689307e+03,  5.19966602e+03, ...,\n", "            4.18022461e+03,  5.71078955e+03,  6.07124756e+03],\n", "          [ 5.52548193e+03,  5.39502148e+03,  5.26803027e+03, ...,\n", "            4.27636426e+03,  5.65259082e+03,  6.02839551e+03]],\n", "\n", "         [[-4.30662476e+02, -4.98497650e+02, -6.36040283e+02, ...,\n", "           -1.59732471e+03, -8.44463501e+01,  4.16011841e+02],\n", "          [-4.04746796e+02, -4.47732910e+02, -6.25104431e+02, ...,\n", "           -1.34803528e+03, -1.34425674e+02,  3.58913910e+02],\n", "          [-3.83980865e+02, -3.82793640e+02, -6.12269226e+02, ...,\n", "           -1.06238086e+03, -1.72044617e+02,  3.00715088e+02],\n", "          ...,\n", "          [-4.76181213e+02, -6.91664734e+02, -8.76616943e+02, ...,\n", "           -1.02120447e+03,  3.02631165e+02,  5.89108459e+02],\n", "          [-4.69195312e+02, -6.09610718e+02, -8.19463867e+02, ...,\n", "           -1.69832202e+03,  1.05783974e+02,  5.32890869e+02],\n", "          [-4.54145996e+02, -5.66205994e+02, -7.47517029e+02, ...,\n", "           -1.75474609e+03, -1.51596117e+01,  4.73814758e+02]]]]],\n", "      dtype=float32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-ff3f119f-c0c4-4ee6-9324-e970fc6260d7' class='xr-section-summary-in' type='checkbox'  ><label for='section-ff3f119f-c0c4-4ee6-9324-e970fc6260d7' class='xr-section-summary' >Indexes: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>latitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-c6fd77da-82a6-4e63-9120-513c10c6dfd1' class='xr-index-data-in' type='checkbox'/><label for='index-c6fd77da-82a6-4e63-9120-513c10c6dfd1' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([ -87.18750000000003,  -81.56250000000001,            -75.9375,\n", "        -70.31249999999999,  -64.68750000000001,            -59.0625,\n", "                  -53.4375,            -47.8125,            -42.1875,\n", "                  -36.5625, -30.937499999999996, -25.312500000000004,\n", "       -19.687499999999996, -14.062499999999991,  -8.437499999999996,\n", "        -2.812500000000003,   2.812500000000003,   8.437500000000009,\n", "        14.062500000000004,  19.687499999999996,  25.312500000000004,\n", "         30.93750000000001,  36.562499999999986,             42.1875,\n", "                   47.8125,             53.4375,  59.062500000000014,\n", "         64.68750000000001,             70.3125,             75.9375,\n", "         81.56249999999997,   87.18750000000003],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;latitude&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>longitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-5c2c10e3-3c86-4eda-be1b-1dd27fa389d2' class='xr-index-data-in' type='checkbox'/><label for='index-5c2c10e3-3c86-4eda-be1b-1dd27fa389d2' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([               0.0,              5.625,              11.25,\n", "                   16.875,               22.5,             28.125,\n", "                    33.75,             39.375,               45.0,\n", "                   50.625,              56.25,  61.87499999999999,\n", "                     67.5,             73.125,              78.75,\n", "                   84.375,               90.0,             95.625,\n", "                   101.25,            106.875,              112.5,\n", "                  118.125, 123.74999999999999,            129.375,\n", "                    135.0,            140.625,             146.25,\n", "                  151.875,              157.5,            163.125,\n", "                   168.75,            174.375,              180.0,\n", "                  185.625,             191.25,            196.875,\n", "                    202.5,            208.125,             213.75,\n", "                  219.375,              225.0, 230.62499999999997,\n", "                   236.25,            241.875, 247.49999999999997,\n", "                  253.125,             258.75,            264.375,\n", "                    270.0,            275.625,             281.25,\n", "                  286.875,              292.5,            298.125,\n", "                   303.75,            309.375,              315.0,\n", "                  320.625,             326.25,            331.875,\n", "                    337.5,            343.125,             348.75,\n", "                  354.375],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;longitude&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>init_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-d3d4ab4c-3670-4014-8e42-1eea4f8adac6' class='xr-index-data-in' type='checkbox'/><label for='index-d3d4ab4c-3670-4014-8e42-1eea4f8adac6' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2020-01-01 21:00:00&#x27;], dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;init_time&#x27;, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>lead_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-55ce4e10-1311-4b9f-9a13-6aabd3a11180' class='xr-index-data-in' type='checkbox'/><label for='index-55ce4e10-1311-4b9f-9a13-6aabd3a11180' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(TimedeltaIndex([&#x27;0 days 03:00:00&#x27;], dtype=&#x27;timedelta64[ns]&#x27;, name=&#x27;lead_time&#x27;, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>level</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-1e3f492b-1851-41a6-b4d8-18ba0a900fc7' class='xr-index-data-in' type='checkbox'/><label for='index-1e3f492b-1851-41a6-b4d8-18ba0a900fc7' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([50, 100, 150, 200, 250, 300, 400, 500, 600, 700, 850, 925, 1000], dtype=&#x27;int32&#x27;, name=&#x27;level&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-1b29d9f8-4e88-4bb3-b3c6-81ca95bee323' class='xr-section-summary-in' type='checkbox'  checked><label for='section-1b29d9f8-4e88-4bb3-b3c6-81ca95bee323' class='xr-section-summary' >Attributes: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>2 metre temperature</dd><dt><span>short_name :</span></dt><dd>t2m</dd><dt><span>standard_name :</span></dt><dd>unknown</dd><dt><span>units :</span></dt><dd>K</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 116kB\n", "Dimensions:         (latitude: 32, longitude: 64, init_time: 1, lead_time: 1,\n", "                     level: 13)\n", "Coordinates:\n", "  * latitude        (latitude) float64 256B -87.19 -81.56 -75.94 ... 81.56 87.19\n", "  * longitude       (longitude) float64 512B 0.0 5.625 11.25 ... 348.8 354.4\n", "  * init_time       (init_time) datetime64[ns] 8B 2020-01-01T21:00:00\n", "  * lead_time       (lead_time) timedelta64[ns] 8B 03:00:00\n", "  * level           (level) int32 52B 50 100 150 200 250 ... 700 ************\n", "Data variables:\n", "    2m_temperature  (init_time, lead_time, longitude, latitude) float32 8kB 2...\n", "    geopotential    (init_time, lead_time, level, longitude, latitude) float32 106kB ...\n", "Attributes:\n", "    long_name:      2 metre temperature\n", "    short_name:     t2m\n", "    standard_name:  unknown\n", "    units:          K"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["prediction_chunk"]}, {"cell_type": "markdown", "id": "25b0d867-03b8-4f21-9980-3069bd8b7a8f", "metadata": {}, "source": ["As you can see from the printed time stamps above, the forecast read from file was the 12UTC + 12h forecast but the returned init/lead_times are the \"query times\".\n", "\n", "Note that for Zarr files, the available nominal init_times are directly read from the Xarray coordinate. For other, non-Xarray data loaders, use `ConstantLatencyWrapper` and explicitly specify the available nominal init_times.\n", "\n", "Sometimes there are cases, where different forecasts are split across datasets, e.g. 00/12UTC and 06/18UTC files. `MultipleConstantLatencyWrapper` allows combining several latency wrappers and will pick the most recently available forecast across all files."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}