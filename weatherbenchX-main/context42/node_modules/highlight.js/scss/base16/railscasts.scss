pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Railscasts
  Author: <PERSON> (http://railscasts.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme railscasts
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #2b2b2b  Default Background
base01  #272935  Lighter Background (Used for status bars, line number and folding marks)
base02  #3a4055  Selection Background
base03  #5a647e  Comments, Invisibles, Line Highlighting
base04  #d4cfc9  Dark Foreground (Used for status bars)
base05  #e6e1dc  Default Foreground, Caret, Delimiters, Operators
base06  #f4f1ed  Light Foreground (Not often used)
base07  #f9f7f3  Light Background (Not often used)
base08  #da4939  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #cc7833  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ffc66d  Classes, Markup Bold, Search Text Background
base0B  #a5c261  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #519f50  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #6d9cbe  Functions, Methods, Attribute IDs, Headings
base0E  #b6b3eb  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #bc9458  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #e6e1dc;
  background: #2b2b2b
}
.hljs::selection,
.hljs ::selection {
  background-color: #3a4055;
  color: #e6e1dc
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #5a647e -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #5a647e
}
/* base04 - #d4cfc9 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #d4cfc9
}
/* base05 - #e6e1dc -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #e6e1dc
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #da4939
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #cc7833
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ffc66d
}
.hljs-strong {
  font-weight: bold;
  color: #ffc66d
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #a5c261
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #519f50
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #6d9cbe
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #b6b3eb
}
.hljs-emphasis {
  color: #b6b3eb;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #bc9458
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}