{"version": 3, "sources": ["../../src/lib/database.ts"], "sourcesContent": ["import { randomUUID } from \"node:crypto\"\nimport { mkdirSync } from \"node:fs\"\nimport { relative } from \"node:path\"\nimport { DatabaseSync } from \"node:sqlite\"\nimport type { StyleGuide } from \"./types.js\"\n\nexport class DB {\n  private database: DatabaseSync\n  runId: string\n\n  constructor(dbPath: string, existingRunId?: string) {\n    // Create directory if needed (for file-based databases)\n    if (dbPath !== \":memory:\") {\n      const dir = dbPath.substring(0, dbPath.lastIndexOf(\"/\"))\n      if (dir) {\n        mkdirSync(dir, { recursive: true })\n      }\n\n      process.on(\"exit\", () => this.close())\n      process.on(\"SIGINT\", () => this.close())\n      process.on(\"SIGTERM\", () => this.close())\n    }\n\n    this.runId = existingRunId || randomUUID()\n    this.database = new DatabaseSync(dbPath)\n\n    this.init()\n  }\n\n  init = (): void => {\n    // Create tables if they don't exist\n    this.database.exec(`\n      CREATE TABLE IF NOT EXISTS responses (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        run_id TEXT NOT NULL,\n        result TEXT NOT NULL,\n        created_at DATETIME NOT NULL\n      );\n      CREATE INDEX IF NOT EXISTS idx_responses_lookup ON responses(run_id, created_at DESC);\n\n      CREATE TABLE IF NOT EXISTS style_guides (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        run_id TEXT NOT NULL,\n        language TEXT NOT NULL,\n        content TEXT NOT NULL,\n        directory TEXT NOT NULL,\n        created_at DATETIME NOT NULL,\n        updated_at DATETIME NOT NULL\n      );\n      CREATE INDEX IF NOT EXISTS idx_style_guides_lookup ON style_guides(run_id, language, directory);\n    `)\n  }\n\n  saveResponse = (result: string): void => {\n    const stmt = this.database.prepare(\"INSERT INTO responses (run_id, result, created_at) VALUES (?, ?, ?)\")\n    stmt.run(this.runId, result, new Date().toISOString())\n  }\n\n  saveStyleGuide = (language: string, content: string, directory: string): void => {\n    const now = new Date().toISOString()\n\n    // Check if style guide exists for this language and directory in current run\n    const existing = this.database\n      .prepare(\"SELECT id FROM style_guides WHERE run_id = ? AND language = ? AND directory = ?\")\n      .get(this.runId, language, directory) as { id: number } | undefined\n\n    if (existing) {\n      // Update existing\n      const stmt = this.database.prepare(\"UPDATE style_guides SET content = ?, updated_at = ? WHERE id = ?\")\n      stmt.run(content, now, existing.id)\n    } else {\n      // Insert new\n      const stmt = this.database.prepare(\n        \"INSERT INTO style_guides (run_id, language, content, directory, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)\",\n      )\n      stmt.run(this.runId, language, content, directory, now, now)\n    }\n  }\n\n  getStyleGuide = (language: string, directory: string): StyleGuide | null => {\n    const stmt = this.database.prepare(\n      \"SELECT * FROM style_guides WHERE run_id = ? AND language = ? AND directory = ? ORDER BY updated_at DESC LIMIT 1\",\n    )\n    const row = stmt.get(this.runId, language, directory) as\n      | {\n          id: number\n          run_id: string\n          language: string\n          content: string\n          directory: string\n          created_at: string\n          updated_at: string\n        }\n      | undefined\n\n    if (!row) return null\n\n    return {\n      id: row.id,\n      project_path: row.directory,\n      language: row.language,\n      content: row.content,\n      created_at: row.created_at,\n    }\n  }\n\n  getChildStyleGuides = (parentDirectory: string, language: string): Array<{ directory: string; content: string }> => {\n    const stmt = this.database.prepare(\n      `SELECT directory, content FROM style_guides\n       WHERE run_id = ? AND language = ? AND directory LIKE ?\n       AND directory != ?\n       ORDER BY directory`,\n    )\n    const rows = stmt.all(this.runId, language, `${parentDirectory}/%`, parentDirectory) as Array<{\n      directory: string\n      content: string\n    }>\n\n    // Filter to only immediate children (no additional slashes after parent)\n    const immediateChildren = rows.filter(row => {\n      const relativePath = row.directory.slice(parentDirectory.length + 1)\n      return !relativePath.includes(\"/\")\n    })\n\n    return immediateChildren.map(row => ({\n      directory: relative(parentDirectory, row.directory),\n      content: row.content,\n    }))\n  }\n\n  close = (): void => {\n    if (this.database?.isOpen) {\n      this.database.close()\n    }\n  }\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAC1B,SAAS,gBAAgB;AACzB,SAAS,oBAAoB;AAGtB,MAAM,GAAG;AAAA,EACN;AAAA,EACR;AAAA,EAEA,YAAY,QAAgB,eAAwB;AAElD,QAAI,WAAW,YAAY;AACzB,YAAM,MAAM,OAAO,UAAU,GAAG,OAAO,YAAY,GAAG,CAAC;AACvD,UAAI,KAAK;AACP,kBAAU,KAAK,EAAE,WAAW,KAAK,CAAC;AAAA,MACpC;AAEA,cAAQ,GAAG,QAAQ,MAAM,KAAK,MAAM,CAAC;AACrC,cAAQ,GAAG,UAAU,MAAM,KAAK,MAAM,CAAC;AACvC,cAAQ,GAAG,WAAW,MAAM,KAAK,MAAM,CAAC;AAAA,IAC1C;AAEA,SAAK,QAAQ,iBAAiB,WAAW;AACzC,SAAK,WAAW,IAAI,aAAa,MAAM;AAEvC,SAAK,KAAK;AAAA,EACZ;AAAA,EAEA,OAAO,MAAY;AAEjB,SAAK,SAAS,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAmBlB;AAAA,EACH;AAAA,EAEA,eAAe,CAAC,WAAyB;AACvC,UAAM,OAAO,KAAK,SAAS,QAAQ,qEAAqE;AACxG,SAAK,IAAI,KAAK,OAAO,SAAQ,oBAAI,KAAK,GAAE,YAAY,CAAC;AAAA,EACvD;AAAA,EAEA,iBAAiB,CAAC,UAAkB,SAAiB,cAA4B;AAC/E,UAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AAGnC,UAAM,WAAW,KAAK,SACnB,QAAQ,iFAAiF,EACzF,IAAI,KAAK,OAAO,UAAU,SAAS;AAEtC,QAAI,UAAU;AAEZ,YAAM,OAAO,KAAK,SAAS,QAAQ,kEAAkE;AACrG,WAAK,IAAI,SAAS,KAAK,SAAS,EAAE;AAAA,IACpC,OAAO;AAEL,YAAM,OAAO,KAAK,SAAS;AAAA,QACzB;AAAA,MACF;AACA,WAAK,IAAI,KAAK,OAAO,UAAU,SAAS,WAAW,KAAK,GAAG;AAAA,IAC7D;AAAA,EACF;AAAA,EAEA,gBAAgB,CAAC,UAAkB,cAAyC;AAC1E,UAAM,OAAO,KAAK,SAAS;AAAA,MACzB;AAAA,IACF;AACA,UAAM,MAAM,KAAK,IAAI,KAAK,OAAO,UAAU,SAAS;AAYpD,QAAI,CAAC,IAAK,QAAO;AAEjB,WAAO;AAAA,MACL,IAAI,IAAI;AAAA,MACR,cAAc,IAAI;AAAA,MAClB,UAAU,IAAI;AAAA,MACd,SAAS,IAAI;AAAA,MACb,YAAY,IAAI;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,sBAAsB,CAAC,iBAAyB,aAAoE;AAClH,UAAM,OAAO,KAAK,SAAS;AAAA,MACzB;AAAA;AAAA;AAAA;AAAA,IAIF;AACA,UAAM,OAAO,KAAK,IAAI,KAAK,OAAO,UAAU,GAAG,eAAe,MAAM,eAAe;AAMnF,UAAM,oBAAoB,KAAK,OAAO,SAAO;AAC3C,YAAM,eAAe,IAAI,UAAU,MAAM,gBAAgB,SAAS,CAAC;AACnE,aAAO,CAAC,aAAa,SAAS,GAAG;AAAA,IACnC,CAAC;AAED,WAAO,kBAAkB,IAAI,UAAQ;AAAA,MACnC,WAAW,SAAS,iBAAiB,IAAI,SAAS;AAAA,MAClD,SAAS,IAAI;AAAA,IACf,EAAE;AAAA,EACJ;AAAA,EAEA,QAAQ,MAAY;AAClB,QAAI,KAAK,UAAU,QAAQ;AACzB,WAAK,SAAS,MAAM;AAAA,IACtB;AAAA,EACF;AACF;", "names": []}