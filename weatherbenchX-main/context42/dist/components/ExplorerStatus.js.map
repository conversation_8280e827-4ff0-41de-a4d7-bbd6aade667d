{"version": 3, "sources": ["../../src/components/ExplorerStatus.tsx"], "sourcesContent": ["import { Box, Text } from \"ink\"\nimport Spinner from \"ink-spinner\"\nimport type React from \"react\"\nimport type { FileGroup, Language } from \"../lib/types.js\"\n\nexport type ExplorerStatusProps = {\n  readonly fileGroups: Map<Language, FileGroup[]>\n  readonly isLoading: boolean\n}\n\nexport const ExplorerStatus: React.FC<ExplorerStatusProps> = ({ fileGroups, isLoading }) => {\n  const foundLanguages = Array.from(fileGroups.keys())\n\n  // Calculate total file count\n  let fileCount = 0\n  const languageCounts: Record<string, number> = {}\n\n  for (const [language, groups] of fileGroups) {\n    const languageFileCount = groups.reduce((sum, g) => sum + g.files.length, 0)\n    languageCounts[language] = languageFileCount\n    fileCount += languageFileCount\n  }\n\n  return (\n    <Box flexDirection=\"column\" marginBottom={1}>\n      <Box gap={1}>\n        {isLoading && (\n          <Text color=\"cyan\">\n            <Spinner type=\"dots\" />\n          </Text>\n        )}\n      </Box>\n      {!isLoading && foundLanguages.length > 0 && (\n        <Box flexDirection=\"column\" marginLeft={2} marginTop={1}>\n          <Text>\n            Found {fileCount} files in {foundLanguages.length} languages\n          </Text>\n          <Box flexDirection=\"column\" marginTop={1}>\n            {Object.entries(languageCounts).map(([language, count]) => (\n              <Text key={language} color=\"gray\">\n                • {language}: {count} files\n              </Text>\n            ))}\n          </Box>\n          <Box flexDirection=\"column\" marginTop={1}>\n            <Text color=\"dim\">Directories:</Text>\n            {[\n              ...new Set(\n                Array.from(fileGroups.values())\n                  .flat()\n                  .map(g => g.directory),\n              ),\n            ].map(dir => (\n              <Text key={dir} color=\"gray\">\n                • {dir}\n              </Text>\n            ))}\n          </Box>\n        </Box>\n      )}\n    </Box>\n  )\n}\n"], "mappings": "AAAA,SAAS,KAAK,YAAY;AAC1B,OAAO,aAAa;AA2BR,cAMF,YANE;AAlBL,MAAM,iBAAgD,CAAC,EAAE,YAAY,UAAU,MAAM;AAC1F,QAAM,iBAAiB,MAAM,KAAK,WAAW,KAAK,CAAC;AAGnD,MAAI,YAAY;AAChB,QAAM,iBAAyC,CAAC;AAEhD,aAAW,CAAC,UAAU,MAAM,KAAK,YAAY;AAC3C,UAAM,oBAAoB,OAAO,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,MAAM,QAAQ,CAAC;AAC3E,mBAAe,QAAQ,IAAI;AAC3B,iBAAa;AAAA,EACf;AAEA,SACE,qBAAC,OAAI,eAAc,UAAS,cAAc,GACxC;AAAA,wBAAC,OAAI,KAAK,GACP,uBACC,oBAAC,QAAK,OAAM,QACV,8BAAC,WAAQ,MAAK,QAAO,GACvB,GAEJ;AAAA,IACC,CAAC,aAAa,eAAe,SAAS,KACrC,qBAAC,OAAI,eAAc,UAAS,YAAY,GAAG,WAAW,GACpD;AAAA,2BAAC,QAAK;AAAA;AAAA,QACG;AAAA,QAAU;AAAA,QAAW,eAAe;AAAA,QAAO;AAAA,SACpD;AAAA,MACA,oBAAC,OAAI,eAAc,UAAS,WAAW,GACpC,iBAAO,QAAQ,cAAc,EAAE,IAAI,CAAC,CAAC,UAAU,KAAK,MACnD,qBAAC,QAAoB,OAAM,QAAO;AAAA;AAAA,QAC7B;AAAA,QAAS;AAAA,QAAG;AAAA,QAAM;AAAA,WADZ,QAEX,CACD,GACH;AAAA,MACA,qBAAC,OAAI,eAAc,UAAS,WAAW,GACrC;AAAA,4BAAC,QAAK,OAAM,OAAM,0BAAY;AAAA,QAC7B;AAAA,UACC,GAAG,IAAI;AAAA,YACL,MAAM,KAAK,WAAW,OAAO,CAAC,EAC3B,KAAK,EACL,IAAI,OAAK,EAAE,SAAS;AAAA,UACzB;AAAA,QACF,EAAE,IAAI,SACJ,qBAAC,QAAe,OAAM,QAAO;AAAA;AAAA,UACxB;AAAA,aADM,GAEX,CACD;AAAA,SACH;AAAA,OACF;AAAA,KAEJ;AAEJ;", "names": []}