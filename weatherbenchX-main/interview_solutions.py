#!/usr/bin/env python3
"""
Gridmatic Interview Practice - SOLUTIONS
Reference solutions for the practice exercises

Study these after attempting the exercises yourself!
Focus on the patterns and approaches used.
"""

import pandas as pd
import numpy as np
import xarray as xr
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple


# =============================================================================
# SOLUTION 1: DATA CLEANING & VALIDATION
# =============================================================================

def clean_weather_data(raw_data: List[Dict]) -> pd.DataFrame:
    """
    SOLUTION: Clean messy weather station data
    
    Key patterns:
    - Convert to DataFrame early
    - Handle missing/invalid data systematically
    - Use pandas methods for efficiency
    - Validate data ranges
    """
    if not raw_data:
        return pd.DataFrame()
    
    # Convert to DataFrame
    df = pd.DataFrame(raw_data)
    
    # Rename columns for consistency
    if 'time' in df.columns:
        df = df.rename(columns={'time': 'timestamp'})
    
    # Convert timestamp to datetime
    df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
    
    # Remove rows with invalid timestamps
    df = df.dropna(subset=['timestamp'])
    
    # Clean temperature data
    df['temp'] = pd.to_numeric(df['temp'], errors='coerce')
    
    # Remove temperature outliers (reasonable range: -50 to 60°C)
    df = df[(df['temp'] >= -50) & (df['temp'] <= 60)]
    
    # Clean coordinates
    df['lat'] = pd.to_numeric(df['lat'], errors='coerce')
    df['lon'] = pd.to_numeric(df['lon'], errors='coerce')
    
    # Remove rows with missing coordinates
    df = df.dropna(subset=['lat', 'lon'])
    
    # Validate coordinate ranges
    df = df[(df['lat'] >= -90) & (df['lat'] <= 90)]
    df = df[(df['lon'] >= -180) & (df['lon'] <= 180)]
    
    # Remove duplicates (same station, same time)
    df = df.drop_duplicates(subset=['station', 'timestamp'])
    
    # Sort by timestamp
    df = df.sort_values('timestamp').reset_index(drop=True)
    
    return df


# =============================================================================
# SOLUTION 2: TIME SERIES ANALYSIS
# =============================================================================

def calculate_daily_statistics(df: pd.DataFrame) -> pd.DataFrame:
    """
    SOLUTION: Calculate daily weather statistics
    
    Key patterns:
    - Use pandas groupby with date extraction
    - Multiple aggregations in one operation
    - Handle missing data appropriately
    """
    if df.empty:
        return pd.DataFrame()
    
    # Ensure timestamp is datetime
    df = df.copy()
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    
    # Extract date for grouping
    df['date'] = df['timestamp'].dt.date
    
    # Group by date and calculate statistics
    daily_stats = df.groupby('date').agg({
        'temperature': ['mean', 'min', 'max', 'count', 'std']
    }).round(2)
    
    # Flatten column names
    daily_stats.columns = ['temp_mean', 'temp_min', 'temp_max', 'readings_count', 'temp_std']
    
    # Calculate temperature range
    daily_stats['temp_range'] = daily_stats['temp_max'] - daily_stats['temp_min']
    
    # Reset index to make date a column
    daily_stats = daily_stats.reset_index()
    
    return daily_stats


# =============================================================================
# SOLUTION 3: GEOSPATIAL FILTERING
# =============================================================================

def filter_stations_in_region(stations: List[Dict], bounds: Dict) -> List[Dict]:
    """
    SOLUTION: Filter weather stations within geographic bounds
    
    Key patterns:
    - Validate input data
    - Use list comprehension for filtering
    - Handle missing data gracefully
    """
    if not stations or not bounds:
        return []
    
    required_bounds = ['north', 'south', 'east', 'west']
    if not all(key in bounds for key in required_bounds):
        return []
    
    filtered_stations = []
    
    for station in stations:
        # Check if station has required coordinates
        if 'lat' not in station or 'lon' not in station:
            continue
        
        lat, lon = station['lat'], station['lon']
        
        # Skip if coordinates are None or invalid
        if lat is None or lon is None:
            continue
        
        # Check if station is within bounds
        if (bounds['south'] <= lat <= bounds['north'] and
            bounds['west'] <= lon <= bounds['east']):
            filtered_stations.append(station)
    
    return filtered_stations


# =============================================================================
# SOLUTION 4: DATA AGGREGATION
# =============================================================================

def aggregate_by_month(df: pd.DataFrame) -> Dict[str, float]:
    """
    SOLUTION: Aggregate temperature data by month
    
    Key patterns:
    - Extract time components for grouping
    - Calculate multiple statistics
    - Find extremes using idxmax/idxmin
    """
    if df.empty or 'temperature' not in df.columns:
        return {}
    
    # Ensure date column is datetime
    df = df.copy()
    if 'date' in df.columns:
        df['date'] = pd.to_datetime(df['date'])
    else:
        return {}
    
    # Extract month
    df['month'] = df['date'].dt.month
    
    # Calculate monthly statistics
    monthly_stats = df.groupby('month')['temperature'].agg(['mean', 'std']).round(2)
    
    # Find hottest and coldest months
    hottest_month = monthly_stats['mean'].idxmax()
    coldest_month = monthly_stats['mean'].idxmin()
    
    # Find month with highest variation
    highest_variation_month = monthly_stats['std'].idxmax()
    
    results = {
        'average_temp': float(df['temperature'].mean()),
        'hottest_month': int(hottest_month),
        'hottest_month_temp': float(monthly_stats.loc[hottest_month, 'mean']),
        'coldest_month': int(coldest_month),
        'coldest_month_temp': float(monthly_stats.loc[coldest_month, 'mean']),
        'highest_variation_month': int(highest_variation_month),
        'highest_variation_std': float(monthly_stats.loc[highest_variation_month, 'std'])
    }
    
    return results


# =============================================================================
# SOLUTION 5: XARRAY OPERATIONS
# =============================================================================

def find_temperature_hotspots(ds: xr.Dataset, threshold: float) -> List[Tuple[float, float]]:
    """
    SOLUTION: Find geographic locations with temperatures above threshold
    
    Key patterns:
    - Use xarray's max() along time dimension
    - Apply boolean indexing
    - Extract coordinates using where()
    """
    if 'temperature' not in ds.data_vars:
        return []
    
    # Find maximum temperature at each location across all times
    max_temps = ds['temperature'].max(dim='time')
    
    # Find locations where max temperature exceeds threshold
    hotspot_mask = max_temps > threshold
    
    # Get coordinates of hotspots
    hotspot_coords = ds.where(hotspot_mask, drop=True)
    
    # Extract lat/lon pairs
    hotspots = []
    for lat in hotspot_coords.lat.values:
        for lon in hotspot_coords.lon.values:
            if not np.isnan(hotspot_coords.sel(lat=lat, lon=lon)['temperature'].max().values):
                hotspots.append((float(lat), float(lon)))
    
    return hotspots


# =============================================================================
# BONUS SOLUTION: STRING PROCESSING
# =============================================================================

def parse_weather_codes(weather_strings: List[str]) -> Dict[str, int]:
    """
    BONUS SOLUTION: Parse weather condition codes
    
    Key patterns:
    - Use string split() method
    - Handle malformed strings gracefully
    - Use dictionary for counting
    """
    condition_counts = {}
    
    for weather_str in weather_strings:
        try:
            # Split by underscore and get first part (condition)
            parts = weather_str.split('_')
            if parts:
                condition = parts[0]
                condition_counts[condition] = condition_counts.get(condition, 0) + 1
        except Exception:
            # Skip malformed strings
            continue
    
    return condition_counts


# =============================================================================
# ADVANCED PATTERNS & TIPS
# =============================================================================

def advanced_data_processing_patterns():
    """
    Advanced patterns you might encounter in interviews
    """
    
    # Pattern 1: Method chaining for clean code
    def process_weather_chain(df):
        return (df
                .dropna(subset=['temp'])
                .query('temp >= -50 and temp <= 60')
                .groupby('station')
                .resample('D', on='timestamp')
                .agg({'temp': ['mean', 'min', 'max']})
                .round(2))
    
    # Pattern 2: Error handling with context managers
    def safe_file_processing(filename):
        try:
            with open(filename, 'r') as f:
                data = f.read()
            return process_data(data)
        except FileNotFoundError:
            print(f"File {filename} not found")
            return None
        except Exception as e:
            print(f"Error processing {filename}: {e}")
            return None
    
    # Pattern 3: Generator for memory efficiency
    def process_large_dataset(data_chunks):
        for chunk in data_chunks:
            processed_chunk = chunk.dropna().query('temp > 0')
            if not processed_chunk.empty:
                yield processed_chunk
    
    # Pattern 4: Vectorized operations
    def calculate_heat_index(temp_c, humidity):
        """Vectorized calculation using numpy"""
        temp_f = temp_c * 9/5 + 32
        hi = (temp_f + humidity) / 2  # Simplified formula
        return np.where(temp_f > 80, hi, temp_f)


# =============================================================================
# INTERVIEW TIPS
# =============================================================================

"""
KEY INTERVIEW SUCCESS PATTERNS:

1. START SIMPLE, THEN OPTIMIZE
   - Get a working solution first
   - Add error handling and edge cases
   - Optimize for performance if time permits

2. THINK OUT LOUD
   - Explain your approach before coding
   - Mention edge cases you're considering
   - Ask clarifying questions

3. COMMON PATTERNS TO PRACTICE
   - DataFrame operations: groupby, merge, pivot
   - Time series: resampling, rolling windows
   - Data cleaning: dropna, fillna, query
   - Xarray: sel, where, groupby, resample

4. ERROR HANDLING PRIORITIES
   - Check for empty/None inputs first
   - Validate data types and ranges
   - Handle missing data appropriately
   - Use try/except for file operations

5. CODE STRUCTURE
   - Use descriptive variable names
   - Add docstrings for complex functions
   - Break complex operations into steps
   - Return early for edge cases

6. TESTING YOUR CODE
   - Create simple test cases
   - Check edge cases (empty data, single row)
   - Verify output format and types
   - Print intermediate results to debug
"""

if __name__ == "__main__":
    print("GRIDMATIC INTERVIEW SOLUTIONS")
    print("=" * 40)
    print("Study these patterns and practice the exercises!")
    print("Focus on clean code structure and error handling.")
