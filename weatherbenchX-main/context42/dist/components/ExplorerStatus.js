import { Box, Text } from "ink";
import Spinner from "ink-spinner";
import { jsx, jsxs } from "react/jsx-runtime";
const ExplorerStatus = ({ fileGroups, isLoading }) => {
  const foundLanguages = Array.from(fileGroups.keys());
  let fileCount = 0;
  const languageCounts = {};
  for (const [language, groups] of fileGroups) {
    const languageFileCount = groups.reduce((sum, g) => sum + g.files.length, 0);
    languageCounts[language] = languageFileCount;
    fileCount += languageFileCount;
  }
  return /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [
    /* @__PURE__ */ jsx(Box, { gap: 1, children: isLoading && /* @__PURE__ */ jsx(Text, { color: "cyan", children: /* @__PURE__ */ jsx(Spinner, { type: "dots" }) }) }),
    !isLoading && foundLanguages.length > 0 && /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginLeft: 2, marginTop: 1, children: [
      /* @__PURE__ */ jsxs(Text, { children: [
        "Found ",
        fileCount,
        " files in ",
        foundLanguages.length,
        " languages"
      ] }),
      /* @__PURE__ */ jsx(Box, { flexDirection: "column", marginTop: 1, children: Object.entries(languageCounts).map(([language, count]) => /* @__PURE__ */ jsxs(Text, { color: "gray", children: [
        "\u2022 ",
        language,
        ": ",
        count,
        " files"
      ] }, language)) }),
      /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginTop: 1, children: [
        /* @__PURE__ */ jsx(Text, { color: "dim", children: "Directories:" }),
        [
          ...new Set(
            Array.from(fileGroups.values()).flat().map((g) => g.directory)
          )
        ].map((dir) => /* @__PURE__ */ jsxs(Text, { color: "gray", children: [
          "\u2022 ",
          dir
        ] }, dir))
      ] })
    ] })
  ] });
};
export {
  ExplorerStatus
};
//# sourceMappingURL=ExplorerStatus.js.map
