#!/usr/bin/env python3
"""
WeatherBench-X Interview Tutorial: Key Components for 30-Minute Coding Interview

This tutorial covers the core WeatherBench-X patterns you might encounter:
1. Aggregation system (weighted statistics)
2. Binning strategies (spatial/temporal grouping)
3. Interpolation methods (grid-to-point alignment)
4. Metrics computation (evaluation scores)

Focus: Understanding the data flow and key abstractions
"""

import numpy as np
import pandas as pd
import xarray as xr
from typing import Dict, List, Mapping, Optional, Sequence
from abc import ABC, abstractmethod
import dataclasses


# =============================================================================
# 1. AGGREGATION SYSTEM - Core Pattern for Weighted Statistics
# =============================================================================

@dataclasses.dataclass
class AggregationState:
    """Container for weighted statistics that can be combined and normalized.
    
    Key concept: Separate sum_weighted_statistics and sum_weights allows
    combining results from different chunks before computing final means.
    """
    sum_weighted_statistics: Dict
    sum_weights: Dict
    
    def __add__(self, other: 'AggregationState') -> 'AggregationState':
        """Combine two aggregation states - key for distributed processing"""
        # In real implementation, this recursively adds nested dictionaries
        combined_stats = {}
        combined_weights = {}
        
        # Simplified version - real code handles nested dict structures
        for key in self.sum_weighted_statistics:
            combined_stats[key] = (self.sum_weighted_statistics[key] + 
                                 other.sum_weighted_statistics[key])
            combined_weights[key] = (self.sum_weights[key] + 
                                   other.sum_weights[key])
        
        return AggregationState(combined_stats, combined_weights)
    
    def mean_statistics(self) -> Dict:
        """Compute final weighted means: sum_weighted_stats / sum_weights"""
        means = {}
        for key in self.sum_weighted_statistics:
            means[key] = self.sum_weighted_statistics[key] / self.sum_weights[key]
        return means


class Aggregator:
    """Drives the reduction from per-point statistics to aggregated results.
    
    Key pattern: Composable aggregation through binning and weighting
    """
    
    def __init__(self, 
                 reduce_dims: List[str],
                 bin_by: Optional[List['Binning']] = None,
                 weigh_by: Optional[List['Weighting']] = None,
                 masked: bool = False,
                 skipna: bool = False):
        """
        Args:
            reduce_dims: Dimensions to collapse (e.g., ['time', 'lat', 'lon'])
            bin_by: List of binning strategies (regions, time periods, etc.)
            weigh_by: List of weighting schemes (area weighting, etc.)
            masked: Whether to use mask coordinate
            skipna: How to handle NaN values
        """
        self.reduce_dims = reduce_dims
        self.bin_by = bin_by or []
        self.weigh_by = weigh_by or []
        self.masked = masked
        self.skipna = skipna
    
    def aggregation_fn(self, stat: xr.DataArray) -> xr.DataArray:
        """Core aggregation: xr.dot(stat, weights, bin_masks, dims=reduce_dims)
        
        This is the heart of WeatherBench-X aggregation:
        - Applies weights (e.g., grid area weighting)
        - Applies bin masks (e.g., regional masks)
        - Reduces over specified dimensions
        """
        # Get weights from weighting objects
        weights = []
        for weighting in self.weigh_by:
            weights.append(weighting.weights(stat))
        
        # Get bin masks from binning objects
        bin_masks = []
        for binning in self.bin_by:
            bin_masks.append(binning.create_bin_mask(stat))
        
        # The magic: xr.dot handles broadcasting and reduction
        return xr.dot(stat, *weights, *bin_masks, dims=self.reduce_dims)
    
    def aggregate_statistics(self, statistics: Dict) -> AggregationState:
        """Walk nested dict {statistic → {var → DataArray}} and aggregate each"""
        sum_weighted_stats = {}
        sum_weights = {}
        
        for stat_name, var_dict in statistics.items():
            sum_weighted_stats[stat_name] = {}
            sum_weights[stat_name] = {}
            
            for var_name, data_array in var_dict.items():
                # Apply aggregation function
                weighted_sum = self.aggregation_fn(data_array)
                
                # For weights, aggregate ones with same structure
                ones = xr.ones_like(data_array)
                weight_sum = self.aggregation_fn(ones)
                
                sum_weighted_stats[stat_name][var_name] = weighted_sum
                sum_weights[stat_name][var_name] = weight_sum
        
        return AggregationState(sum_weighted_stats, sum_weights)


# =============================================================================
# 2. BINNING SYSTEM - Spatial/Temporal Grouping Strategies
# =============================================================================

class Binning(ABC):
    """Base class for all binning strategies"""
    
    def __init__(self, bin_dim_name: str):
        self.bin_dim_name = bin_dim_name
    
    @abstractmethod
    def create_bin_mask(self, statistic: xr.DataArray) -> xr.DataArray:
        """Create boolean mask with new bin dimension"""
        pass


class Regions(Binning):
    """Bin by geographic regions - most common spatial binning"""
    
    def __init__(self, regions: Dict[str, tuple], bin_dim_name: str = 'region'):
        """
        Args:
            regions: {'region_name': ((lat_min, lat_max), (lon_min, lon_max))}
        """
        super().__init__(bin_dim_name)
        self.regions = regions
    
    def create_bin_mask(self, statistic: xr.DataArray) -> xr.DataArray:
        """Create mask for each region"""
        masks = []
        region_names = []
        
        for region_name, ((lat_min, lat_max), (lon_min, lon_max)) in self.regions.items():
            # Create boolean mask for this region
            lat_mask = (statistic.lat >= lat_min) & (statistic.lat <= lat_max)
            lon_mask = (statistic.lon >= lon_min) & (statistic.lon <= lon_max)
            region_mask = lat_mask & lon_mask
            
            masks.append(region_mask)
            region_names.append(region_name)
        
        # Stack masks along new region dimension
        mask_array = xr.concat(masks, dim=self.bin_dim_name)
        mask_array[self.bin_dim_name] = region_names
        
        return mask_array


class ByExactCoord(Binning):
    """One bin per unique coordinate value - great for lead_time binning"""
    
    def __init__(self, coord: str):
        super().__init__(coord)
        self.coord = coord
    
    def create_bin_mask(self, statistic: xr.DataArray) -> xr.DataArray:
        """Create mask for each unique coordinate value"""
        coord_values = statistic[self.coord].values
        unique_values = np.unique(coord_values)
        
        masks = []
        for value in unique_values:
            mask = statistic[self.coord] == value
            masks.append(mask)
        
        mask_array = xr.concat(masks, dim=self.bin_dim_name)
        mask_array[self.bin_dim_name] = unique_values
        
        return mask_array


class ByTimeUnit(Binning):
    """Bin by time units (hour, month, season) - temporal aggregation"""
    
    def __init__(self, time_dim: str, unit: str, bin_dim_name: str = None):
        """
        Args:
            time_dim: Name of time dimension
            unit: 'hour', 'month', 'season', etc.
        """
        super().__init__(bin_dim_name or unit)
        self.time_dim = time_dim
        self.unit = unit
    
    def create_bin_mask(self, statistic: xr.DataArray) -> xr.DataArray:
        """Create mask for each time unit"""
        time_coord = statistic[self.time_dim]
        
        if self.unit == 'hour':
            time_values = time_coord.dt.hour
            unique_values = np.arange(24)
        elif self.unit == 'month':
            time_values = time_coord.dt.month
            unique_values = np.arange(1, 13)
        elif self.unit == 'season':
            time_values = time_coord.dt.season
            unique_values = ['DJF', 'MAM', 'JJA', 'SON']
        else:
            raise ValueError(f"Unsupported time unit: {self.unit}")
        
        masks = []
        for value in unique_values:
            mask = time_values == value
            masks.append(mask)
        
        mask_array = xr.concat(masks, dim=self.bin_dim_name)
        mask_array[self.bin_dim_name] = unique_values
        
        return mask_array


# =============================================================================
# 3. WEIGHTING SYSTEM - Proper Statistical Weighting
# =============================================================================

class Weighting(ABC):
    """Base class for weighting schemes"""
    
    @abstractmethod
    def weights(self, statistic: xr.DataArray) -> xr.DataArray:
        """Return weights that broadcast against statistic"""
        pass


class GridAreaWeighting(Weighting):
    """Weight by grid cell area - essential for global statistics"""
    
    def __init__(self, latitude_name: str = 'lat'):
        self.latitude_name = latitude_name
    
    def weights(self, statistic: xr.DataArray) -> xr.DataArray:
        """Compute area weights proportional to cos(latitude)"""
        if self.latitude_name not in statistic.dims:
            return xr.DataArray(1.0)  # No weighting needed
        
        lat = statistic[self.latitude_name]
        # Area weight proportional to cos(latitude)
        weights = np.cos(np.deg2rad(lat))
        
        # Normalize to mean of 1
        weights = weights / weights.mean()
        
        return weights


# =============================================================================
# 4. METRICS SYSTEM - Evaluation Scores
# =============================================================================

class Metric(ABC):
    """Base class for evaluation metrics"""
    
    @abstractmethod
    def wanted_statistics(self) -> List[str]:
        """Return list of statistics this metric needs"""
        pass
    
    @abstractmethod
    def evaluate_from_statistics(self, statistics: Dict) -> xr.DataArray:
        """Compute metric from pre-computed statistics"""
        pass


class RMSE(Metric):
    """Root Mean Square Error"""
    
    def wanted_statistics(self) -> List[str]:
        return ['squared_error']
    
    def evaluate_from_statistics(self, statistics: Dict) -> xr.DataArray:
        return np.sqrt(statistics['squared_error'])


class MAE(Metric):
    """Mean Absolute Error"""
    
    def wanted_statistics(self) -> List[str]:
        return ['absolute_error']
    
    def evaluate_from_statistics(self, statistics: Dict) -> xr.DataArray:
        return statistics['absolute_error']


def compute_basic_statistics(predictions: xr.DataArray, 
                           targets: xr.DataArray) -> Dict[str, xr.DataArray]:
    """Compute basic statistics needed by metrics"""
    error = predictions - targets
    
    return {
        'squared_error': error ** 2,
        'absolute_error': np.abs(error),
        'error': error
    }


# =============================================================================
# 5. COMPLETE WORKFLOW EXAMPLE
# =============================================================================

def weatherbench_evaluation_example():
    """Complete WeatherBench-X evaluation workflow"""
    
    # 1. Create sample data
    time = pd.date_range('2024-01-01', periods=100, freq='6H')
    lat = np.linspace(-90, 90, 37)
    lon = np.linspace(0, 360, 72)
    
    # Predictions and targets
    predictions = xr.DataArray(
        np.random.normal(280, 10, (100, 37, 72)),
        dims=['time', 'lat', 'lon'],
        coords={'time': time, 'lat': lat, 'lon': lon}
    )
    
    targets = predictions + xr.DataArray(
        np.random.normal(0, 2, (100, 37, 72)),
        dims=['time', 'lat', 'lon']
    )
    
    # 2. Compute basic statistics
    statistics = {
        'temperature': compute_basic_statistics(predictions, targets)
    }
    
    # 3. Set up aggregation
    regions = {
        'global': ((-90, 90), (0, 360)),
        'tropics': ((-30, 30), (0, 360)),
        'northern_hemisphere': ((0, 90), (0, 360))
    }
    
    aggregator = Aggregator(
        reduce_dims=['time', 'lat', 'lon'],
        bin_by=[Regions(regions)],
        weigh_by=[GridAreaWeighting()],
        skipna=True
    )
    
    # 4. Aggregate statistics
    agg_state = aggregator.aggregate_statistics(statistics)
    
    # 5. Compute final metrics
    metrics = {'rmse': RMSE(), 'mae': MAE()}
    
    final_scores = {}
    mean_stats = agg_state.mean_statistics()
    
    for metric_name, metric in metrics.items():
        for var_name in mean_stats:
            var_stats = mean_stats[var_name]
            score = metric.evaluate_from_statistics(var_stats)
            final_scores[f'{metric_name}.{var_name}'] = score
    
    return final_scores


if __name__ == "__main__":
    print("WeatherBench-X Interview Tutorial")
    print("=" * 50)
    
    # Run example
    scores = weatherbench_evaluation_example()
    
    print("Final evaluation scores:")
    for metric_name, score in scores.items():
        print(f"{metric_name}: {score}")
    
    print("\nKey concepts demonstrated:")
    print("1. AggregationState: Weighted statistics with combinable state")
    print("2. Binning: Regional and temporal grouping strategies")
    print("3. Weighting: Grid area weighting for proper global statistics")
    print("4. Metrics: Modular evaluation score computation")
    print("5. Workflow: Complete evaluation pipeline")
