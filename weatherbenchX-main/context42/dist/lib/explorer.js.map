{"version": 3, "sources": ["../../src/lib/explorer.ts"], "sourcesContent": ["import { stat } from \"node:fs/promises\"\nimport { dirname, extname, join, relative } from \"node:path\"\nimport { globby } from \"globby\"\nimport type { ExplorerOptions, FileGroup, Language } from \"./types.js\"\n\nconst DEFAULT_IGNORE = [\n  \"**/node_modules/**\",\n  \"**/.git/**\",\n  \"**/dist/**\",\n  \"**/build/**\",\n  \"**/coverage/**\",\n  \"**/.next/**\",\n  \"**/.nuxt/**\",\n  \"**/venv/**\",\n  \"**/__pycache__/**\",\n  \"**/*.min.js\",\n  \"**/*.min.css\",\n  \"**/vendor/**\",\n  \"**/.cache/**\",\n  \"**/tmp/**\",\n  \"**/temp/**\",\n]\n\nexport const explorer = async (options: ExplorerOptions): Promise<Map<Language, FileGroup[]>> => {\n  const ignore = [...DEFAULT_IGNORE, ...(options.ignore ?? [])]\n\n  // Find all files in the directory\n  const pattern = join(options.directory, \"**/*\")\n  const allFiles = await globby(pattern, {\n    ignore,\n    gitignore: true,\n    absolute: true,\n    onlyFiles: true,\n  })\n\n  // Group files by extension\n  const filesByExtension = new Map<string, string[]>()\n\n  for (const file of allFiles) {\n    // Skip files that are too large\n    const { size } = await stat(file)\n    if (size > 512 * 1024) continue // Skip files larger than 0.5MB\n\n    // Get the extension (without the dot)\n    const ext = extname(file).slice(1).toLowerCase()\n    if (!ext) continue // Skip files without extensions\n\n    if (!filesByExtension.has(ext)) {\n      filesByExtension.set(ext, [])\n    }\n    filesByExtension.get(ext)!.push(file)\n  }\n\n  // Now group by directory within each extension\n  const result = new Map<Language, FileGroup[]>()\n\n  for (const [extension, files] of filesByExtension) {\n    const extensionFileGroups: FileGroup[] = []\n\n    // Group files by their parent directory\n    const filesByDirectory = new Map<string, string[]>()\n\n    for (const file of files) {\n      const directory = dirname(file)\n      if (!filesByDirectory.has(directory)) {\n        filesByDirectory.set(directory, [])\n      }\n      filesByDirectory.get(directory)?.push(file)\n    }\n\n    // Create a FileGroup for each directory\n    for (const [directory, dirFiles] of filesByDirectory) {\n      extensionFileGroups.push({\n        directory,\n        language: extension, // The extension IS the language\n        files: Object.freeze(dirFiles) as readonly string[],\n      })\n    }\n\n    result.set(extension, extensionFileGroups)\n  }\n\n  return toposort(result)\n}\n\nexport const getDirectoriesForProcessing = (fileGroupsMap: Map<Language, FileGroup[]>): string[] => {\n  const allDirectories = new Set<string>()\n\n  for (const fileGroups of fileGroupsMap.values()) {\n    for (const group of fileGroups) {\n      allDirectories.add(group.directory)\n    }\n  }\n\n  // Sort directories by depth (process parent directories first)\n  return Array.from(allDirectories).sort((a, b) => {\n    const depthA = a.split(\"/\").length\n    const depthB = b.split(\"/\").length\n    if (depthA !== depthB) return depthA - depthB\n    return a.localeCompare(b)\n  })\n}\n\nexport const getFilesForDirectory = (\n  fileGroupsMap: Map<Language, FileGroup[]>,\n  directory: string,\n): Map<string, string[]> => {\n  const result = new Map<string, string[]>()\n\n  for (const [language, fileGroups] of fileGroupsMap) {\n    for (const group of fileGroups) {\n      const filesInDir = group.files.filter(file => {\n        const fileDir = dirname(file)\n        const relativeDir = relative(process.cwd(), fileDir) || \".\"\n        return relativeDir === directory\n      })\n\n      if (filesInDir.length > 0) {\n        result.set(language, filesInDir)\n      }\n    }\n  }\n\n  return result\n}\n\nexport const getSupportedLanguages = (): readonly Language[] => {\n  // This function is no longer meaningful since we support any extension\n  // Return empty array to maintain compatibility\n  return []\n}\n\n// Helper function to check if one directory is a child of another\nconst isChildDirectory = (child: string, parent: string): boolean => {\n  if (child === parent) return false\n  const childParts = child.split(\"/\")\n  const parentParts = parent.split(\"/\")\n\n  if (childParts.length <= parentParts.length) return false\n\n  for (let i = 0; i < parentParts.length; i++) {\n    if (childParts[i] !== parentParts[i]) return false\n  }\n\n  return true\n}\n\n// Topological sort for FileGroups to ensure child directories are processed before parents\nexport const toposort = (fileGroups: Map<Language, FileGroup[]>): Map<Language, FileGroup[]> => {\n  const result = new Map<Language, FileGroup[]>()\n\n  // Process each language separately\n  for (const [language, groups] of fileGroups) {\n    if (groups.length === 0) {\n      result.set(language, [])\n      continue\n    }\n\n    // Build adjacency list (parent -> children)\n    const adjacency = new Map<string, Set<string>>()\n    const inDegree = new Map<string, number>()\n    const directories = groups.map(g => g.directory)\n\n    // Initialize\n    for (const dir of directories) {\n      adjacency.set(dir, new Set())\n      inDegree.set(dir, 0)\n    }\n\n    // Build edges (parent depends on children, so children must come first)\n    for (const parent of directories) {\n      for (const child of directories) {\n        if (isChildDirectory(child, parent)) {\n          // Check if it's a direct child (no intermediate directories)\n          const isDirectChild = !directories.some(\n            d => d !== child && d !== parent && isChildDirectory(child, d) && isChildDirectory(d, parent),\n          )\n\n          if (isDirectChild) {\n            adjacency.get(parent)?.add(child)\n            inDegree.set(child, inDegree.get(child)! + 1)\n          }\n        }\n      }\n    }\n\n    // Topological sort using DFS\n    const visited = new Set<string>()\n    const sorted: string[] = []\n\n    const dfs = (dir: string) => {\n      if (visited.has(dir)) return\n      visited.add(dir)\n\n      // Visit all children first\n      const children = Array.from(adjacency.get(dir)!).sort()\n      for (const child of children) {\n        dfs(child)\n      }\n\n      sorted.push(dir)\n    }\n\n    // Start DFS from roots (directories with no parents)\n    const roots = directories\n      .filter(d => {\n        // A root has no parent directory in our list\n        return !directories.some(other => other !== d && isChildDirectory(d, other))\n      })\n      .sort()\n\n    for (const root of roots) {\n      dfs(root)\n    }\n\n    // Create sorted FileGroup array\n    const sortedGroups = sorted.map(dir => groups.find(g => g.directory === dir)!).filter(Boolean)\n    result.set(language, sortedGroups)\n  }\n\n  return result\n}\n"], "mappings": "AAAA,SAAS,YAAY;AACrB,SAAS,SAAS,SAAS,MAAM,gBAAgB;AACjD,SAAS,cAAc;AAGvB,MAAM,iBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEO,MAAM,WAAW,OAAO,YAAkE;AAC/F,QAAM,SAAS,CAAC,GAAG,gBAAgB,GAAI,QAAQ,UAAU,CAAC,CAAE;AAG5D,QAAM,UAAU,KAAK,QAAQ,WAAW,MAAM;AAC9C,QAAM,WAAW,MAAM,OAAO,SAAS;AAAA,IACrC;AAAA,IACA,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,EACb,CAAC;AAGD,QAAM,mBAAmB,oBAAI,IAAsB;AAEnD,aAAW,QAAQ,UAAU;AAE3B,UAAM,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI;AAChC,QAAI,OAAO,MAAM,KAAM;AAGvB,UAAM,MAAM,QAAQ,IAAI,EAAE,MAAM,CAAC,EAAE,YAAY;AAC/C,QAAI,CAAC,IAAK;AAEV,QAAI,CAAC,iBAAiB,IAAI,GAAG,GAAG;AAC9B,uBAAiB,IAAI,KAAK,CAAC,CAAC;AAAA,IAC9B;AACA,qBAAiB,IAAI,GAAG,EAAG,KAAK,IAAI;AAAA,EACtC;AAGA,QAAM,SAAS,oBAAI,IAA2B;AAE9C,aAAW,CAAC,WAAW,KAAK,KAAK,kBAAkB;AACjD,UAAM,sBAAmC,CAAC;AAG1C,UAAM,mBAAmB,oBAAI,IAAsB;AAEnD,eAAW,QAAQ,OAAO;AACxB,YAAM,YAAY,QAAQ,IAAI;AAC9B,UAAI,CAAC,iBAAiB,IAAI,SAAS,GAAG;AACpC,yBAAiB,IAAI,WAAW,CAAC,CAAC;AAAA,MACpC;AACA,uBAAiB,IAAI,SAAS,GAAG,KAAK,IAAI;AAAA,IAC5C;AAGA,eAAW,CAAC,WAAW,QAAQ,KAAK,kBAAkB;AACpD,0BAAoB,KAAK;AAAA,QACvB;AAAA,QACA,UAAU;AAAA;AAAA,QACV,OAAO,OAAO,OAAO,QAAQ;AAAA,MAC/B,CAAC;AAAA,IACH;AAEA,WAAO,IAAI,WAAW,mBAAmB;AAAA,EAC3C;AAEA,SAAO,SAAS,MAAM;AACxB;AAEO,MAAM,8BAA8B,CAAC,kBAAwD;AAClG,QAAM,iBAAiB,oBAAI,IAAY;AAEvC,aAAW,cAAc,cAAc,OAAO,GAAG;AAC/C,eAAW,SAAS,YAAY;AAC9B,qBAAe,IAAI,MAAM,SAAS;AAAA,IACpC;AAAA,EACF;AAGA,SAAO,MAAM,KAAK,cAAc,EAAE,KAAK,CAAC,GAAG,MAAM;AAC/C,UAAM,SAAS,EAAE,MAAM,GAAG,EAAE;AAC5B,UAAM,SAAS,EAAE,MAAM,GAAG,EAAE;AAC5B,QAAI,WAAW,OAAQ,QAAO,SAAS;AACvC,WAAO,EAAE,cAAc,CAAC;AAAA,EAC1B,CAAC;AACH;AAEO,MAAM,uBAAuB,CAClC,eACA,cAC0B;AAC1B,QAAM,SAAS,oBAAI,IAAsB;AAEzC,aAAW,CAAC,UAAU,UAAU,KAAK,eAAe;AAClD,eAAW,SAAS,YAAY;AAC9B,YAAM,aAAa,MAAM,MAAM,OAAO,UAAQ;AAC5C,cAAM,UAAU,QAAQ,IAAI;AAC5B,cAAM,cAAc,SAAS,QAAQ,IAAI,GAAG,OAAO,KAAK;AACxD,eAAO,gBAAgB;AAAA,MACzB,CAAC;AAED,UAAI,WAAW,SAAS,GAAG;AACzB,eAAO,IAAI,UAAU,UAAU;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEO,MAAM,wBAAwB,MAA2B;AAG9D,SAAO,CAAC;AACV;AAGA,MAAM,mBAAmB,CAAC,OAAe,WAA4B;AACnE,MAAI,UAAU,OAAQ,QAAO;AAC7B,QAAM,aAAa,MAAM,MAAM,GAAG;AAClC,QAAM,cAAc,OAAO,MAAM,GAAG;AAEpC,MAAI,WAAW,UAAU,YAAY,OAAQ,QAAO;AAEpD,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,QAAI,WAAW,CAAC,MAAM,YAAY,CAAC,EAAG,QAAO;AAAA,EAC/C;AAEA,SAAO;AACT;AAGO,MAAM,WAAW,CAAC,eAAuE;AAC9F,QAAM,SAAS,oBAAI,IAA2B;AAG9C,aAAW,CAAC,UAAU,MAAM,KAAK,YAAY;AAC3C,QAAI,OAAO,WAAW,GAAG;AACvB,aAAO,IAAI,UAAU,CAAC,CAAC;AACvB;AAAA,IACF;AAGA,UAAM,YAAY,oBAAI,IAAyB;AAC/C,UAAM,WAAW,oBAAI,IAAoB;AACzC,UAAM,cAAc,OAAO,IAAI,OAAK,EAAE,SAAS;AAG/C,eAAW,OAAO,aAAa;AAC7B,gBAAU,IAAI,KAAK,oBAAI,IAAI,CAAC;AAC5B,eAAS,IAAI,KAAK,CAAC;AAAA,IACrB;AAGA,eAAW,UAAU,aAAa;AAChC,iBAAW,SAAS,aAAa;AAC/B,YAAI,iBAAiB,OAAO,MAAM,GAAG;AAEnC,gBAAM,gBAAgB,CAAC,YAAY;AAAA,YACjC,OAAK,MAAM,SAAS,MAAM,UAAU,iBAAiB,OAAO,CAAC,KAAK,iBAAiB,GAAG,MAAM;AAAA,UAC9F;AAEA,cAAI,eAAe;AACjB,sBAAU,IAAI,MAAM,GAAG,IAAI,KAAK;AAChC,qBAAS,IAAI,OAAO,SAAS,IAAI,KAAK,IAAK,CAAC;AAAA,UAC9C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,UAAM,UAAU,oBAAI,IAAY;AAChC,UAAM,SAAmB,CAAC;AAE1B,UAAM,MAAM,CAAC,QAAgB;AAC3B,UAAI,QAAQ,IAAI,GAAG,EAAG;AACtB,cAAQ,IAAI,GAAG;AAGf,YAAM,WAAW,MAAM,KAAK,UAAU,IAAI,GAAG,CAAE,EAAE,KAAK;AACtD,iBAAW,SAAS,UAAU;AAC5B,YAAI,KAAK;AAAA,MACX;AAEA,aAAO,KAAK,GAAG;AAAA,IACjB;AAGA,UAAM,QAAQ,YACX,OAAO,OAAK;AAEX,aAAO,CAAC,YAAY,KAAK,WAAS,UAAU,KAAK,iBAAiB,GAAG,KAAK,CAAC;AAAA,IAC7E,CAAC,EACA,KAAK;AAER,eAAW,QAAQ,OAAO;AACxB,UAAI,IAAI;AAAA,IACV;AAGA,UAAM,eAAe,OAAO,IAAI,SAAO,OAAO,KAAK,OAAK,EAAE,cAAc,GAAG,CAAE,EAAE,OAAO,OAAO;AAC7F,WAAO,IAAI,UAAU,YAAY;AAAA,EACnC;AAEA,SAAO;AACT;", "names": []}